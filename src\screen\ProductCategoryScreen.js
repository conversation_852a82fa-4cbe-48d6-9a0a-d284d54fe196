import React, {
  Component,
  useReducer,
  useState,
  useEffect,
  use<PERSON>allback,
} from "react";
import {
  InteractionManager,
  StyleSheet,
  ScrollView,
  Image,
  View,
  Text,
  Alert,
  TouchableOpacity,
  TextInput,
  Dimensions,
  FlatList,
  Modal,
  Picker,
  PermissionsAndroid,
  ActivityIndicator,
  useWindowDimensions,
} from "react-native";
import { useLinkTo } from "@react-navigation/native";
import Colors from "../constant/Colors";
import SideBar from "./SideBar";
import * as User from "../util/User";
import AIcon from "react-native-vector-icons/AntDesign";
import Icon from "react-native-vector-icons/Feather";
import Icon1 from "react-native-vector-icons/FontAwesome";
import Icon2 from "react-native-vector-icons/EvilIcons";
import MaterialIcons from "react-native-vector-icons/MaterialIcons";
import Entypo from "react-native-vector-icons/Entypo";
import DropDownPicker from "react-native-dropdown-picker";
// import { ceil } from 'react-native-reanimated';
//import CheckBox from '@react-native-community/checkbox';
import DateTimePicker from "react-native-modal-datetime-picker";
import moment from "moment";
import Close from "react-native-vector-icons/AntDesign";
import ApiClient from "../util/ApiClient";
import API from "../constant/API";
import AsyncStorage from "@react-native-async-storage/async-storage";
import Styles from "../constant/Styles";
// import DocumentPicker from 'react-native-document-picker';
import Ionicons from "react-native-vector-icons/Ionicons";
//import RNFetchBlob from 'rn-fetch-blob';
//import { isTablet } from 'react-native-device-detection';
import "react-native-get-random-values";
import { customAlphabet } from "nanoid";
import { CommonStore } from "../store/commonStore";
import { MerchantStore } from "../store/merchantStore";
import {
  ORDER_TYPE,
  ORDER_TYPE_PARSED,
  PURCHASE_ORDER_STATUS,
  PURCHASE_ORDER_STATUS_PARSED,
  EXPAND_TAB_TYPE,
} from "../constant/common";
import { UserStore } from "../store/userStore";
import { OutletStore } from "../store/outletStore";
//import { KeyboardAwareFlatList, KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import AntDesign from "react-native-vector-icons/AntDesign";
import { PROMOTION_TYPE_VARIATION } from "../constant/promotions";
import FontAwesome5 from "react-native-vector-icons/FontAwesome5";
import {
  autofitColumns,
  convertArrayToCSV,
  generateEmailReport,
  getImageFromFirebaseStorage,
  getPathForFirebaseStorageFromBlob,
  toDataUrl,
  uploadFileToFirebaseStorage,
  sliceUnicodeStringV2WithDots,
} from "../util/common";
import XLSX from "xlsx";
//import { zip, unzip, unzipAssets, subscribe } from 'react-native-zip-archive'
//const RNFS = require('react-native-fs');
import "react-native-get-random-values";
import { v4 as uuidv4 } from "uuid";
import { EMAIL_REPORT_TYPE, ROLE_TYPE } from "../constant/common";
import { prefix } from "../constant/env";
import { useFilePicker } from "use-file-picker";
import Promise from "bluebird";
import JsZip from "jszip";
import FileSaver from "file-saver";
import { useFocusEffect } from "@react-navigation/native";
// import UserIdleWrapper from '../components/userIdleWrapper';

import Ionicon from "react-native-vector-icons/Ionicons";
import Switch from "react-switch";
import {
  EFFECTIVE_DAY_DROPDOWN_LIST1,
  EFFECTIVE_DAY,
} from "../constant/promotions";
import DateTimePickerModal from "react-native-modal-datetime-picker";
import APILocal from '../util/apiLocalReplacers';
import Select from "react-select";
import personicon from "../assets/image/default-profile.png";
import headerLogo from "../assets/image/logo.png";

import { ReactComponent as Edit } from "../assets/svg/Edit.svg";
import { ReactComponent as ArrowDown } from "../assets/svg/ArrowDown.svg";

import MultiSelect from "react-multiple-select-dropdown-lite";
import "react-multiple-select-dropdown-lite/dist/index.css";
import "../constant/styles.css";
import TimeKeeper from "react-timekeeper";

import { ReactComponent as Sort } from "../assets/svg/Sort.svg"
import { DragDropContext, Droppable, Draggable } from "react-beautiful-dnd";

const alphabet = "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ";
const nanoid = customAlphabet(alphabet, 12);

const ProductCategoryScreen = (props) => {
  const { navigation } = props;

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, [])
  );

  ///////////////////////////////////////////////////////////

  const linkTo = useLinkTo();

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const [
    openFileSelector,
    {
      plainFiles,
      filesContent,
      loading: loadingFileInput,
      clear: clearAllFiles,
    },
  ] = useFilePicker({
    accept: ".zip",
    readAs: "ArrayBuffer",
  });

  useEffect(() => {
    console.log(plainFiles, filesContent, loadingFileInput);

    // Upload the batch template zip to firebase when the file is finish loaded
    if (plainFiles.length && filesContent.length && !loadingFileInput) {
      importTemplateData();
    }
  }, [plainFiles, filesContent, loadingFileInput]);

  const [purchaseOrder, setPurchaseOrder] = useState(true);
  const [addStockTake, setAddStockTake] = useState(false);
  const [addPurchase, setAddPurchase] = useState(false);
  const [editPurchase, setEditPurchase] = useState(false);
  const [addStockTransfer, setAddStockTransfer] = useState(false);
  const [stockList, setStockList] = useState([]);
  const [stockTransferList, setStockTransferList] = useState([]);
  const [stockTakeList, setStockTakeList] = useState([]);
  const [orderList, setOrderList] = useState([]);
  const [itemsToOrder, setItemsToOrder] = useState([{}]);
  const [itemsToOrder2, setItemsToOrder2] = useState([{}, {}, {}]);
  const [addStockTransferList, setAddStockTransferList] = useState([
    {},
    {},
    {},
  ]);
  const [addCountedStockTakeList, setAddCountedStockTakeList] = useState([
    {},
    {},
    {},
  ]);
  const [addUnCountedStockTakeList, setAddUnCountedStockTakeList] = useState([
    {},
    {},
    {},
  ]);
  const [productList, setProductList] = useState([]);
  const [isSelected, setIsSelected] = useState(false);
  const [isSelected2, setIsSelected2] = useState(false);
  const [isSelected3, setIsSelected3] = useState(true);
  const [isSelected4, setIsSelected4] = useState(false);
  const [isDateTimePickerVisible, setIsDateTimePickerVisible] = useState(false);
  const [date, setDate] = useState(Date.now());
  const [visible, setVisible] = useState(false);
  const [Email, setEmail] = useState(null);
  const [modal, setModal] = useState(false);
  const [outletId, setOutletId] = useState(User.getOutletId());
  const [switchMerchant, setSwitchMerchant] = useState(false);
  const [search, setSearch] = useState("");
  const [search2, setSearch2] = useState("");
  const [search3, setSearch3] = useState("");
  const [ideal, setIdeal] = useState("");
  const [minimum, setMinimum] = useState("");
  const [itemId, setItemId] = useState("");
  const [choose, setChoose] = useState(null);

  const [loading, setLoading] = useState(false);
  const [choice2, setChoice2] = useState("Print P.O");
  const [choice7, setChoice7] = useState("1");
  const [choice8, setChoice8] = useState("1");
  const [choice4, setChoice4] = useState("Chicken patty");

  const [deleteModal, setDeleteModal] = useState(false);

  const [editCategory, setEditCategory] = useState(false);

  ///////////////////////////////////////////////////////////////////

  const [categoryUniqueId, setCategoryUniqueId] = useState("");
  const [categoryName, setCategoryName] = useState("");
  const [newCategoryName, setNewCategoryName] = useState("");

  ///////////////////////////////////////////////////////////////////

  // hide menu support

  const [hideInOrderTypes, setHideInOrderTypes] = useState("");

  ///////////////////////////////////////////////////////////////////

  const [supplierName, setSupplierName] = useState("");
  const [supplierEmail, setSupplierEmail] = useState("");
  const [supplierPhone, setSupplierPhone] = useState("");
  const [supplierTax, setSupplierTax] = useState("");

  const [supplierItems, setSupplierItems] = useState([
    {
      supplyItemId: "",
      name: "",
      sku: "",
      unit: "",
      price: 0,
    },
  ]);

  ///////////////////////////////////////////////////////////////////

  // 2023-02-09 - Print KD multiple times

  const [printKDNum, setPrintKDNum] = useState(1);

  ///////////////////////////////////////////////////////////////////

  const [poId, setPoId] = useState("");

  const [supplierDropdownList, setSupplierDropdownList] = useState([]);
  const [selectedSupplierId, setSelectedSupplierId] = useState("");

  const [poStatus, setPoStatus] = useState(PURCHASE_ORDER_STATUS.CREATED);

  const [targetOutletDropdownList, setTargetOutletDropdownList] = useState([]);
  const [selectedTargetOutletId, setSelectedTargetOutletId] = useState("");

  const [supplyItemDropdownList, setSupplyItemDropdownList] = useState([]);

  const [poItems, setPoItems] = useState([
    {
      supplyItemId: "",
      name: "",
      sku: "",
      quantity: 0,
      orderQuantity: 0,
      receivedQuantity: 0,
      price: 0,
      totalPrice: 0,
    },
  ]);

  const [selectedSupplier, setSelectedSupplier] = useState({
    taxRate: 0,
  });

  const [subtotal, setSubtotal] = useState(0);
  const [taxTotal, setTaxTotal] = useState(0);
  const [discountTotal, setDiscountTotal] = useState(0);
  const [finalTotal, setFinalTotal] = useState(0);

  const [categoryProductQuantity, setCategoryProductQuantity] = useState(0);

  const [expandViewDict, setExpandViewDict] = useState({});

  const [expandThreeDots, setExpandThreeDots] = useState({}); //Use to expand the view when three dots are tapped

  const outletSupplyItemsSkuDict = CommonStore.useState(
    (s) => s.outletSupplyItemsSkuDict
  );

  const supplyItems = CommonStore.useState((s) => s.supplyItems);
  const supplyItemsDict = CommonStore.useState((s) => s.supplyItemsDict);
  const suppliers = CommonStore.useState((s) => s.suppliers);
  const allOutlets = MerchantStore.useState((s) => s.allOutlets);
  const merchantId = UserStore.useState((s) => s.merchantId);
  const purchaseOrders = CommonStore.useState((s) => s.purchaseOrders);

  const [importModal, setImportModal] = useState(false);
  const isLoading = CommonStore.useState((s) => s.isLoading);

  const userName = UserStore.useState((s) => s.name);
  const merchantName = MerchantStore.useState((s) => s.name);
  const merchantLogo = MerchantStore.useState((s) => s.logo);

  const currOutletId = MerchantStore.useState((s) => s.currOutletId);

  const outletItems = OutletStore.useState((s) => s.outletItems);
  const outletItemsDict = OutletStore.useState((s) => s.outletItemsDict);
  const outletCategories = OutletStore.useState((s) => s.outletCategories);

  const currOutletTaxes = CommonStore.useState((s) => s.currOutletTaxes);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  const [effectiveStartTime, setEffectiveStartTime] = useState(
    moment().startOf("day").toDate()
  );
  const [effectiveEndTime, setEffectiveEndTime] = useState(
    moment().endOf("day").toDate()
  );
  const [showEffectiveStartTimePicker, setShowEffectiveStartTimePicker] =
    useState(false);
  const [showEffectiveEndTimePicker, setShowEffectiveEndTimePicker] =
    useState(false);
  const [isAvailableDayActive, setIsAvailableDayActive] = useState(false);
  const [selectedEffectiveTypeOptions, setSelectedEffectiveTypeOptions] =
    useState([]);
  const [effectiveDays, setEffectiveDays] = useState(moment().day());
  const [isActive, setIsActive] = useState(true);

  /////////////////////////////////////////////////////////////////////////////////

  const [selectedPrinterAreaList, setSelectedPrinterAreaList] = useState([]);
  const [printerAreaDropdownList, setPrinterAreaDropdownList] = useState([]);

  const outletPrinters = OutletStore.useState((s) => s.outletPrinters);

  /////////////////////////////////////////////////////////////////////////////////
  /* 6 march 2023 tag */

  const [selectedUserTagList, setSelectedUserTagList] = useState([]);
  const [userTagList, setUserTagList] = useState([]);
  const [userTagDropdownList, setUserTagDropdownList] = useState([]);
  const [searchingUserTagText, setSearchingUserTagText] = useState('');
  const crmUserTags = OutletStore.useState((s) => s.crmUserTags);
  const taguserId = UserStore.useState((s) => s.firebaseUid);
  const crmUserTagsDict = OutletStore.useState((s) => s.crmUserTagsDict);
  const [tagModal, setTagModal] = useState(false);

  const [openSection, setOpenSection] = useState(false);
  const [noManualDisc, setNoManualDisc] = useState(false);
  const [selectedHideOutletSectionIdList, setSelectedHideOutletSectionIdList] = useState([]);
  const [outletSectionDropdownList, setOutletSectionDropdownList] = useState([]);
  const outletSections = OutletStore.useState((s) => s.outletSections);

  /////////////////////////////////////////////////////////////////////////////////
  useEffect(() => {
    if (crmUserTags.length > 0) {
      setUserTagDropdownList(
        crmUserTags
          .filter((item) => {
            var isExisted = false;

            for (var i = 0; i < userTagList.length; i++) {
              if (userTagList[i].uniqueId === item.uniqueId) {
                isExisted = true;
                break;
              }
            }

            return !isExisted;
          })
          .map((item) => {
            return { label: item.name, value: item.uniqueId };
          }),
      );
    }
  }, [crmUserTags, userTagList]);
  /////////////////////////////////////////////////////////////////////////////////

  const firebaseUid = UserStore.useState((s) => s.firebaseUid);

  const outletItemsUnsorted = OutletStore.useState((s) => s.outletItems);

  const outletSelectDropdownView = CommonStore.useState(
    (s) => s.outletSelectDropdownView
  );
  const selectedOutletCategoryEdit = CommonStore.useState(
    (s) => s.selectedOutletCategoryEdit
  );

  useEffect(() => {
    if (selectedOutletCategoryEdit) {
      // insert info

      setCategoryUniqueId(selectedOutletCategoryEdit.uniqueId);
      setCategoryName(selectedOutletCategoryEdit.name);
      setHideInOrderTypes(selectedOutletCategoryEdit.hideInOrderTypes || []);
      setIsAvailableDayActive(selectedOutletCategoryEdit.isAvailableDayActive || false,);

      setPrintKDNum(selectedOutletCategoryEdit.printKDNum ? selectedOutletCategoryEdit.printKDNum : 1);

      setSelectedUserTagList(
        selectedOutletCategoryEdit.crmUserTagIdList
          ? selectedOutletCategoryEdit.crmUserTagIdList
          : [],
      );
      setSearchingUserTagText('');

      var effectiveTypeOptionsTemp = [];

      if (selectedOutletCategoryEdit && selectedOutletCategoryEdit.effectiveTypeOptions &&
        selectedOutletCategoryEdit.effectiveTypeOptions.length > 0) {
        for (var i = 0; i < selectedOutletCategoryEdit.effectiveTypeOptions.length; i++) {
          if (
            EFFECTIVE_DAY_DROPDOWN_LIST1.find(
              (item) => item.value === selectedOutletCategoryEdit.effectiveTypeOptions[i],
            )
          ) {
            effectiveTypeOptionsTemp.push(selectedOutletCategoryEdit.effectiveTypeOptions[i]);
          }
        }
        if (selectedOutletCategoryEdit.isAvailableDayActive === true) {
          setSelectedEffectiveTypeOptions(effectiveTypeOptionsTemp);
        }
      }

      setEffectiveStartTime(selectedOutletCategoryEdit.effectiveStartTime ? moment(selectedOutletCategoryEdit.effectiveStartTime).toDate() : moment().startOf('day').toDate());
      setEffectiveEndTime(selectedOutletCategoryEdit.effectiveEndTime ? moment(selectedOutletCategoryEdit.effectiveEndTime).toDate() : moment().endOf('day').toDate());

      setSelectedPrinterAreaList(selectedOutletCategoryEdit.printerAreaList ? selectedOutletCategoryEdit.printerAreaList : []);

      setSelectedHideOutletSectionIdList(selectedOutletCategoryEdit.hideOutletSectionIdList ? selectedOutletCategoryEdit.hideOutletSectionIdList : []);

      setNoManualDisc(selectedOutletCategoryEdit.noManualDisc ? selectedOutletCategoryEdit.noManualDisc : false);
    } else {
      // designed to always mounted, thus need clear manually...

      setCategoryUniqueId('');
      setCategoryName('');
      setHideInOrderTypes([]);
      setEffectiveStartTime(moment().startOf('day').toDate());
      setEffectiveEndTime(moment().startOf('day').toDate());
      setIsAvailableDayActive(false);
      setSelectedEffectiveTypeOptions([EFFECTIVE_DAY_DROPDOWN_LIST1[0].value]);

      setSelectedPrinterAreaList([]);

      setSelectedHideOutletSectionIdList([]);

      setPrintKDNum(1);

      setSelectedUserTagList([]);
      setSearchingUserTagText('');

      setNoManualDisc(false);
    }
  }, [selectedOutletCategoryEdit]);

  useEffect(() => {
    setSupplierDropdownList(
      suppliers.map((supplier) => ({
        label: supplier.name,
        value: supplier.uniqueId,
      }))
    );

    if (selectedSupplierId === "" && suppliers.length > 0) {
      setSelectedSupplierId(suppliers[0].uniqueId);
    }
  }, [suppliers]);

  const allOutletsCategories = OutletStore.useState((s) => s.allOutletsCategories);

  useEffect(() => {
    var outletCategories = [];
    var outletCategoriesDict = {};

    // var allOutletsCategories = [];
    // var allOutletsCategoriesNameDict = {};
    // var allOutletsCategoriesDict = {};

    // var allOutletsCategoriesUnique = [];

    var allOutletsCategoriesCurrOutlet = allOutletsCategories.filter(
      (item) => item.outletId === currOutletId
    );

    for (var i = 0; i < allOutletsCategoriesCurrOutlet.length; i++) {
      outletCategories.push(allOutletsCategoriesCurrOutlet[i]);
      outletCategoriesDict[allOutletsCategoriesCurrOutlet[i].uniqueId] =
        allOutletsCategoriesCurrOutlet[i];
    }

    // Update global.outletCategoriesDict safely
    global.outletCategoriesDict = { ...global.outletCategoriesDict, ...outletCategoriesDict };

    // allOutletsCategories.push(record);

    // if (allOutletsCategoriesNameDict[record.name]) {
    //   allOutletsCategoriesNameDict[record.name].push(record);
    // } else {
    //   allOutletsCategoriesNameDict[record.name] = [record];
    // }

    // if (allOutletsCategoriesDict[record.uniqueId]) {
    //   allOutletsCategoriesDict[record.uniqueId] = record;
    // } else {
    //   allOutletsCategoriesDict[record.uniqueId] = record;
    // }

    // var isExisted = false;

    // for (var j = 0; j < allOutletsCategoriesUnique.length; j++) {
    //   if (allOutletsCategoriesUnique[j].name === record.name) {
    //     isExisted = true;
    //     break;
    //   }
    // }

    // if (!isExisted) {
    //   allOutletsCategoriesUnique.push(record);
    // }


    // allOutletsCategoriesUnique.sort((a, b) => a.name.localeCompare(b.name));

    outletCategories.sort((a, b) => {
      return (a.name || "").localeCompare(b.name || "");
    });

    OutletStore.update((s) => {
      s.outletCategories = outletCategories;
      s.outletCategoriesDict = outletCategoriesDict;
    });

    CommonStore.update((s) => {
      s.selectedOutletItemCategory = outletCategories[0];
    });
  }, [currOutletId, allOutletsCategories]);

  useEffect(() => {
    setTargetOutletDropdownList(
      allOutlets.map((outlet) => ({
        label: outlet.name,
        value: outlet.uniqueId,
      }))
    );

    if (selectedTargetOutletId === "" && allOutlets.length > 0) {
      setSelectedTargetOutletId(allOutlets[0].uniqueId);
    }
  }, [allOutlets]);

  useEffect(() => {
    setSupplyItemDropdownList(
      supplyItems.map((supplyItem) => ({
        label: supplyItem.name,
        value: supplyItem.uniqueId,
      }))
    );

    if (
      supplyItems.length > 0 &&
      poItems.length === 1 &&
      poItems[0].supplyItemId === ""
    ) {
      setPoItems([
        {
          supplyItemId: supplyItems[0].uniqueId,
          name: supplyItems[0].name,
          sku: supplyItems[0].sku,
          quantity: outletSupplyItemsSkuDict[supplyItems[0].sku]
            ? outletSupplyItemsSkuDict[supplyItems[0].sku].quantity
            : 0, // check if the supply item sku for this outlet existed
          orderQuantity: 0,
          receivedQuantity: 0,
          price: supplyItems[0].price,
          totalPrice: 0,
        },
      ]);
    } else if (
      poItems[0].supplyItemId !== "" &&
      Object.keys(supplyItemsDict).length > 0
    ) {
      var poItemsTemp = [...poItems];

      for (var i = 0; i < poItemsTemp.length; i++) {
        const supplyItem = supplyItemsDict[poItemsTemp[i].supplyItemId];

        poItemsTemp[i] = {
          ...poItemsTemp[i],
          quantity: outletSupplyItemsSkuDict[supplyItem.sku]
            ? outletSupplyItemsSkuDict[supplyItem.sku].quantity
            : 0, // check if the supply item sku for this outlet existed | might changed in real time
          price: supplyItem.price, // might changed in real time
        };
      }

      setPoItems(poItemsTemp);
    }
  }, [
    supplyItems,
    supplyItemsDict,
    // outletSupplyItemsSkuDict
  ]);

  useEffect(() => {
    if (suppliers.length > 0 && selectedSupplierId !== "") {
      setSelectedSupplier(
        suppliers.find((supplier) => supplier.uniqueId === selectedSupplierId)
      );
    }
  }, [suppliers, selectedSupplierId]);

  useEffect(() => {
    // console.log('subtotal');
    // console.log(
    //   poItems.reduce((accum, poItem) => accum + poItem.totalPrice, 0),
    // );
    setSubtotal(
      poItems.reduce((accum, poItem) => accum + poItem.totalPrice, 0)
    );
  }, [poItems]);

  useEffect(() => {
    // console.log('taxTotal');
    // console.log(subtotal * selectedSupplier.taxRate);
    setTaxTotal(subtotal * selectedSupplier.taxRate);
  }, [subtotal]);

  useEffect(() => {
    // console.log('finalTotal');
    // console.log(subtotal - discountTotal + taxTotal);
    setFinalTotal(subtotal - discountTotal + taxTotal);
  }, [subtotal, discountTotal, taxTotal]);

  useEffect(() => {
    requestStoragePermission();

    setPoId(nanoid());
  }, []);

  useEffect(() => {
    var uniquePrinterAreaNameList = [];
    var uniquePrinterAreaList = [];

    for (var i = 0; i < outletPrinters.length; i++) {
      var name = '';

      if (outletPrinters[i].area) {
        name = outletPrinters[i].area;
      } else if (outletPrinters[i].name) {
        name = outletPrinters[i].name;
      }

      if (name && !uniquePrinterAreaList.includes(name)) {
        uniquePrinterAreaNameList.push(name);
        uniquePrinterAreaList.push(outletPrinters[i]);
      }
    }

    const printerAreaDropdownListTemp = uniquePrinterAreaList.map((item) => ({
      label: item.area || item.name,
      // value: item.uniqueId,
      value: item.area || item.name,
    }));

    setPrinterAreaDropdownList(printerAreaDropdownListTemp);

    if (selectedOutletCategoryEdit === null) {
      console.log('========================================');
      console.log('null product');
      console.log(printerAreaDropdownListTemp.length);

      if (printerAreaDropdownListTemp.length > 0) {
        console.log('help select');

        // setSelectedPrinterAreaList([printerAreaDropdownListTemp[0].value]);
        setSelectedPrinterAreaList(printerAreaDropdownListTemp.map(printerArea => printerArea.value));
      } else {
        console.log('make blank');

        setSelectedPrinterAreaList([]);
      }
    } else {
      var selectedPrinterAreaListTemp = [];

      if (
        selectedOutletCategoryEdit.printerAreaList &&
        selectedOutletCategoryEdit.printerAreaList.length > 0
      ) {
        for (var i = 0; i < selectedOutletCategoryEdit.printerAreaList.length; i++) {
          var isPrinterAreaMissing = false;

          const printerArea = selectedOutletCategoryEdit.printerAreaList[i];

          for (var j = 0; j < printerAreaDropdownListTemp.length; j++) {
            if (printerAreaDropdownListTemp[j].value === printerArea) {
              isPrinterAreaMissing = true;
              break;
            }
          }

          if (!isPrinterAreaMissing) {
            // means the saved printer area deleted or renamed
            // skip the printer area
          } else {
            selectedPrinterAreaListTemp.push(printerArea);
          }
        }
      }

      setSelectedPrinterAreaList(selectedPrinterAreaListTemp);
    }
  }, [outletPrinters, selectedOutletCategoryEdit]);

  useEffect(() => {
    const outletSectionDropdownListTemp = outletSections.map((item) => ({
      label: item.sectionName,
      // value: item.uniqueId,
      value: item.uniqueId,
    }));

    setOutletSectionDropdownList(outletSectionDropdownListTemp);

    if (selectedOutletCategoryEdit === null) {
      console.log('========================================');
      console.log('null product');
      console.log(outletSectionDropdownListTemp.length);

      if (outletSectionDropdownListTemp.length > 0) {
        console.log('help select');

        // setSelectedHideOutletSectionIdList(outletSectionDropdownListTemp);
        setSelectedHideOutletSectionIdList([]);
      } else {
        console.log('make blank');

        setSelectedHideOutletSectionIdList([]);
      }
    } else {
      var selectedOutletSectionListTemp = [];

      if (
        selectedOutletCategoryEdit.hideOutletSectionIdList &&
        selectedOutletCategoryEdit.hideOutletSectionIdList.length > 0
      ) {
        for (var i = 0; i < selectedOutletCategoryEdit.hideOutletSectionIdList.length; i++) {
          var isOutletSectionMissing = true;

          const outletSectionId = selectedOutletCategoryEdit.hideOutletSectionIdList[i];

          for (var j = 0; j < outletSectionDropdownListTemp.length; j++) {
            if (outletSectionDropdownListTemp[j].value === outletSectionId) {
              isOutletSectionMissing = false;
              break;
            }
          }

          if (!isOutletSectionMissing) {
            // means the saved printer area deleted or renamed
            // skip the printer area
            selectedOutletSectionListTemp.push(outletSectionId);
          } else {

          }
        }
      }

      setSelectedHideOutletSectionIdList(selectedOutletSectionListTemp);
    }
  }, [outletSections, selectedOutletCategoryEdit]);

  const setState = () => { };

  // navigation.dangerouslyGetParent().setOptions({
  //   tabBarVisible: false
  // });

  // const allOutletsItems = OutletStore.useState((s) => s.allOutletsItems);

  // const allOutlets = MerchantStore.useState((s) => s.allOutlets);

  // const merchantName = MerchantStore.useState((s) => s.name);

  const currOutletShiftStatus = OutletStore.useState(
    (s) => s.currOutletShiftStatus
  );
  // const [outletDropdownList, setOutletDropdownList] = useState([]);
  // const [selectedOutletList, setSelectedOutletList] = useState([]); // multi-outlets

  // var outletNames = [];

  // for (var i = 0; i < allOutlets.length; i++) {
  //   for (var j = 0; j < selectedOutletList.length; j++) {
  //     if (selectedOutletList.includes(allOutlets[i].uniqueId)) {
  //       outletNames.push(allOutlets[i].name);
  //       break;
  //     }
  //   }
  // }

  // useEffect(() => {
  //   setOutletDropdownList(
  //     allOutlets.map((item) => {
  //       return { label: item.name, value: item.uniqueId };
  //     })
  //   );
  // }, [allOutlets]);

  var targetOutletDropdownListTemp = allOutlets.map((outlet) => ({
    label: sliceUnicodeStringV2WithDots(outlet.name, 20),
    value: outlet.uniqueId,
  }));

  // useEffect(() => {
  //   CommonStore.update((s) => {
  //     s.outletSelectDropdownView = () => {
  //       return (
  //         <View
  //           style={{
  //             flexDirection: "row",
  //             alignItems: "center",
  //             borderRadius: 8,
  //             width: 200,
  //             backgroundColor: "white",
  //           }}
  //         >
  //           {currOutletId.length > 0 &&
  //             allOutlets.find((item) => item.uniqueId === currOutletId) ? (
  //             <MultiSelect
  //               clearable={false}
  //               singleSelect={true}
  //               defaultValue={currOutletId}
  //               placeholder={"Choose Outlet"}
  //               onChange={(value) => {
  //                 if (value) {
  //                   // if choose the same option again, value = ''
  //                   MerchantStore.update((s) => {
  //                     s.currOutletId = value;
  //                     s.currOutlet =
  //                       allOutlets.find(
  //                         (outlet) => outlet.uniqueId === value
  //                       ) || {};
  //                   });
  //                 }

  //                 CommonStore.update((s) => {
  //                   s.shiftClosedModal = false;
  //                 });
  //               }}
  //               options={targetOutletDropdownListTemp}
  //               className="msl-varsHEADER"
  //             />
  //           ) : (
  //             <ActivityIndicator size={"small"} color={Colors.whiteColor} />
  //           )}

  //           {/* <Select
  //             defaultValue={selectedOutletList}
  //             placeholder={"Choose Outlet"}
  //             onChange={(items) => {
  //               setSelectedOutletList(items);
  //             }}
  //             options={outletDropdownList}
  //             isMulti
  //           /> */}
  //         </View>
  //       );
  //     };
  //   });
  // }, [allOutlets, currOutletId, isLoading, currOutletShiftStatus]);

  navigation.setOptions({
    headerLeft: () => (
      <View
        style={[
          styles.headerLeftStyle,
          {
            width: windowWidth * 0.17,
          },
        ]}
      >
        <img src={headerLogo} width={124} height={26} />
        {/* <Image
          style={{
            width: 124,
            height: 26,
          }}
          resizeMode="contain"
          source={require('../assets/image/logo.png')}
        /> */}
      </View>
    ),
    headerTitle: () => (
      <View
        style={[
          {
            justifyContent: "center",
            alignItems: "center",
            // marginRight: Platform.OS === 'ios' ? "27%" : 0,
            // bottom: switchMerchant ? '2%' : 0,
            //width:  "55%",
          },
          Dimensions.get("screen").width <= 768
            ? { right: Dimensions.get("screen").width * 0.12 }
            : {},
        ]}
      >
        <Text
          style={{
            fontSize: 24,
            // lineHeight: 25,
            textAlign: "center",
            fontFamily: "NunitoSans-Bold",
            color: Colors.whiteColor,
            opacity: 1,
          }}
        >
          Manage Product
        </Text>
      </View>
    ),
    headerRight: () => (
      <View
        style={{
          flexDirection: "row",
          alignItems: "center",
          justifyContent: "space-between",
        }}
      >
        {/* {console.log('edward test')} */}
        {/* {console.log(outletSelectDropdownView)} */}
        {outletSelectDropdownView && outletSelectDropdownView()}
        <View
          style={{
            backgroundColor: "white",
            width: 0.5,
            height: Dimensions.get("screen").height * 0.025,
            opacity: 0.8,
            marginHorizontal: 15,
            bottom: -1,
            // borderWidth: 1
          }}
        ></View>
        <TouchableOpacity
          onPress={() => {
            if (global.currUserRole === 'admin') {
              navigation.navigate("General Settings - KooDoo BackOffice")
            }
          }}
          style={{ flexDirection: "row", alignItems: "center" }}
        >
          <Text
            style={{
              fontFamily: "NunitoSans-SemiBold",
              fontSize: 16,
              color: Colors.secondaryColor,
              marginRight: 15,
            }}
          >
            {userName}
          </Text>
          <View
            style={{
              //backgroundColor: 'red',
              marginRight: 30,
              width: windowHeight * 0.05,
              height: windowHeight * 0.05,
              borderRadius: windowHeight * 0.05 * 0.5,
              alignItems: "center",
              justifyContent: "center",
              backgroundColor: "white",
            }}
          >
            <img
              src={personicon}
              width={windowHeight * 0.035}
              height={windowHeight * 0.035}
            />
            {/* <Image
              style={{
                width: windowHeight * 0.05,
              height: windowHeight * 0.05,
                alignSelf: 'center',
              }}
              source={require('../assets/image/profile-pic.jpg')}
            /> */}
          </View>
        </TouchableOpacity>
      </View>
    ),
  });

  // componentDidMount = () => {
  //   // setInterval(() => {
  //   //   getStockOrder()
  //   //   getStockTransfer()
  //   //   getLowStock()
  //   // }, 1000);
  //   getStockOrder();

  // }

  // async componentWillMount = () => {
  //   await requestStoragePermission()
  // }

  const requestStoragePermission = async () => {
    try {
      const granted = await PermissionsAndroid.request(
        PermissionsAndroid.PERMISSIONS.WRITE_EXTERNAL_STORAGE,
        {
          title: "KooDoo Merchant Storage Permission",
          message: "KooDoo Merchant App needs access to your storage ",
          buttonNegative: "Cancel",
          buttonPositive: "OK",
        }
      );
      if (granted === PermissionsAndroid.RESULTS.GRANTED) {
        //console.log("Storage permission granted");
      } else {
        //console.log("Storage permission denied");
      }
    } catch (err) {
      //console.warn(err);
    }
  };

  const expandThreeDotsFunc = (param) => {
    if (expandThreeDots[param.uniqueId]) {
      setExpandThreeDots({
        ...expandThreeDots,
        [param.uniqueId]: false,
      });
    } else {
      setExpandThreeDots({
        // ...expandThreeDots,
        [param.uniqueId]: true,
      });
    }
  };

  // const importCSV = () => {
  //   try {
  //     const res = DocumentPicker.pick({
  //       type: [DocumentPicker.types.csv],
  //     });
  //     console.log(
  //       res.uri,
  //       res.type,
  //       res.name,
  //       res.size
  //     );
  //   } catch (err) {
  //     if (DocumentPicker.isCancel(err)) {
  //       // User cancelled the picker, exit any dialogs or menus and move on
  //     } else {
  //       throw err;
  //     }
  //   }
  // }

  // const expandOrderFunc = (param) => {
  //   if (expandOrder == false) {
  //     // return setState({ expandOrder: true }), param.expandOrder = true;
  //     setExpandOrder(true);
  //     setExpandViewDict({
  //       ...expandViewDict,
  //       [param.uniqueId]: true,
  //     });
  //     expandViewDict;
  //   } else {
  //     // return setState({ expandOrder: false }), param.expandOrder = false;
  //     setExpandOrder(false);
  //     setExpandViewDict({
  //       ...expandViewDict,
  //       [param.uniqueId]: undefined,
  //     });
  //   }
  // }

  // const exportFunc = () => {
  //   // var body = {
  //   //   data: orderList
  //   // }

  //   if (outletCategories) {
  //     const csvData = convertArrayToCSV(outletCategories);

  //     const pathToWrite = `${RNFetchBlob.fs.dirs.DownloadDir}/koodoo-report-product-category-${moment().format('YYYY-MM-DD-HH-mm-ss')}.csv`;
  //     console.log("PATH", pathToWrite)
  //     RNFetchBlob.fs
  //       .writeFile(pathToWrite, csvData, 'utf8')
  //       .then(() => {
  //         console.log(`wrote file ${pathToWrite}`);
  //         // wrote file /storage/emulated/0/Download/data.csv
  //        window.confirm(
  //           'Success',
  //           `Sent to ${pathToWrite}`,
  //           [{ text: 'OK', onPress: () => { } }],
  //           { cancelable: false },
  //         );
  //       })
  //       .catch(error => console.error(error));
  //   }

  //   // ApiClient.POST(API.exportDataCSV, body, false).then((result) => {
  //   //   console.log("RESULT", result)
  //   //   if (result !== null) {
  //   //     const pathToWrite = `${RNFetchBlob.fs.dirs.DownloadDir}/POData.csv`;
  //   //     console.log("PATH", pathToWrite)
  //   //     RNFetchBlob.fs
  //   //       .writeFile(pathToWrite, result, 'utf8')
  //   //       .then(() => {
  //   //         console.log(`wrote file ${pathToWrite}`);
  //   //         // wrote file /storage/emulated/0/Download/data.csv
  //   //         window.confirm(
  //   //           'Success',
  //   //           'The data had exported',
  //   //           [{ text: 'OK', onPress: () => { } }],
  //   //           { cancelable: false },
  //   //         );
  //   //       })
  //   //       .catch(error => console.error(error));
  //   //   }
  //   // });
  // }

  const importFunc = () => {
    var body = {
      data: orderList,
    };
    // ApiClient.POST(API.exportDataCSV, body, false).then((result) => {
    //   console.log(result)
    // });

    alert("Success: The data has imported");
  };

  const getStockOrder = () => {
    // ApiClient.GET(API.getStockOrder + outletId).then((result) => {
    //   setState({ orderList: result })
    // });
  };

  // function here
  const showDateTimePicker = () => {
    // setState({ isDateTimePickerVisible: true });
    setIsDateTimePickerVisible(true);
  };

  const hideDateTimePicker = () => {
    // setState({ isDateTimePickerVisible: false });
    setIsDateTimePickerVisible(false);
  };

  const handleDatePicked = (date) => {
    // setState({ date: date.toString() });
    setDate(date);
  };

  const renderOrderItem = ({ item, index }) => {
    // console.log('renderOrderItem');
    // console.log(item);

    //var quantity = 0;
    var totalQuantity = 0;

    // for ( var i = 0; i < outletItems.length; i++ ) {
    //   if (outletItems[i].categoryId === item.categoryId){
    //     for ( var j= 0; j<outletItems[i].categoryId; j++ ){
    //      const quantity = outletItems[i].categoryId[j];

    //     totalQuantity += quantity
    //     }
    //   }
    // }

    for (var i = 0; i < outletItems.length; i++) {
      //console.log('hello')
      if (outletItems[i].categoryId === item.uniqueId) {
        //console.log('123hihi')
        totalQuantity += 1;
      }
    }
    setCategoryProductQuantity(totalQuantity);

    return (
      <TouchableOpacity
        onPress={() => {
          // CommonStore.update(s => {
          //   s.selectedOutletCategoryEdit = item;
          // });

          // setPurchaseOrder(false);
          // setAddPurchase(true);

          /* CommonStore.update(s => {
          s.selectedOutletCategoryEdit = item;

          s.timestamp = Date.now;
        }, () => {
          navigation.navigate('Product');
        }); */

          CommonStore.update((s) => {
            s.selectedOutletCategoryEdit = item;

            s.timestamp = Date.now;
          });
          navigation.navigate("Product - KooDoo BackOffice");
        }}
      >
        <View
          style={{
            backgroundColor: "#ffffff",
            flexDirection: "row",
            paddingVertical: 20,
            paddingHorizontal: 20,
            borderBottomWidth: StyleSheet.hairlineWidth,
            borderBottomColor: "#c4c4c4",
          }}
        >
          <Text
            style={{
              fontSize: 14,
              fontFamily: "NunitoSans-Regular",
              width: "21%",
              color: Colors.primaryColor,
            }}
          >{`${item.name}`}</Text>
          <Text
            style={{
              fontSize: 14,
              fontFamily: "NunitoSans-Regular",
              width: "21%",
              color: Colors.primaryColor,
            }}
          >
            {/* {item.hideInOrderTypes && item.hideInOrderTypes.length > 0
              ? item.hideInOrderTypes
                .map((type) => ORDER_TYPE_PARSED[type])
                .join("\n")
              : "-"} */}
          </Text>
          <Text
            style={{
              fontSize: 14,
              fontFamily: "NunitoSans-Regular",
              width: "18%",
              color: Colors.primaryColor,
            }}
          >
            {totalQuantity}
          </Text>
          <Text
            style={{
              fontSize: 14,
              fontFamily: "NunitoSans-Regular",
              width: "18%",
            }}
          >
            {item.createdAt
              ? moment(item.createdAt).format("DD MMM YYYY")
              : "N/A"}
          </Text>
          <Text
            style={{
              fontSize: 14,
              fontFamily: "NunitoSans-Regular",
              width: "18%",
            }}
          >
            {item.updatedAt
              ? moment(item.updatedAt).format("DD MMM YYYY")
              : "N/A"}
          </Text>

          {expandThreeDots[item.uniqueId] == true ? (
            <View
              style={{
                //position: 'absolute',
                width: 110,
                height: 33,
                marginLeft: -110,
                zIndex: 1,
                flexDirection: "column",
                backgroundColor: "#FFFFFF",
                borderWidth: 1,
                //borderColor: '#E7E7E7',
                borderColor: Colors.highlightColor,
                borderRadius: 7,
                shadowOpacity: 0,
                shadowColor: "#000",
                shadowOffset: {
                  height: 2,
                },
                shadowOpacity: 0.22,
                shadowRadius: 3.22,
                elevation: 2,
              }}
            >
              <TouchableOpacity
                style={{
                  flexDirection: "row",
                  height: "100%",
                  justifyContent: "center",
                  alignItems: "center",
                }}
                onPress={() => {
                  // CommonStore.update(s => {
                  //   s.selectedPromotionEdit = item;
                  // });

                  // navigation.navigate('NewCampaign');

                  CommonStore.update((s) => {
                    s.selectedOutletCategoryEdit = item;

                    s.timestamp = Date.now;
                  });

                  setPurchaseOrder(false);
                  setAddPurchase(true);

                  // expandThreeDotsFunc(item);
                }}
              >
                <View style={{ width: "30%", paddingLeft: 12 }}>
                  <Edit size={17} color={Colors.primaryColor} />
                </View>
                <View style={{ width: "70%" }}>
                  <Text style={{ marginLeft: 5 }}>Edit</Text>
                </View>
              </TouchableOpacity>
            </View>
          ) : null}

          <View
            style={{
              width: "4%",
              flexDirection: "row",
              // paddingRight: 20,
              // backgroundColor: 'red'
            }}
          >
            <TouchableOpacity
              style={{
                marginTop: 0,
                alignSelf: "flex-start",
                alignItems: "flex-start",
              }}
              onPress={() => {
                // expandThreeDotsFunc(item)

                CommonStore.update((s) => {
                  s.selectedOutletCategoryEdit = item;
                });

                setPurchaseOrder(false);
                setAddPurchase(true);
                setEditCategory(true);
              }}
            >
              <Edit size={17} color={Colors.primaryColor} />
            </TouchableOpacity>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderOrderItemDelete = ({ item, index }) => {
    // console.log('renderOrderItem');
    // console.log(item);

    //var quantity = 0;
    var totalQuantity = 0;

    // for ( var i = 0; i < outletItems.length; i++ ) {
    //   if (outletItems[i].categoryId === item.categoryId){
    //     for ( var j= 0; j<outletItems[i].categoryId; j++ ){
    //      const quantity = outletItems[i].categoryId[j];

    //     totalQuantity += quantity
    //     }
    //   }
    // }

    for (var i = 0; i < outletItems.length; i++) {
      //console.log('hello')
      if (outletItems[i].categoryId === item.uniqueId) {
        //console.log('123hihi')
        totalQuantity += 1;
      }
    }
    setCategoryProductQuantity(totalQuantity);

    return (
      <TouchableOpacity
        onPress={() => {
          isChecked(item)
        }}
      >
        <View
          style={{
            backgroundColor: "#ffffff",
            flexDirection: "row",
            paddingVertical: 20,
            paddingHorizontal: 20,
            borderBottomWidth: StyleSheet.hairlineWidth,
            borderBottomColor: "#c4c4c4",
          }}
        >
          <Text
            style={{
              fontSize: 14,
              fontFamily: "NunitoSans-Regular",
              width: "21%",
              color: Colors.primaryColor,
            }}
          >{`${item.name}`}</Text>
          <Text
            style={{
              fontSize: 14,
              fontFamily: "NunitoSans-Regular",
              width: "21%",
              color: Colors.primaryColor,
            }}
          >
            {/* {item.hideInOrderTypes && item.hideInOrderTypes.length > 0
              ? item.hideInOrderTypes
                .map((type) => ORDER_TYPE_PARSED[type])
                .join("\n")
              : "-"} */}
          </Text>
          <Text
            style={{
              fontSize: 14,
              fontFamily: "NunitoSans-Regular",
              width: "18%",
              color: Colors.primaryColor,
            }}
          >
            {totalQuantity}
          </Text>
          <Text
            style={{
              fontSize: 14,
              fontFamily: "NunitoSans-Regular",
              width: "18%",
            }}
          >
            {item.createdAt
              ? moment(item.createdAt).format("DD MMM YYYY")
              : "N/A"}
          </Text>
          <Text
            style={{
              fontSize: 14,
              fontFamily: "NunitoSans-Regular",
              width: "18%",
            }}
          >
            {item.updatedAt
              ? moment(item.updatedAt).format("DD MMM YYYY")
              : "N/A"}
          </Text>

          <View
            style={{
              width: "4%",
              flexDirection: "row",
              // paddingRight: 20,
              // backgroundColor: 'red'
            }}
          >
            <input
              onChange={() => {
                isChecked(item)
              }}
              style={{
                alignSelf: 'center',
                borderRadius: 15,
                paddingBottom: 3,

                marginRight: 3,
                width: "30%",
                height: 20,
              }}
              type={'checkbox'}
              checked={deleteList.find((findItem) => findItem.uniqueId === item.uniqueId) ? true : false}
            // disabled={true}
            />
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderSupplierItems = ({ item, index }) => {
    return (
      <View
        style={{
          backgroundColor: "#ffffff",
          flexDirection: "row",
          paddingVertical: 20,
          paddingHorizontal: 20,
          borderBottomWidth: StyleSheet.hairlineWidth,
          borderBottomColor: "#c4c4c4",
          alignItems: "center",
          // height: (Dimensions.get('window').width * 0.1) * 3,
        }}
      >
        <View
          style={{
            width: "20%",
            // marginLeft: 50,
            marginLeft: "-1%",
          }}
        >
          <TextInput
            underlineColorAndroid={Colors.fieldtBgColor}
            style={{
              height: 40,
              width: 150,
              paddingHorizontal: 20,
              backgroundColor: Colors.fieldtBgColor,
              borderRadius: 10,
            }}
            placeholder={"Item name"}
            //placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
            keyboardType={"default"}
            // placeholder={itemName}
            onChangeText={(text) => {
              // setState({ itemName: text });
              setSupplierItems(
                supplierItems.map((supplierItem, i) =>
                  i === index
                    ? {
                      ...supplierItem,
                      name: text,
                      isChanged: true,
                    }
                    : supplierItem
                )
              );
            }}
            value={supplierItems[index].name}
          // ref={myTextInput}
          />
        </View>

        <View
          style={{
            width: "20%",
            // marginLeft: 50,
            // backgroundColor: 'blue',
            marginLeft: "-1%",
          }}
        >
          <TextInput
            editable={item.supplyItemId === ""}
            underlineColorAndroid={Colors.fieldtBgColor}
            style={{
              height: 40,
              width: 150,
              paddingHorizontal: 20,
              backgroundColor: Colors.fieldtBgColor,
              borderRadius: 10,
            }}
            placeholder={"SKU"}
            //placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
            keyboardType={"default"}
            // placeholder={itemName}
            onChangeText={(text) => {
              // setState({ itemName: text });
              setSupplierItems(
                supplierItems.map((supplierItem, i) =>
                  i === index
                    ? {
                      ...supplierItem,
                      sku: text,
                      isChanged: true,
                    }
                    : supplierItem
                )
              );
            }}
            value={supplierItems[index].sku}
          // ref={myTextInput}
          />
        </View>

        <View
          style={{
            width: "20%",
            // marginLeft: 50,
            // backgroundColor: 'blue',
            // marginLeft: '-1%',
          }}
        >
          <TextInput
            editable={item.supplyItemId === ""}
            underlineColorAndroid={Colors.fieldtBgColor}
            style={{
              height: 40,
              width: 100,
              paddingHorizontal: 20,
              backgroundColor: Colors.fieldtBgColor,
              borderRadius: 10,
            }}
            placeholder={"Unit"}
            //placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
            keyboardType={"default"}
            // placeholder={itemName}
            onChangeText={(text) => {
              // setState({ itemName: text });
              setSupplierItems(
                supplierItems.map((supplierItem, i) =>
                  i === index
                    ? {
                      ...supplierItem,
                      unit: text,
                      isChanged: true,
                    }
                    : supplierItem
                )
              );
            }}
            value={supplierItems[index].unit}
          // ref={myTextInput}
          />
        </View>

        <View
          style={{
            width: "20%",
            // marginLeft: 50,
            // backgroundColor: 'blue',
            // marginLeft: '-1%',
          }}
        >
          <TextInput
            underlineColorAndroid={Colors.fieldtBgColor}
            style={{
              height: 40,
              width: 100,
              paddingHorizontal: 20,
              backgroundColor: Colors.fieldtBgColor,
              borderRadius: 10,
            }}
            placeholder={"Price"}
            //placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
            keyboardType={"decimal-pad"}
            // placeholder={itemName}
            onChangeText={(text) => {
              // setState({ itemName: text });
              setSupplierItems(
                supplierItems.map((supplierItem, i) =>
                  i === index
                    ? {
                      ...supplierItem,
                      price: text,
                      isChanged: true,
                    }
                    : supplierItem
                )
              );
            }}
            value={supplierItems[index].price}
          // ref={myTextInput}
          />
        </View>

        <View
          style={{
            width: "10%",
            // marginLeft: 50,
            // backgroundColor: 'blue',
          }}
        ></View>

        <TouchableOpacity
          style={{ marginLeft: 10 }}
          onPress={() => {
            setSupplierItems([
              ...supplierItems.slice(0, index),
              ...supplierItems.slice(index + 1),
            ]);
          }}
        >
          <Icon name="trash-2" size={20} color="#eb3446" />
        </TouchableOpacity>
      </View>
    );
  };

  const email = () => {
    var body = {
      stockTransferId: 1,
      email: Email,
    };
    // ApiClient.POST(API.emailStockTransfer, body, false).then((result) => {

    //   if (result == true) {

    //     window.confirm(
    //       'Success',
    //       'The email had sent',
    //       [
    //         { text: "OK", onPress: () => { } }
    //       ],
    //       { cancelable: false },
    //     );
    //   }
    // });
  };

  // const createStockOrder = () => {
  //   var body = {
  //     poId: poId,
  //     poItems: poItems,
  //     supplierId: selectedSupplierId,
  //     status: poStatus,
  //     outletId: selectedTargetOutletId,
  //     tax: taxTotal,
  //     discount: discountTotal,
  //     totalPrice: subtotal,
  //     finalTotal: finalTotal,
  //     estimatedArrivalDate: date,

  //     merchantId: merchantId,
  //     remarks: '',
  //   };

  //   ApiClient.POST(API.createPurchaseOrder, body).then((result) => {
  //     if (result && result.uniqueId) {
  //       window.confirm(
  //         'Success',
  //         'Purchase order created successfully',
  //         [
  //           { text: "OK", onPress: () => { props.navigation.goBack() } }
  //         ],
  //         { cancelable: false },
  //       );
  //     }
  //   });
  // }

  const createOutletItemCategory = () => {
    //console.log('on createOutletItemCategory');

    if (categoryName !== "") {
      if (selectedOutletCategoryEdit === null) {
        var body = {
          categoryName,
          outletId: currOutletId,
          merchantId,

          hideInOrderTypes,

          // isActive: effectiveDays == selectedEffectiveTypeOptions ? true : false,
          isActive: true,
          effectiveTypeOptions: selectedEffectiveTypeOptions,
          effectiveStartTime: moment(effectiveStartTime).valueOf(),
          effectiveEndTime: moment(effectiveEndTime).valueOf(),
          isAvailableDayActive,

          printerAreaList: selectedPrinterAreaList,

          printKDNum,

          crmUserTagIdList: selectedUserTagList ? selectedUserTagList : [],
          searchingUserTagText,

          hideOutletSectionIdList: selectedHideOutletSectionIdList ? selectedHideOutletSectionIdList : [],

          uid: userId,

          noManualDisc: noManualDisc,
        };

        console.log(body);

        // ApiClient.POST(API.createOutletItemCategory, body).then((result) => {
        APILocal.createOutletItemCategory(body).then((result) => {
          if (result && result.status === "success") {
            if (window.confirm("Success: Product category has been created")) {
              // props.navigation.goBack()
              setEditPurchase(false);
              setAddPurchase(false);
              setPurchaseOrder(true);
            }
          } else {
            alert("Error: Failed to create Product Category");
          }
        });
      } else {
        var body = {
          categoryName,
          outletId: currOutletId,
          hideInOrderTypes,

          uniqueId: selectedOutletCategoryEdit.uniqueId,

          // isActive: isActive !== undefined ? isActive : true,
          isActive: true,
          effectiveTypeOptions: selectedEffectiveTypeOptions,
          effectiveStartTime: moment(effectiveStartTime).valueOf(),
          effectiveEndTime: moment(effectiveEndTime).valueOf(),
          isAvailableDayActive,

          printerAreaList: selectedPrinterAreaList,

          printKDNum,

          crmUserTagIdList: selectedUserTagList ? selectedUserTagList : [],
          searchingUserTagText,

          hideOutletSectionIdList: selectedHideOutletSectionIdList ? selectedHideOutletSectionIdList : [],

          uid: userId,

          noManualDisc: noManualDisc,
        };

        console.log(body);

        // ApiClient.POST(API.updateOutletItemCategory, body).then((result) => {
        APILocal.updateOutletItemCategory(body).then((result) => {
          if (result && result.status === "success") {
            if (window.confirm("Success: Product category has been updated")) {
              // props.navigation.goBack()
              setEditPurchase(false);
              setAddPurchase(false);
              setPurchaseOrder(true);
            }
          } else {
            alert("Error: Failed to update Product Category");
          }
        });
      }
    } else {
      alert("Error: Please fill in all informations:\nCategory Name");
    }
  };

  /* tag */
  const createCRMUserTagOrAddCRMUserTagToCategory = async () => {
    var body = {
      crmUserTagIdList: selectedUserTagList,
      merchantId: merchantId,

      outletItemCategoryId: selectedOutletCategoryEdit.uniqueId,

      searchingUserTagText: searchingUserTagText,
    };

    // clear searching text after submit
    setSearchingUserTagText('');

    CommonStore.update((s) => {
      s.isLoading = true;
    });

    // ApiClient.POST(
    //   API.createCRMUserTagOrAddCRMUserTagToProduct,
    //   body,
    //   false,
    // )
    APILocal.createCRMUserTagOrAddCRMUserTagToCategory({
      body: body,
      uid: taguserId,
    }).then((result) => {
      if (result && result.status === 'success') {
        alert(
          'Success',
          'Tag(s) has been saved to the category',
          [
            {
              text: 'OK',
              onPress: () => {
                // navigation.goBack();
              },
            },
          ],
          { cancelable: false },
        );

        if (result.outletItemCategory) {
          CommonStore.update((s) => {
            s.selectedOutletCategoryEdit = result.outletItemCategory;
          });
        }

        setTagModal(false);
      }

      CommonStore.update((s) => {
        s.isLoading = false;
      });
    });
  };
  const renderProductTag = ({ item }) => {
    return (
      <View
        style={{
          borderWidth: 1,
          borderColor: 'darkgreen',
          borderRadius: 5,
          padding: 5,
          alignSelf: 'baseline',
          justifyContent: 'space-between',
          flexDirection: 'row',
          alignItems: 'center',
          marginRight: 10,
          marginBottom: 5,
        }}>
        <Text style={{ fontWeight: '500', color: 'green' }}>TESTING</Text>
        <TouchableOpacity
          style={{ marginLeft: 5 }}
          onPress={() => {
            alert(
              'Info, Are you sure you want to remove this tag?',
              [
                { text: 'NO', onPress: () => { }, style: 'cancel' },
                {
                  text: 'YES',
                  onPress: () => { },
                },
              ],
              { cancelable: false },
            );
          }}>
          <AntDesign name="closecircle" color={Colors.fieldtTxtColor} />
        </TouchableOpacity>
      </View>
    );
  };

  const convertTemplateToExcelFormat = () => {
    var excelTemplate = [];

    var allOutletsStr = allOutlets.map((item) => item.name).join(";");

    // var taxName = currOutletTaxes[0].name;
    var taxName = "SST";

    var excelColumn = {
      Name: "Fried noodle",
      Description: "Hong Kong style with dark soy sauce",
      SKU: "FN001",
      Price: "12.99",
      "Prepare Time (seconds)": "900",
      Currency: "MYR",
      Category: "Dry Noodles",
      Outlet: allOutletsStr,
      // 'Tax': `${taxName}:${currOutletTaxes[0].rate}`,
      Tax: `${taxName}:0.06`,
      Variant: "Size:Regular/0.00|Large/3.00;Flavour:Original/0.00|Spicy/0.00",
      Addon:
        "Toppings:More fried wonton strips/0/1/2.00|More shrimps/0/1/3.00;Specials:Boiled egg/0/5/2.00",
      // 'Variant': '',
      // 'Addon': '',
      "Image Label (png, jpg, jpeg)": "Fried_noodle",
      Printers: "K1",
      "Stock Count": "10",
    };
    excelTemplate.push(excelColumn);

    var excelColumn2 = {
      Name: "Fried rice",
      Description: "Special fried rice",
      SKU: "FN002",
      Price: "14.99",
      "Prepare Time (seconds)": "900",
      Currency: "MYR",
      Category: "Rice",
      Outlet: allOutletsStr,
      // 'Tax': `${taxName}:${currOutletTaxes[0].rate}`,
      Tax: `${taxName}:0.06`,
      // 'Variant': 'Size:Regular 0.00,Large 3.00;Flavour:Original 0.00,Spicy 0.00',
      // 'Addon': 'Toppings:More fried wonton strips 0 1 2.00,More shrimps 0 1 3.00;Specials:Boiled egg 0 5 2.00',
      Variant: "",
      Addon: "",
      "Image Label (png, jpg, jpeg)": "Fried_rice",
      Printers: "K2,Cashier",
      "Stock Count": "0",
    };
    excelTemplate.push(excelColumn2);

    console.log("excelTemplate");
    console.log(excelTemplate);

    return excelTemplate;
  };

  const download = async (url) => {
    return fetch(url).then((resp) => resp.blob());
  };

  const downloadByGroup = (urls, files_per_group = 5) => {
    return Promise.map(
      urls,
      async (url) => {
        return await download(url);
      },
      { concurrency: files_per_group }
    );
  };

  const exportZip = (blobs, filenames, extensions, extra) => {
    const zip = JsZip();

    blobs.forEach((blob, i) => {
      // hard coded file name
      zip
        .folder(`images`)
        .folder(`${filenames[i]}`)
        .file(`image.${extensions[i]}`, blob);
    });

    // download extra blob
    if (extra) zip.file(`koodoo-product-template.xlsx`, extra);

    zip.generateAsync({ type: "blob" }).then((zipFile) => {
      const fileName = `koodoo-product-template.zip`;
      return FileSaver.saveAs(zipFile, fileName);
    });
  };

  const downloadAndZip = async (urls, filenames, extensions, extra) => {
    if (extensions) {
      let isInvalid = false;
      extensions.forEach((item) => {
        if (!["jpg", "jpeg", "png"].includes(item)) {
          isInvalid = true;
          alert(
            `Unsupported extesion type \n  Extension type: ${item} is not supported`
          );
        }
      });

      if (isInvalid) return;

      return downloadByGroup(urls, 5).then((blobs) =>
        exportZip(blobs, filenames, extensions, extra)
      );
    }
  };

  // convert base64 string to array buffer
  const s2ab = (s) => {
    var buf = new ArrayBuffer(s.length);
    var view = new Uint8Array(buf);
    for (var i = 0; i != s.length; ++i) view[i] = s.charCodeAt(i) & 0xff;
    return buf;
  };
  const exportTemplate = async () => {
    // add image export

    var zip = new JsZip();
    var templateImageUrl = "";

    if (merchantLogo) {
      await new Promise((resolve, reject) => {
        if (
          merchantLogo.startsWith("http") ||
          merchantLogo.startsWith("file")
        ) {
          templateImageUrl = merchantLogo;

          resolve();
        } else {
          getImageFromFirebaseStorage(merchantLogo, (parsedUrl) => {
            templateImageUrl = parsedUrl;

            resolve();
          });
        }
      });
    }

    await toDataUrl(merchantLogo, (base64Image) => {
      // console.log(base64); // base64 is the base64 string

      // base64Image = base64Image.replace("data:image/jpeg;base64,", "");
      const dataUrlPrefix = 'data:image/jpg;base64,';
      const base64Content = base64Image.slice(dataUrlPrefix.length);
      var img = zip.folder("images");
      img.file("Fried_noodle.jpg", base64Content, {
        base64: true,
      });
      var img2 = zip.folder("images");
      img2.file("Fried_rice.jpg", base64Content, {
        base64: true,
      });

      var arrayData = convertTemplateToExcelFormat();
      var wb = XLSX.utils.book_new(),
        ws = XLSX.utils.json_to_sheet(arrayData);

      XLSX.utils.book_append_sheet(wb, ws, "Product Template");

      const workBookData = XLSX.write(wb, {
        type: "binary",
        bookType: "xlsx",
      });

      zip.file("koodoo-product-template.xlsx", workBookData, {
        binary: true,
      });

      zip.generateAsync({ type: "blob" }).then(function (content) {
        // see FileSaver.js
        FileSaver.saveAs(content, "koodoo-product-template.zip");
      });
    });
  };

  // const exportTemplate = async () => {
  //   // try {
  //   //   const excelTemplate = convertTemplateToExcelFormat();
  //   //   const tempFolderName = `koodoo-product-template-${moment().format(
  //   //     'YYYY-MM-DD-HH-mm-ss',
  //   //   )}`;
  //   //   // const tempFolderPath = `${Platform.OS === 'ios'
  //   //   //   ? RNFS.DocumentDirectoryPath
  //   //   //   : RNFS.DownloadDirectoryPath
  //   //   //   }/${tempFolderName}`;
  //   //   // const tempImageFolderName = 'images/Fried_noodle';
  //   //   // const tempImageFolderPath = `${Platform.OS === 'ios'
  //   //   //   ? RNFS.DocumentDirectoryPath
  //   //   //   : RNFS.DownloadDirectoryPath
  //   //   //   }/${tempFolderName}/${tempImageFolderName}`;
  //   //   const tempImageFolderName2 = 'images/Fried_rice';
  //   //   // const tempImageFolderPath2 = `${Platform.OS === 'ios'
  //   //   //   ? RNFS.DocumentDirectoryPath
  //   //   //   : RNFS.DownloadDirectoryPath
  //   //   //   }/${tempFolderName}/${tempImageFolderName2}`;
  //   //   // let exists = await RNFS.exists(tempFolderPath);
  //   //   // if (exists) {
  //   //   //   // exists call delete
  //   //   //   await RNFS.unlink(tempFolderPath);
  //   //   //   // console.log('Previous Temp File Deleted', tempFolderPath);
  //   //   // } else {
  //   //   //   // console.log('Previous Temp File Not Available', tempFolderPath);
  //   //   // }
  //   //   // RNFS.mkdir(tempFolderPath);
  //   //   // RNFS.mkdir(tempImageFolderPath);
  //   //   // RNFS.mkdir(tempImageFolderPath2);
  //   //   var templateImageUrl = '';
  //   //   // download merchant logo as example image file
  //   //   if (merchantLogo) {
  //   //     await new Promise((resolve, reject) => {
  //   //       if (
  //   //         merchantLogo.startsWith('http') ||
  //   //         merchantLogo.startsWith('file')
  //   //       ) {
  //   //         templateImageUrl = merchantLogo;
  //   //         resolve();
  //   //       } else {
  //   //         getImageFromFirebaseStorage(merchantLogo, (parsedUrl) => {
  //   //           templateImageUrl = parsedUrl;
  //   //           resolve();
  //   //         });
  //   //       }
  //   //     });
  //   //     var tempImageFileName = 'image.' + templateImageUrl.split('.').pop();
  //   //     tempImageFileName = tempImageFileName.split('?')[0];
  //   //     // const tempImageFilePath = `${Platform.OS === 'ios'
  //   //     //   ? RNFS.DocumentDirectoryPath
  //   //     //   : RNFS.DownloadDirectoryPath
  //   //     //   }/${tempFolderName}/${tempImageFolderName}/${tempImageFileName}`;
  //   //     // const downloadJob = RNFS.downloadFile({
  //   //     //   fromUrl: templateImageUrl,
  //   //     //   toFile: tempImageFilePath,
  //   //     // });
  //   //     // await downloadJob.promise;
  //   //     // const tempImageFilePath2 = `${Platform.OS === 'ios'
  //   //     //   ? RNFS.DocumentDirectoryPath
  //   //     //   : RNFS.DownloadDirectoryPath
  //   //     //   }/${tempFolderName}/${tempImageFolderName2}/${tempImageFileName}`;
  //   //     // const downloadJob2 = RNFS.downloadFile({
  //   //     //   fromUrl: templateImageUrl,
  //   //     //   toFile: tempImageFilePath2,
  //   //     // });
  //   //     // await downloadJob2.promise;
  //   //     // var excelFile = `${Platform.OS === 'ios' ? RNFS.DocumentDirectoryPath : RNFS.DownloadDirectoryPath}/koodoo-Product${moment().format('YYYY-MM-DD-HH-mm-ss')}.xlsx`;
  //   //     // var excelFile = `${Platform.OS === 'ios'
  //   //     //   ? RNFS.DocumentDirectoryPath
  //   //     //   : RNFS.DownloadDirectoryPath
  //   //     //   }/${tempFolderName}/koodoo-product-template.xlsx`;
  //   //     var excelWorkSheet = XLSX.utils.json_to_sheet(excelTemplate);
  //   //     var excelWorkBook = XLSX.utils.book_new();
  //   //     excelWorkSheet = autofitColumns(excelTemplate, excelWorkSheet);
  //   //     XLSX.utils.book_append_sheet(
  //   //       excelWorkBook,
  //   //       excelWorkSheet,
  //   //       'Product Template',
  //   //     );
  //   //     const workBookData = XLSX.write(excelWorkBook, {
  //   //       type: 'binary',
  //   //       bookType: 'xlsx',
  //   //     });
  //   //     RNFS.writeFile(excelFile, workBookData, 'ascii')
  //   //       .then(async (success) => {
  //   //         // console.log(`wrote file ${excelFile}`);
  //   //         // zip the folder
  //   //         const tempZipPath = `${RNFS.DownloadDirectoryPath}/${tempFolderName}.zip`;
  //   //         let exists = await RNFS.exists(tempZipPath);
  //   //         if (exists) {
  //   //           // exists call delete
  //   //           await RNFS.unlink(tempZipPath);
  //   //           // console.log('Previous Zip File Deleted');
  //   //         } else {
  //   //           // console.log('Previous Zip File Not Available');
  //   //         }
  //   //         zip(tempFolderPath, tempZipPath)
  //   //           .then(async (path) => {
  //   //             // console.log(`zip completed at ${path}`);
  //   //             let exists = await RNFS.exists(tempFolderPath);
  //   //             if (exists) {
  //   //               // exists call delete
  //   //               await RNFS.unlink(tempFolderPath);
  //   //               // console.log('Clean Temp folder File Deleted');
  //   //             } else {
  //   //               // console.log('Clean Temp folder File Not Available');
  //   //             }
  //   //             alert(
  //   //               'Success',
  //   //               `Sent to ${tempZipPath}`,
  //   //               [{ text: 'OK', onPress: () => { } }],
  //   //               { cancelable: false },
  //   //             );
  //   //           })
  //   //           .catch((error) => {
  //   //             console.error(error);
  //   //             alert(
  //   //               'Error',
  //   //               'Failed to export template \nTry deleting the old file and try again',
  //   //             );
  //   //           });
  //   //       })
  //   //       .catch((err) => {
  //   //         // console.log(err.message);
  //   //         alert(
  //   //           'Error',
  //   //           'Failed to export template \nTry deleting the old file and try again',
  //   //         );
  //   //       });
  //   //   } else {
  //   //     alert(
  //   //       'Info',
  //   //       'Please set the merchant logo first before proceed.',
  //   //     );
  //   //   }
  //   // } catch (ex) {
  //   //   console.error(ex);
  //   //   alert(
  //   //     'Error',
  //   //     'Failed to export template \nTry deleting the old file and try again',
  //   //   );
  //   // }
  // };

  const importTemplateData = async () => {
    CommonStore.update((s) => {
      s.isLoading = true;
    });

    try {
      var res = null;
      // if (Platform.OS === 'ios') {
      //   res = await DocumentPicker.pick({
      //     type: [DocumentPicker.types.zip],
      //     copyTo: 'documentDirectory',
      //   });
      // }
      //else {
      // res = await DocumentPicker.pickSingle({
      //   type: [DocumentPicker.types.zip],
      //   copyTo: 'documentDirectory',
      // });
      // }

      var referenceId = uuidv4();
      var referencePath = `/merchant/${merchantId}/batchUploads/${referenceId}.zip`;

      console.log(plainFiles);
      console.log(filesContent);

      // await uploadFileToFirebaseStorage(plainFiles[0], referencePath);
      await uploadFileToFirebaseStorage(filesContent[0].content, referencePath);

      clearAllFiles();

      const body = {
        zipId: referenceId,
        zipPath: referencePath,

        userId: firebaseUid,
        merchantId: merchantId,
        outletId: currOutletId,
      };

      ApiClient.POST(API.batchCreateOutletItems, body)
        .then((result) => {
          if (result && result.status === "success") {
            alert(
              "Success: Submitted to upload queue, we will notify you once processing done"
            );
          } else {
            alert("Error: Failed to import the data");
          }

          CommonStore.update((s) => {
            s.isLoading = false;
          });
        })
        .catch((err) => {
          console.log(err);

          alert("Error: Failed to import the data");

          CommonStore.update((s) => {
            s.isLoading = false;
          });
        });
    } catch (err) {
      CommonStore.update((s) => {
        s.isLoading = false;
      });

      console.error(err);

      alert("Error: Failed to import the data");
    }
  };

  const deleteOutletItemCategory = () => {
    var body = {
      outletItem: outletItemsUnsorted.filter((item) => item.categoryId === categoryUniqueId),
      allOutletsItemAddOnDict: allOutletsItemAddOnDict,
      allOutletsItemAddOnChoiceDict: allOutletsItemAddOnChoiceDict,

      categoryName: categoryName,
      outletId: currOutletId,
      hideInOrderTypes: hideInOrderTypes,

      uniqueId: selectedOutletCategoryEdit.uniqueId,
    };

    console.log('deleting body', body);

    APILocal.deleteOutletItemCategory({
      body,
      uid: userId,
    })
      .then((result) => {
        if (result && result.status === "success") {
          setTimeout(() => {
            if (window.confirm(`Success: Category "${categoryName}" has been deleted`)) {
              // props.navigation.goBack()
              // setEditPurchase(false);
              // setAddPurchase(false);
              // setPurchaseOrder(true);
            }

            setDeleteModal(false);
            setPurchaseOrder(true);
            setEditPurchase(false);
            setAddPurchase(false);
          }, 500);
        } else {
          alert("Error: Failed to delete Product category");
        }
      });
    // ApiClient.POST(API.deleteOutletItemCategory, body).then((result) => {
    //   if (result && result.status === "success") {
    //     if (window.confirm("Success: Product category has been deleted")) {
    //       // props.navigation.goBack()
    //       // setEditPurchase(false);
    //       // setAddPurchase(false);
    //       // setPurchaseOrder(true);
    //     }

    //     setDeleteModal(false);
    //     setPurchaseOrder(true);
    //     setEditPurchase(false);
    //     setAddPurchase(false);
    //   } else {
    //     alert("Error: Failed to delete Product category");
    //   }
    // });
  };

  //Greg - Sorting

  const userId = UserStore.useState((s) => s.firebaseUid);

  const [sortingHelp, setSortingHelp] = useState(false);
  const [isSavingList, setIsSavingList] = useState(false);
  const [draggingScroll, setDraggingScroll] = useState(true);
  const [changeLayout, setChangeLayout] = useState(false);
  const [startDragging, setStartDragging] = useState(false);
  var sortingTemp = {}

  const [sortedCategory, setSortedCategory] = useState([]);
  const [sortedCategoryOrderIndexIdList, setSortedCategoryOrderIndexIdList] = useState({});

  useEffect(() => {
    var filteredOutletCategorySort = outletCategories.slice()

    filteredOutletCategorySort.sort((a, b) => {
      return (
        (sortedCategoryOrderIndexIdList[a.uniqueId]
          ? sortedCategoryOrderIndexIdList[a.uniqueId]
          : a.orderIndex
            ? a.orderIndex
            : outletCategories.length) -
        (sortedCategoryOrderIndexIdList[b.uniqueId]
          ? sortedCategoryOrderIndexIdList[b.uniqueId]
          : b.orderIndex
            ? b.orderIndex
            : outletCategories.length)
      );
    });

    setSortedCategory(filteredOutletCategorySort);
  }, [outletCategories, sortedCategoryOrderIndexIdList,])

  const updateOutletCategoryOrderIndex = () => {
    var sortedCategoryOrderIndexList = Object.entries(sortedCategoryOrderIndexIdList).map(
      ([key, value]) => ({
        outletCategoryId: key,
        orderIndex: value,
      }),
    );

    var body = {
      sortedCategoryOrderIndexIdList: sortedCategoryOrderIndexList,
    };

    // console.log(body);

    // CommonStore.update((s) => {
    //   s.isLoading = true;
    // });

    //APILocal.updateOutletCategoryOrderIndex({ body: body, uid: userId })
    ApiClient.POST(API.updateOutletCategoryOrderIndex, body)
      .then((result) => {
        if (result && result.status === 'success') {
          alert('Success: Menu has been updated.');
        } else {
          alert('Error: Failed to update the menu.');
        }

        CommonStore.update((s) => {
          s.isLoading = false;
        });
      });
    setChangeLayout(false);
  };

  const reorder = (list, startIndex, endIndex) => {
    const result = Array.from(list);
    const [removed] = result.splice(startIndex, 1);
    result.splice(endIndex, 0, removed);

    return result;
  };

  const onDragEnd = (result) => {
    // dropped outside the list
    if (!result.destination) {
      return;
    }

    const items = reorder(
      sortedCategory,
      result.source.index,
      result.destination.index
    );

    setSortedCategory(items);

    // Update the sortedCategoryOrderIndexIdList with new order
    const newSortedCategoryOrderIndexIdList = {};
    items.forEach((item, index) => {
      newSortedCategoryOrderIndexIdList[item.uniqueId] = index + 1;
    });

    setSortedCategoryOrderIndexIdList(newSortedCategoryOrderIndexIdList);
  };

  const getListStyle = (isDraggingOver) => ({
    //background: isDraggingOver ? Colors.lightPrimary : Colors.whiteColor,
    // height: windowHeight * 0.35,
  });

  //Greg - Hide Category

  const [hideModal, setHideModal] = useState(false);
  const [selectedChoice, setSelectedChoice] = useState(null);
  const [hideHelp, setHideHelp] = useState(false);

  const [customTimeSelectStart, setCustomTimeSelectStart] = useState(false);
  const [customTimeSelectEnd, setCustomTimeSelectEnd] = useState(false);
  const hideCategoryChoice = [
    {
      label: '2 HOURS',
      value: 'HOURS_2',
    },
    {
      label: '6 HOURS',
      value: 'HOURS_6',
    },
    {
      label: 'END OF THE DAY',
      value: 'END_DAY',
    },
    {
      label: 'CUSTOM TIME',
      value: 'CUSTOM_TIME',
    },
    {
      label: 'FOREVER',
      value: 'FOREVER',
    },
  ];

  const [customStartTime, setCustomStartTime] = useState(moment().startOf('day').toDate());
  const [customEndTime, setCustomEndTime] = useState(moment().endOf('day').toDate());

  const [isHidden, setIsHidden] = useState(false);

  //Greg - Multi Delete

  const role = UserStore.useState((s) => s.role);

  const [deleteOn, setDeleteOn] = useState(false);
  const [deleteHelp, setDeleteHelp] = useState(false);
  const [deleteList, setDeleteList] = useState([]);

  const [openAO, setOpenAO] = useState(false);
  const [openDND, setOpenDND] = useState(false);
  const [openPA, setOpenPA] = useState(false);
  const [openPKDT, setOpenPKDT] = useState(false);
  const [openTags, setOpenTags] = useState(false);

  const isChecked = (item) => {
    var selectedDeleteItems = sortedCategory.filter(
      (o) => o.uniqueId === item.uniqueId,
    );

    // setDeleteList2(...deleteList2, selectedDeleteItems2)
    console.log('D ITEM', selectedDeleteItems);
    const convertToObj = sortedCategory.find((o) => {
      return selectedDeleteItems.map(item => item.uniqueId).includes(o.uniqueId);
    });
    console.log('convertToObj', convertToObj);

    const itemExists = deleteList.find((o) => {
      return selectedDeleteItems.map(item => item.uniqueId).includes(o.uniqueId);
    });

    if (itemExists) {
      setDeleteList(deleteList.filter((item) => item.uniqueId !== itemExists.uniqueId));
      // setDeleteList({
      //   ...deleteList,
      //   [item.uniqueId]: deleteList[item.uniqueId] ? false : true
      // });
    }
    else {
      setDeleteList([...deleteList, convertToObj]);
    }
  };

  const allOutletsItemAddOnDict = CommonStore.useState(
    (s) => s.allOutletsItemAddOnDict,
  );

  const allOutletsItemAddOnChoiceDict = CommonStore.useState(
    (s) => s.allOutletsItemAddOnChoiceDict,
  );

  useEffect(() => {
    console.log('deleteList', deleteList);
  }, [deleteList]);

  const deleteSelected = () => {
    for (var i = 0; i < deleteList.length; i++) {
      console.log('IN LOOP', i)

      var body = {
        outletItem: outletItemsUnsorted.filter((item) => item.categoryId === deleteList[i].uniqueId),
        allOutletsItemAddOnDict: allOutletsItemAddOnDict,
        allOutletsItemAddOnChoiceDict: allOutletsItemAddOnChoiceDict,

        categoryName: deleteList[i].name,
        outletId: deleteList[i].outletId,
        hideInOrderTypes: deleteList[i].hideInOrderTypes,

        uniqueId: deleteList[i].uniqueId,
      };

      console.log('deleting body', body);

      CommonStore.update((s) => {
        s.isLoading = true;
      });

      // ApiClient.POST(API.deleteOutletItemCategory, body)
      APILocal.deleteOutletItemCategory({
        body,
        uid: userId,
      });
    }

    setTimeout(() => {
      alert(`Success \n${deleteList.length} ${deleteList.length > 1 ? 'categories' : 'category'} have been deleted`);
    }, 1000);

    CommonStore.update((s) => {
      s.isLoading = false;
    });

    setDeleteList([]);
    setDeleteOn(false);
  }

  // function end

  return (
    // <UserIdleWrapper disabled={!isMounted}>
    <View
      style={[
        styles.container,
        {
          height: windowHeight,
          width: windowWidth,
        },
      ]}
    >
      <View style={{ flex: 0.8 }}>
        <SideBar navigation={navigation} selectedTab={2} expandReport={true} />
      </View>
      <View style={{ height: windowHeight, flex: 9 }}>
        <ScrollView
          showsVerticalScrollIndicator={false}
          style={{ width: windowWidth * 0.9 }}
          contentContainerStyle={{
            paddingBottom: windowHeight * 0.1,
          }}
        >
          <View
            style={{
              paddingVertical: 30,
              marginHorizontal: 30,
            }}
          >
            <DateTimePickerModal
              locale="en_GB"
              isVisible={showEffectiveStartTimePicker}
              mode={"time"}
              date={effectiveStartTime}
              onConfirm={(dt) => {
                setShowEffectiveStartTimePicker(false);
                setShowEffectiveEndTimePicker(false);
                setEffectiveStartTime(moment(dt).toDate());
              }}
              onCancel={() => {
                setShowEffectiveStartTimePicker(false);
              }}
            />

            <DateTimePickerModal
              locale="en_GB"
              isVisible={showEffectiveEndTimePicker}
              mode={"time"}
              date={effectiveEndTime}
              onConfirm={(dt) => {
                setShowEffectiveStartTimePicker(false);
                setShowEffectiveEndTimePicker(false);
                setEffectiveEndTime(moment(dt).toDate());
              }}
              onCancel={() => {
                setShowEffectiveEndTimePicker(false);
              }}
            />

            <Modal
              style={{}}
              visible={importModal}
              supportedOrientations={["portrait", "landscape"]}
              transparent={true}
              animationType={"fade"}
            >
              <View
                style={{
                  flex: 1,
                  backgroundColor: Colors.modalBgColor,
                  alignItems: "center",
                  justifyContent: "center",
                }}
              >
                <View
                  style={{
                    height: windowWidth * 0.15,
                    width: windowWidth * 0.2,
                    backgroundColor: Colors.whiteColor,
                    borderRadius: 12,
                    padding: windowWidth * 0.03,
                    alignItems: "center",
                    justifyContent: "center",
                  }}
                >
                  <TouchableOpacity
                    style={{
                      position: "absolute",
                      right: windowWidth * 0.015,
                      top: windowWidth * 0.01,

                      elevation: 1000,
                      zIndex: 1000,
                    }}
                    onPress={() => {
                      // setState({ changeTable: false });
                      setImportModal(false);
                    }}
                  >
                    <AntDesign
                      name="closecircle"
                      size={25}
                      color={Colors.fieldtTxtColor}
                    />
                  </TouchableOpacity>
                  <View style={styles.modalTitle}>
                    <Text style={[styles.modalTitleText]}>Upload Options</Text>
                  </View>
                  <View
                    style={{
                      alignItems: "center",
                      top: "10%",
                    }}
                  >
                    <TouchableOpacity
                      style={{
                        justifyContent: "center",
                        flexDirection: "row",
                        borderWidth: 1,
                        borderColor: Colors.primaryColor,
                        backgroundColor: "#4E9F7D",
                        borderRadius: 5,
                        width: 240,
                        paddingHorizontal: 10,
                        height: 40,
                        alignItems: "center",
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 1,
                        zIndex: -1,
                        marginBottom: 10,
                      }}
                      onPress={() => {
                        openFileSelector();
                      }}
                      disabled={isLoading}
                    >
                      <Text
                        style={{
                          color: Colors.whiteColor,
                          //marginLeft: 5,
                          fontSize: 16,
                          fontFamily: "NunitoSans-Bold",
                        }}
                      >
                        {isLoading ? "Loading..." : "UPLOAD TEMPLATE"}
                      </Text>

                      {isLoading ? (
                        <ActivityIndicator
                          style={{
                            marginLeft: 5,
                          }}
                          color={Colors.whiteColor}
                          size={"small"}
                        />
                      ) : (
                        <></>
                      )}
                    </TouchableOpacity>
                    <TouchableOpacity
                      style={{
                        justifyContent: "center",
                        flexDirection: "row",
                        borderWidth: 1,
                        borderColor: Colors.primaryColor,
                        backgroundColor: "#4E9F7D",
                        borderRadius: 5,
                        width: 240,
                        paddingHorizontal: 10,
                        height: 40,
                        alignItems: "center",
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 1,
                        zIndex: -1,
                      }}
                      onPress={() => {
                        exportTemplate();
                      }}
                    >
                      <Text
                        style={{
                          color: Colors.whiteColor,
                          //marginLeft: 5,
                          fontSize: 16,
                          fontFamily: "NunitoSans-Bold",
                        }}
                      >
                        EXPORT TEMPLATE
                      </Text>
                    </TouchableOpacity>
                  </View>
                </View>
              </View>
            </Modal>

            {!addPurchase ? (
              <View style={{}}>
                <View
                  style={{
                    flexDirection: "row",
                    marginBottom: 5,
                    justifyContent: "flex-end",
                    width: windowWidth * 0.877,
                    alignSelf: "center",
                    marginHorizontal: 30,
                  }}
                >
                  <View
                    style={{
                      height: 70,
                      flexDirection: "row",
                      justifyContent: "space-between",
                    }}
                  >
                    <View
                      style={{ flexDirection: "row", alignItems: "center" }}
                    >
                      <TouchableOpacity
                        style={{
                          justifyContent: "center",
                          flexDirection: "row",
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: "#4E9F7D",
                          borderRadius: 5,
                          //width: 160,
                          paddingHorizontal: 10,
                          height: 40,
                          alignItems: "center",
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                          //marginHorizontal: 10,
                        }}
                        onPress={() => {
                          setImportModal(true);
                        }}
                      >
                        <View
                          style={{ flexDirection: "row", alignItems: "center" }}
                        >
                          <Icon
                            name="upload"
                            size={20}
                            color={Colors.whiteColor}
                          />
                          <Text
                            style={{
                              color: Colors.whiteColor,
                              marginLeft: 5,
                              fontSize: 16,
                              fontFamily: "NunitoSans-Bold",
                            }}
                          >
                            BATCH UPLOAD
                          </Text>
                        </View>
                      </TouchableOpacity>

                      <TouchableOpacity
                        style={{
                          justifyContent: "center",
                          flexDirection: "row",
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: "#4E9F7D",
                          borderRadius: 5,
                          //width: 160,
                          paddingHorizontal: 10,
                          height: 40,
                          alignItems: "center",
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                          marginHorizontal: 10,
                        }}
                        onPress={() => {
                          CommonStore.update((s) => {
                            s.selectedProductEdit = null;

                            s.timestamp = Date.now();
                          });

                          linkTo && linkTo(`${prefix}/product-add`);
                        }}
                      >
                        <View
                          style={{ flexDirection: "row", alignItems: "center" }}
                        >
                          <AntDesign
                            name="pluscircle"
                            size={20}
                            color={Colors.whiteColor}
                          />
                          <Text
                            style={{
                              color: Colors.whiteColor,
                              marginLeft: 5,
                              fontSize: 16,
                              fontFamily: "NunitoSans-Bold",
                            }}
                          >
                            PRODUCT
                          </Text>
                        </View>
                      </TouchableOpacity>

                      <TouchableOpacity
                        style={{
                          justifyContent: "center",
                          flexDirection: "row",
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: "#4E9F7D",
                          borderRadius: 5,
                          //width: 160,
                          paddingHorizontal: 10,
                          height: 40,
                          alignItems: "center",
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                          marginRight: 10,
                        }}
                        onPress={() => {
                          CommonStore.update((s) => {
                            s.selectedOutletCategoryEdit = null;
                          });
                          setPurchaseOrder(false);
                          setAddPurchase(true);
                          setEditCategory(false);
                          //setNewCategoryName('');
                        }}
                      >
                        <View
                          style={{ flexDirection: "row", alignItems: "center" }}
                        >
                          <AntDesign
                            name="pluscircle"
                            size={20}
                            color={Colors.whiteColor}
                          />
                          <Text
                            style={{
                              color: Colors.whiteColor,
                              marginLeft: 5,
                              fontSize: 16,
                              fontFamily: "NunitoSans-Bold",
                            }}
                          >
                            CATEGORY
                          </Text>
                        </View>
                      </TouchableOpacity>

                      <TouchableOpacity
                        style={{
                          justifyContent: "center",
                          flexDirection: "row",
                          borderWidth: 1,
                          borderColor: Colors.primaryColor,
                          backgroundColor: "#4E9F7D",
                          borderRadius: 5,
                          //width: 160,
                          paddingHorizontal: 10,
                          height: 40,
                          alignItems: "center",
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,
                        }}
                        onPress={() => {
                          CommonStore.update((s) => {
                            s.selectedOutletCategoryEdit = null;

                            s.timestamp = Date.now;
                          });

                          navigation.navigate("Product - KooDoo BackOffice");
                        }}
                      >
                        <View
                          style={{ flexDirection: "row", alignItems: "center" }}
                        >
                          {/* <Icon1 name="plus-circle" size={20} color={Colors.primaryColor} /> */}
                          <Text
                            style={{
                              color: Colors.whiteColor,
                              //marginLeft: 5,
                              fontSize: 16,
                              fontFamily: "NunitoSans-Bold",
                            }}
                          >
                            ALL PRODUCTS
                          </Text>
                        </View>
                      </TouchableOpacity>
                    </View>
                  </View>
                </View>
                <View
                  style={{
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "space-between",
                    //marginTop: 10,
                    width: windowWidth * 0.877,
                    alignSelf: "center",
                    marginHorizontal: 30,
                    //backgroundColor: 'blue',
                  }}
                >
                  <View
                    style={{
                      flexDirection: "row",
                      alignItems: "center",
                      paddingTop: 10,
                      marginLeft: 35,
                    }}
                  >
                    <Text
                      style={{ fontSize: 26, fontFamily: "NunitoSans-Bold" }}
                    >
                      {/* {orderList.length} */}
                      {outletCategories.length}
                    </Text>
                    <Text
                      style={{
                        fontSize: 26,
                        fontFamily: "NunitoSans-Bold",
                        marginLeft: Dimensions.get("screen").width * 0.008,
                      }}
                    >
                      {outletCategories.length > 1 ? "Categories" : "Category"}
                    </Text>
                  </View>
                  <View style={{ flexDirection: 'row' }}>

                    {(role === ROLE_TYPE.ADMIN || role === ROLE_TYPE.STORE_MANAGER) ?
                      <TouchableOpacity
                        style={{
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: changeLayout ? 'grey' : '#eb3446',
                          backgroundColor: changeLayout ? 'grey' : '#eb3446',
                          borderRadius: 5,
                          //width: 160,
                          paddingHorizontal: 10,
                          height: 40,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,

                          marginRight: 10,
                        }}
                        disabled={changeLayout}
                        onPress={() => {
                          if (!deleteOn) {
                            setDeleteOn(!deleteOn);
                          }
                          else {
                            if (deleteList.length <= 0) {
                              window.confirm('Error, No items selected')
                            }
                            else {
                              if (window.confirm(`Warning, Are you sure you want to delete ${deleteList.length} item(s)`)) {
                                if (window.confirm(`Warning, Please note that this procedure cannot be undone. \nWould you like to proceed?`)) {
                                  deleteSelected();
                                }
                              }
                            }
                          }
                        }}>
                        <View
                          style={{ flexDirection: 'row', alignItems: 'center' }}>
                          <Icon name="trash-2" size={20} color={Colors.whiteColor} />
                        </View>
                      </TouchableOpacity>
                      : <></>}

                    <TouchableOpacity
                      style={{
                        justifyContent: 'center',
                        flexDirection: 'row',
                        borderWidth: 1,
                        borderColor: deleteOn ? 'grey' : Colors.primaryColor,
                        backgroundColor: deleteOn ? 'grey' : '#4E9F7D',
                        // borderColor: 'grey',
                        // backgroundColor: 'grey',
                        borderRadius: 5,
                        //width: 160,
                        paddingHorizontal: 10,
                        height: 40,
                        alignItems: 'center',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 1,
                        zIndex: -1,

                        marginRight: 10,
                      }}
                      disabled={deleteOn}
                      onPress={() => {
                        setChangeLayout(true);

                        if (changeLayout) {
                          updateOutletCategoryOrderIndex();
                        }
                      }}>
                      <View
                        style={{ flexDirection: 'row', alignItems: 'center' }}>
                        {/* <Icon1 name="plus-circle" size={20} color={Colors.primaryColor} /> */}
                        <Sort color={Colors.whiteColor} />
                      </View>
                    </TouchableOpacity>

                    {deleteOn || changeLayout ?
                      <TouchableOpacity
                        style={{
                          justifyContent: 'center',
                          flexDirection: 'row',
                          borderWidth: 1,
                          borderColor: Colors.fieldtBgColor,
                          backgroundColor: Colors.fieldtBgColor,
                          borderRadius: 5,
                          //width: 160,
                          paddingHorizontal: 10,
                          height: 40,
                          alignItems: 'center',
                          shadowOffset: {
                            width: 0,
                            height: 2,
                          },
                          shadowOpacity: 0.22,
                          shadowRadius: 3.22,
                          elevation: 1,
                          zIndex: -1,

                          marginRight: 10,
                        }}
                        onPress={() => {
                          if (deleteOn) {
                            setDeleteOn(false);
                            setDeleteList([]);
                          }
                          else {
                            setChangeLayout(false);
                          }
                        }}>
                        <View
                          style={{ flexDirection: 'row', alignItems: 'center' }}>
                          {/* <Icon1 name="plus-circle" size={20} color={Colors.primaryColor} /> */}
                          <Text
                            style={{
                              color: Colors.descriptionColor,
                              //marginLeft: 5,
                              fontSize: 16,
                              fontFamily: 'NunitoSans-Bold',
                            }}>
                            CANCEL
                          </Text>
                        </View>
                      </TouchableOpacity>
                      : <></>}
                    <View
                      style={{
                        width: 250,
                        height: 40,
                        backgroundColor: "white",
                        borderRadius: 5,
                        flexDirection: "row",
                        alignContent: "center",
                        alignItems: "center",
                        shadowColor: "#000",
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 3,
                        borderWidth: 1,
                        borderColor: "#E5E5E5",
                        //marginRight: switchMerchant ? 0 : 0,
                      }}
                    >
                      <Icon
                        name="search"
                        size={18}
                        color={Colors.primaryColor}
                        style={{ marginLeft: 15 }}
                      />
                      <TextInput
                        editable={!loading}
                        underlineColorAndroid={Colors.whiteColor}
                        style={{
                          width: 220,
                          fontSize: 16,
                          fontFamily: "NunitoSans-Regular",
                          height: 45,
                        }}
                        clearButtonMode="while-editing"
                        placeholder=" Search"
                        //placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                        onChangeText={(text) => {
                          setSearch(text);
                        }}
                        value={search}
                      />
                    </View>
                  </View>
                </View>
              </View>
            ) : (
              <></>
            )}

            {purchaseOrder ? (
              <View
                style={{
                  flexDirection: "row",
                  marginTop: 0,
                  justifyContent: "space-between",
                }}
              >
                <View
                  style={{
                    backgroundColor: Colors.whiteColor,
                    borderRadius: 10,
                    width: windowWidth * 0.8575,
                    minHeight: Dimensions.get("screen").height * 0.01,
                    marginTop: 30,
                    marginHorizontal: 30,
                    marginBottom: 30,
                    borderRadius: 5,
                    shadowOpacity: 0,
                    shadowColor: "#000",
                    shadowOffset: {
                      width: 0,
                      height: 2,
                    },
                    shadowOpacity: 0.22,
                    shadowRadius: 3.22,
                    elevation: 3,
                  }}
                >
                  <View
                    style={{
                      backgroundColor: Colors.whiteColor,
                      flexDirection: "row",
                      paddingVertical: 20,
                      paddingHorizontal: 20,
                      borderBottomWidth: StyleSheet.hairlineWidth,
                      borderRadius: 5,
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 3,
                    }}
                  >
                    <Text
                      style={{
                        fontSize: 14,
                        width: "21%",
                        alignSelf: "center",
                        color: Colors.blackColor,
                        fontFamily: "NunitoSans-Bold",
                      }}
                    >
                      Category Name
                    </Text>
                    <Text
                      style={{
                        fontSize: 14,
                        width: "21%",
                        alignSelf: "center",
                        color: Colors.blackColor,
                        fontFamily: "NunitoSans-Bold",
                      }}
                    >
                      Do Not Display
                    </Text>
                    <Text
                      style={{
                        fontSize: 14,
                        width: "18%",
                        alignSelf: "center",
                        color: Colors.blackColor,
                        fontFamily: "NunitoSans-Bold",
                      }}
                    >
                      Product Quantity
                    </Text>
                    <Text
                      style={{
                        fontSize: 14,
                        width: "18%",
                        alignSelf: "center",
                        color: Colors.blackColor,
                        fontFamily: "NunitoSans-Bold",
                      }}
                    >
                      Created Date
                    </Text>
                    <Text
                      style={{
                        fontSize: 14,
                        width: "19%",
                        alignSelf: "center",
                        color: Colors.blackColor,
                        fontFamily: "NunitoSans-Bold",
                      }}
                    >
                      Modified Date
                    </Text>
                  </View>

                  {changeLayout ?
                    <ScrollView
                      //scrollEnabled={!switchMerchant}
                      nestedScrollEnabled={true}
                    >
                      <DragDropContext onDragEnd={onDragEnd}>
                        <Droppable droppableId="droppable">
                          {(provided, snapshot) => (
                            <div
                              {...provided.droppableProps}
                              ref={provided.innerRef}
                              style={getListStyle(snapshot.isDraggingOver)}
                            >
                              {sortedCategory.map((item, index) => (
                                <Draggable
                                  key={item.uniqueId}
                                  draggableId={item.uniqueId}
                                  index={index}
                                >
                                  {(provided, snapshotChild) => (
                                    <div
                                      ref={provided.innerRef}
                                      {...provided.draggableProps}
                                      {...provided.dragHandleProps}
                                    // style={getItemStyle(
                                    //   snapshot.isDragging,
                                    //   provided.draggableProps.style
                                    // )}
                                    >
                                      <View
                                        style={{
                                          backgroundColor: "#ffffff",
                                          flexDirection: "row",
                                          paddingVertical: 20,
                                          paddingHorizontal: 20,
                                          borderBottomWidth: StyleSheet.hairlineWidth,
                                          borderBottomColor: "#c4c4c4",

                                          ...(snapshotChild.isDragging) && {
                                            // position: 'absolute',
                                            width: '100%',
                                            bottom: 300,
                                          },
                                        }}
                                      >
                                        <Text
                                          style={{
                                            fontSize: 14,
                                            fontFamily: "NunitoSans-Regular",
                                            width: "21%",
                                            color: Colors.primaryColor,
                                          }}
                                        >{`${item.name}`}</Text>
                                        <Text
                                          style={{
                                            fontSize: 14,
                                            fontFamily: "NunitoSans-Regular",
                                            width: "21%",
                                            color: Colors.primaryColor,
                                          }}
                                        >
                                          {/* {item.hideInOrderTypes && item.hideInOrderTypes.length > 0
              ? item.hideInOrderTypes
                .map((type) => ORDER_TYPE_PARSED[type])
                .join("\n")
              : "-"} */}
                                        </Text>
                                        <Text
                                          style={{
                                            fontSize: 14,
                                            fontFamily: "NunitoSans-Regular",
                                            width: "18%",
                                            color: Colors.primaryColor,
                                          }}
                                        >
                                          {/* {totalQuantity} */}
                                          -
                                        </Text>
                                        <Text
                                          style={{
                                            fontSize: 14,
                                            fontFamily: "NunitoSans-Regular",
                                            width: "18%",
                                          }}
                                        >
                                          {item.createdAt
                                            ? moment(item.createdAt).format("DD MMM YYYY")
                                            : "N/A"}
                                        </Text>
                                        <Text
                                          style={{
                                            fontSize: 14,
                                            fontFamily: "NunitoSans-Regular",
                                            width: "18%",
                                          }}
                                        >
                                          {item.updatedAt
                                            ? moment(item.updatedAt).format("DD MMM YYYY")
                                            : "N/A"}
                                        </Text>

                                        <View
                                          style={{
                                            width: "4%",
                                            flexDirection: "row",
                                            // paddingRight: 20,
                                            // backgroundColor: 'red'
                                          }}
                                        >
                                          <Ionicon
                                            name="menu"
                                            style={{
                                              bottom: 1,
                                            }}
                                            size={22}
                                            color={Colors.primaryColor}
                                          />
                                        </View>
                                      </View>
                                    </div>
                                  )}
                                </Draggable>
                              ))}
                              {provided.placeholder}
                            </div>
                          )}
                        </Droppable>
                      </DragDropContext>
                    </ScrollView>
                    :
                    <ScrollView
                      //scrollEnabled={!switchMerchant}
                      nestedScrollEnabled={true}
                    >
                      {deleteOn ?
                        <FlatList
                          data={sortedCategory.filter((item) => {
                            var totalQuantity = 0;

                            for (var i = 0; i < outletItems.length; i++) {
                              //console.log('hello')
                              if (outletItems[i].categoryId === item.uniqueId) {
                                //console.log('123hihi')
                                totalQuantity += 1;
                              }
                            }

                            if (search !== "") {
                              const searchLowerCase = search.toLowerCase();

                              if (
                                item.name.toLowerCase().includes(searchLowerCase)
                              ) {
                                return true;
                              } else if (
                                totalQuantity.toString().includes(searchLowerCase)
                              ) {
                                return true;
                              } else {
                                return false;
                              }
                            } else {
                              return true;
                            }
                          })}
                          nestedScrollEnabled={true}
                          renderItem={renderOrderItemDelete}
                          keyExtractor={(item, index) => String(index)}
                        />
                        :
                        <FlatList
                          data={sortedCategory.filter((item) => {
                            var totalQuantity = 0;

                            for (var i = 0; i < outletItems.length; i++) {
                              //console.log('hello')
                              if (outletItems[i].categoryId === item.uniqueId) {
                                //console.log('123hihi')
                                totalQuantity += 1;
                              }
                            }

                            if (search !== "") {
                              const searchLowerCase = search.toLowerCase();

                              if (
                                item.name.toLowerCase().includes(searchLowerCase)
                              ) {
                                return true;
                              } else if (
                                totalQuantity.toString().includes(searchLowerCase)
                              ) {
                                return true;
                              } else {
                                return false;
                              }
                            } else {
                              return true;
                            }
                          })}
                          nestedScrollEnabled={true}
                          renderItem={renderOrderItem}
                          keyExtractor={(item, index) => String(index)}
                        />
                      }
                      <View style={{ height: 100 }}></View>
                    </ScrollView>
                  }
                </View>
              </View>
            ) : null}

            {addPurchase ? (
              <View
                style={{
                  height: Dimensions.get("window").height - 200,
                  width: Dimensions.get("window").width * 0.89,
                }}
              >
                <View>
                  <Modal
                    style={{ flex: 1 }}
                    visible={deleteModal}
                    transparent={true}
                    animationType="slide"
                  >
                    <View style={styles.modalContainer}>
                      <View
                        style={[
                          styles.modalView1,
                          {
                            height: Dimensions.get("screen").height * 0.25,
                          },
                        ]}
                      >
                        <TouchableOpacity
                          style={styles.closeButton}
                          onPress={() => {
                            setDeleteModal(false);
                          }}
                        >
                          <AIcon
                            name="closecircle"
                            size={25}
                            color={Colors.fieldtTxtColor}
                          />
                        </TouchableOpacity>
                        <View style={{}}>
                          <Text
                            style={{
                              fontWeight: "700",
                              fontFamily: "Nunitosans-Regular",
                              fontSize: 18,
                            }}
                          >
                            {/* Please Key In The Authorization Code */}
                            {`Are you sure you want to delete this category ${selectedOutletCategoryEdit
                              ? selectedOutletCategoryEdit.name
                              : ""
                              } ?`}
                          </Text>
                        </View>
                        <View style={{}}>
                          <TouchableOpacity
                            style={{
                              borderRadius: 5,
                              backgroundColor: Colors.tabRed,
                              height: 35,
                              width: 90,
                              alignItems: "center",
                              justifyContent: "center",
                            }}
                            onPress={() => {
                              deleteOutletItemCategory();
                            }}
                          >
                            <Text
                              style={{
                                fontSize: 15,
                                color: "white",
                                fontWeight: "500",
                              }}
                            >
                              DELETE
                            </Text>
                          </TouchableOpacity>
                        </View>
                      </View>
                    </View>
                  </Modal>
                  <Modal
                    style={{ flex: 1 }}
                    visible={visible}
                    transparent={true}
                    animationType="slide"
                  >
                    <View
                      style={{
                        backgroundColor: "rgba(0,0,0,0.5)",
                        flex: 1,
                        justifyContent: "center",
                        alignItems: "center",
                        minHeight: Dimensions.get("window").height,
                      }}
                    >
                      <View style={styles.confirmBox}>
                        <TouchableOpacity
                          onPress={() => {
                            setState({ visible: false });
                          }}
                        >
                          <View
                            style={{
                              alignSelf: "flex-end",
                              padding: 16,
                            }}
                          >
                            {/* <Close name="closecircle" size={25} /> */}
                            <AntDesign
                              name="closecircle"
                              size={25}
                              color={Colors.fieldtTxtColor}
                            />
                          </View>
                        </TouchableOpacity>
                        <View>
                          <Text
                            style={{
                              textAlign: "center",
                              fontWeight: "700",
                              fontSize: 28,
                            }}
                          >
                            Purchase Order
                          </Text>
                        </View>
                        <View style={{ marginTop: 20 }}>
                          <Text
                            style={{
                              textAlign: "center",
                              color: Colors.descriptionColor,
                              fontSize: 25,
                            }}
                          >
                            Fill In The Email Information
                          </Text>
                        </View>
                        <View
                          style={{
                            backgroundColor: "white",
                            alignSelf: "center",
                            flexDirection: "row",
                          }}
                        >
                          <Text style={{ fontSize: 20, marginTop: 70 }}>
                            Email:
                          </Text>
                          <View
                            style={{
                              marginTop: 60,
                              backgroundColor: "#f7f5f5",
                              marginLeft: 10,
                            }}
                          >
                            <TextInput
                              editable={!loading}
                              underlineColorAndroid={Colors.fieldtBgColor}
                              clearButtonMode="while-editing"
                              style={styles.textCapacity}
                              placeholder="<EMAIL>"
                              //placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                              onChangeText={(text) => {
                                setState({ Email: text });
                              }}
                              value={Email}
                            />
                          </View>
                        </View>
                        <View
                          style={{
                            flexDirection: "row",
                            alignSelf: "center",
                            marginTop: 20,
                            justifyContent: "center",
                            alignItems: "center",
                            width: "50%",
                            alignContent: "center",
                            zIndex: 6000,
                          }}
                        ></View>
                        <View
                          style={{
                            alignSelf: "center",
                            marginTop: 20,
                            justifyContent: "center",
                            alignItems: "center",
                            width: 250,
                            height: 40,
                            alignContent: "center",
                            flexDirection: "row",
                            marginTop: 40,
                          }}
                        >
                          <TouchableOpacity
                            // onPress={() => {
                            //   email(),
                            //     setState({ visible: false });
                            // }}
                            style={{
                              backgroundColor: Colors.fieldtBgColor,
                              width: "60%",
                              justifyContent: "center",
                              alignItems: "center",
                              alignContent: "center",
                              borderRadius: 10,
                              height: 60,
                            }}
                          >
                            <Text
                              style={{
                                fontSize: 28,
                                color: Colors.primaryColor,
                              }}
                            >
                              Send
                            </Text>
                          </TouchableOpacity>
                          <TouchableOpacity
                            onPress={() => {
                              setState({ visible: false });
                            }}
                            style={{
                              backgroundColor: Colors.fieldtBgColor,
                              width: "60%",
                              justifyContent: "center",
                              alignItems: "center",
                              alignContent: "center",
                              borderRadius: 10,
                              height: 60,
                              marginLeft: 30,
                            }}
                          >
                            <Text
                              style={{
                                fontSize: 28,
                                color: Colors.primaryColor,
                              }}
                            >
                              No
                            </Text>
                          </TouchableOpacity>
                        </View>
                      </View>
                    </View>
                  </Modal>

                  {tagModal ? (
                    <View
                      style={{
                        flex: 1,
                        position: 'absolute',
                        //backgroundColor: 'white',

                        shadowColor: '#000',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 3,

                        borderRadius: 8,
                        top: '20%',
                        left: '10%',
                        zIndex: 5,
                      }}
                    // visible={tagModal}
                    // transparent={true}
                    >
                      <View
                        style={[
                          styles.modalContainer,
                          {
                            backgroundColor: 'transparent',

                            backgroundColor: 'white',

                            shadowColor: '#000',
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 3,

                            borderWidth: 1,
                            borderRadius: 8,
                            top: 0,
                          },
                        ]}>
                        <View
                          style={[
                            styles.tagModalView,
                            {
                              height: switchMerchant ? 200 : 250,
                              width: switchMerchant ? 300 : 415,
                              //height: windowWidth * 0.3,
                              // top: Platform.OS === 'ios' && keyboardHeight > 0 ? -keyboardHeight * 0.3 : 0,
                              backgroundColor: 'transparent',
                            },
                          ]}>
                          <TouchableOpacity
                            style={styles.closeButton}
                            onPress={() => {
                              setTagModal(false);
                            }}>
                            {/* <AIcon name="closecircle" size={25} color={Colors.fieldtTxtColor} /> */}
                            <AntDesign
                              name="closecircle"
                              size={switchMerchant ? 15 : 25}
                              color={Colors.fieldtTxtColor}
                            />
                          </TouchableOpacity>

                          <View style={{ marginBottom: 2 }}>
                            <Text
                              style={{
                                fontWeight: '700',
                                fontSize: switchMerchant ? 10 : 21,
                              }}>
                              Tags
                            </Text>
                          </View>

                          <View
                            style={{
                              // backgroundColor: Colors.fieldtBgColor,
                              //width: 180,
                              height: switchMerchant ? 35 : 40,
                              borderRadius: 5,
                              paddingVertical: 3,
                              paddingLeft: 5,
                              marginVertical: 5,
                              // 0borderWidth: 1,
                              borderColor: '#E5E5E5',
                              flexDirection: 'row',
                              alignItems: 'center',
                              justifyContent: 'space-between',
                              width: '100%',

                              marginTop: 30,
                            }}>
                            <View
                              style={{
                                flexDirection: 'row',
                                alignItems: 'center',
                                width: '60%',
                                zIndex: 1,
                              }}>
                              <AntDesign
                                name="tags"
                                size={switchMerchant ? 17 : 20}
                                style={{ color: 'black' }}
                              />
                              {/* <TextInput
                                placeholder='Enter Tag(s)'
                                style={{
                                  padding: 5,
                                }}
                              /> */}
                              {/* {
                                (
                                  // selectedUserTagList.every((val) =>
                                  //   userTagDropdownList
                                  //     .map((tag) => tag.value)
                                  //     .includes(val)
                                  // )
                                  // ||
                                  selectedUserTagList.length === 0
                                )
                                  ? */}
                              <DropDownPicker
                                style={{
                                  backgroundColor: Colors.fieldtBgColor,
                                  width: 185,
                                  height: 40,
                                  borderRadius: 10,
                                  borderWidth: 1,
                                  borderColor: "#E5E5E5",
                                  flexDirection: "row",
                                }}
                                dropDownContainerStyle={{
                                  width: 185,
                                  backgroundColor: Colors.fieldtBgColor,
                                  borderColor: "#E5E5E5",
                                }}
                                labelStyle={{
                                  marginLeft: 5,
                                  flexDirection: "row",
                                }}
                                textStyle={{
                                  fontSize: 14,
                                  fontFamily: 'NunitoSans-Regular',

                                  marginLeft: 5,
                                  paddingVertical: 10,
                                  flexDirection: "row",
                                }}
                                selectedItemContainerStyle={{
                                  flexDirection: "row",
                                }}

                                showArrowIcon={true}
                                ArrowDownIconComponent={({ style }) => (
                                  <Ionicon
                                    size={25}
                                    color={Colors.fieldtTxtColor}
                                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                                    name="chevron-down-outline"
                                  />
                                )}
                                ArrowUpIconComponent={({ style }) => (
                                  <Ionicon
                                    size={25}
                                    color={Colors.fieldtTxtColor}
                                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                                    name="chevron-up-outline"
                                  />
                                )}

                                showTickIcon={true}
                                TickIconComponent={({ press }) => (
                                  <Ionicon
                                    style={{ paddingHorizontal: 5, marginTop: 5 }}
                                    color={
                                      press ? Colors.fieldtBgColor : Colors.primaryColor
                                    }
                                    name={'md-checkbox'}
                                    size={25}
                                  />
                                )}
                                items={userTagDropdownList}
                                value={selectedUserTagList}
                                placeholder={"Select tag(s)"}
                                multipleText={`${selectedUserTagList.length} tag(s) selected`}
                                placeholderStyle={{
                                  color: Colors.fieldtTxtColor,
                                  // marginTop: 15,
                                }}
                                onSelectItem={(items) => {
                                  setSelectedUserTagList(items.map(item => item.value));
                                }}
                                multiple={true}
                                searchable={true}
                                onSearch={(text) => {
                                  setSearchingUserTagText(text);
                                }}
                                open={openTags}
                                setOpen={setOpenTags}
                              />
                              {/* :
                                   <MultiSelect
                                  //singleSelect={true}
                                  //disabled={true}
                                  defaultValue={selectedUserTagList}
                                  placeholder={'Select tag(s)'}
                                  onChange={(items) => {
                                    setSelectedUserTagList(items);
                                  }}
                                  options={userTagDropdownList}
                                  className={"msl-vars5"}
                                /> */}
                              {/* } */}
                            </View>

                            <View
                              style={{
                                width: '30%',
                                flexDirection: 'row',
                                justifyContent: 'flex-end',
                                alignItems: 'center',
                                //zIndex: -1,
                              }}>
                              <TouchableOpacity
                                style={{
                                  backgroundColor: Colors.primaryColor,
                                  width: 70,
                                  height: 35,
                                  justifyContent: 'center',
                                  alignItems: 'center',
                                  borderRadius: 5,

                                  flexDirection: 'row',
                                }}
                                disabled={isLoading}
                                onPress={createCRMUserTagOrAddCRMUserTagToCategory}>
                                <Text
                                  style={{
                                    fontSize: switchMerchant ? 10 : 17,
                                    fontWeight: '600',
                                    color: 'white',
                                  }}>
                                  {isLoading ? '' : 'SAVE'}
                                </Text>

                                {isLoading ? (
                                  <ActivityIndicator
                                    color={Colors.whiteColor}
                                    size={'small'}
                                  />
                                ) : (
                                  <></>
                                )}
                              </TouchableOpacity>
                            </View>
                          </View>

                          {/* Choosen Tags Start */}
                          <View style={{ minHeight: 40, maxHeight: 140, zIndex: -1 }}>
                            <ScrollView
                              style={{
                                //marginRight: 12,
                                zIndex: -1,
                                minHeight: 70,
                              }}
                              horizontal={true}
                              showsHorizontalScrollIndicator={false}
                              showsVerticalScrollIndicator={false}>
                              <FlatList
                                data={userTagList}
                                //numRows={2}
                                numColumns={3}
                                renderItem={renderProductTag}
                                keyExtractor={(item, index) => String(index)}
                                style={{
                                  paddingVertical: 5,
                                  paddingHorizontal: 5,
                                }}
                                //horizontal={true}
                                showsHorizontalScrollIndicator={false}
                              />
                            </ScrollView>
                          </View>
                          {/* Choosen Tags End */}
                        </View>
                      </View>
                    </View>
                  ) : (
                    <></>
                  )}

                  <TouchableOpacity
                    style={{
                      marginBottom: 0,
                      flexDirection: "row",
                      alignContent: "center",
                      alignItems: "center",
                      marginTop: 5,
                      paddingLeft: "1.2%",
                    }}
                    onPress={() => {
                      setPurchaseOrder(true);
                      setAddPurchase(false);
                    }}
                  >
                    <Icon
                      name="chevron-left"
                      size={30}
                      color={Colors.primaryColor}
                    />
                    <Text
                      style={{
                        fontFamily: "NunitoSans-Bold",
                        fontSize: 17,
                        color: Colors.primaryColor,
                        marginBottom: 2,
                        marginLeft: "-0.5%",
                      }}
                    >
                      {" "}
                      Back{" "}
                    </Text>
                  </TouchableOpacity>

                  <ScrollView
                    nestedScrollEnabled={true}
                    contentContainerStyle={{
                      justifyContent: "center",
                      alignItems: "center",
                    }}
                    style={{
                      backgroundColor: Colors.whiteColor,
                      borderRadius: 10,
                      minHeight: Dimensions.get("screen").height * 0.01,
                      height: Dimensions.get("screen").height * 0.65,
                      marginTop: 30,
                      marginHorizontal: 30,
                      marginBottom: 30,
                      borderRadius: 5,
                      shadowOpacity: 0,
                      shadowColor: "#000",
                      shadowOffset: {
                        width: 0,
                        height: 2,
                      },
                      shadowOpacity: 0.22,
                      shadowRadius: 3.22,
                      elevation: 3,
                    }}
                  >
                    <View style={{ width: windowWidth * 0.825 }}>
                      <View
                        style={{
                          alignSelf: "flex-end",
                          marginTop: 20,
                          position: "absolute",
                          zIndex: 10000,
                        }}
                      >
                        {editCategory ? (
                          <View
                            style={{
                              justifyContent: "center",
                              flexDirection: "row",
                              borderWidth: 1,
                              borderColor: Colors.tabRed,
                              backgroundColor: Colors.tabRed,
                              borderRadius: 5,
                              width: 130,
                              paddingHorizontal: 10,
                              height: 40,
                              alignItems: "center",
                              shadowOffset: {
                                width: 0,
                                height: 2,
                              },
                              shadowOpacity: 0.22,
                              shadowRadius: 3.22,
                              elevation: 1,
                              zIndex: -1,
                            }}
                          >
                            <TouchableOpacity
                              style={{
                                width: 130,
                                height: 40,
                                alignItems: "center",
                                justifyContent: "center",
                              }}
                              onPress={() => {
                                setDeleteModal(true);
                              }}
                            >
                              <Text
                                style={{
                                  color: Colors.whiteColor,
                                  marginLeft: 5,
                                  fontSize: 16,
                                  fontFamily: "NunitoSans-Bold",
                                }}
                              >
                                DELETE
                              </Text>
                            </TouchableOpacity>
                          </View>
                        ) : (
                          <></>
                        )}

                        <View
                          style={{
                            justifyContent: "center",
                            flexDirection: "row",
                            borderWidth: 1,
                            borderColor: Colors.primaryColor,
                            backgroundColor: "#4E9F7D",
                            borderRadius: 5,
                            width: 130,
                            paddingHorizontal: 20,
                            height: 40,
                            alignItems: "center",
                            shadowOffset: {
                              width: 0,
                              height: 2,
                            },
                            shadowOpacity: 0.22,
                            shadowRadius: 3.22,
                            elevation: 1,
                            zIndex: -1,
                            marginTop: 10,
                          }}
                        >
                          <TouchableOpacity
                            style={{
                              width: 130,
                              height: 40,
                              alignItems: "center",
                              justifyContent: "center",
                            }}
                            onPress={createOutletItemCategory}
                          >
                            <Text
                              style={{
                                color: Colors.whiteColor,
                                marginLeft: 5,
                                fontSize: 16,
                                fontFamily: "NunitoSans-Bold",
                              }}
                            >
                              {editCategory ? "SAVE" : "ADD"}
                            </Text>
                          </TouchableOpacity>
                        </View>
                      </View>
                      <View>
                        <Text
                          style={{
                            alignSelf: "center",
                            marginTop: 30,
                            fontSize: 40,
                            fontWeight: "bold",
                          }}
                        >
                          {editCategory
                            ? "Edit Product Category"
                            : "Add Product Category"}
                        </Text>
                        <Text
                          style={{
                            alignSelf: "center",
                            fontSize: 16,
                            color: "#adadad",
                          }}
                        >
                          Fill In The Product Category Information
                        </Text>
                      </View>

                      <View
                        style={{
                          flexDirection: "row",
                          marginTop: 10,
                          justifyContent: "space-evenly",
                          marginTop: 50,
                          marginBottom: 40,
                          width: "90%",
                          alignSelf: "center",
                          marginLeft:
                            Dimensions.get("screen").width *
                            Styles.sideBarWidth *
                            0.5,
                        }}
                      >
                        <View
                          style={{
                            flexDirection: 'row',
                            flex: 1,
                            alignItems: 'center',
                            // justifyContent: 'space-around',
                            width: '50%',
                          }}>
                          <Text
                            style={{
                              fontFamily: 'NunitoSans-Regular',
                              fontSize: switchMerchant ? 10 : 14,
                              width: '20%',
                              textAlign: 'left',
                            }}>
                            Category Name
                          </Text>

                          <TextInput
                            editable={true}
                            underlineColorAndroid={Colors.fieldtBgColor}
                            style={{
                              backgroundColor: Colors.fieldtBgColor,
                              width: 210,
                              height: 40,
                              borderRadius: 5,
                              padding: 5,
                              marginVertical: 5,
                              borderWidth: 1,
                              borderColor: "#E5E5E5",
                              paddingLeft: 10,
                              fontFamily: "NunitoSans-Regular",
                              fontSize: 14,
                              marginLeft: 30,
                            }}
                            // placeholder={'50'}
                            placeholder={"Category Name"}
                            placeholderStyle={{
                              fontFamily: "NunitoSans-Regular",
                              fontSize: 14,
                            }}
                            //placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                            keyboardType={"default"}
                            onChangeText={(text) => {
                              setCategoryName(text);
                            }}
                            value={categoryName}
                          />
                        </View>

                        <View
                          style={{
                            flexDirection: "row",
                            flex: 1,
                            alignItems: "center",
                            justifyContent: "center",
                            //width: '50%',
                          }}
                        >
                          <Text
                            style={{
                              fontFamily: "NunitoSans-Regular",
                              fontSize: 14,
                              textAlign: "left",
                            }}
                          >
                            Do Not Display
                          </Text>

                          {/* <Text style={{ color: '#adadad', marginLeft: 170, fontSize: 16, }}>P.O.1134</Text> */}

                          <View
                            style={{
                              width: 200,
                              marginLeft: 30,
                              zIndex: 1000,
                            }}
                          >
                            {/* <DropDownPicker
                              containerStyle={{
                                height:  40,
                                zIndex: 2,
                              }}
                              arrowColor={'black'}
                              arrowSize={20}
                              arrowStyle={{ fontWeight: 'bold' }}
                              labelStyle={{
                                fontFamily: 'NunitoSans-Regular',
                                fontSize: 14,
                              }}
                              style={{
                                width: 230,
                                paddingVertical: 0,
                                backgroundColor: Colors.fieldtBgColor,
                                borderRadius: 10,
                              }}
                              placeholderStyle={{ color: Colors.fieldtTxtColor }}
                              itemStyle={{
                                justifyContent: 'flex-start',
                                marginLeft: 5,
                                zIndex: 2,
                              }}
                              items={[
                                {
                                  label: 'Dine In',
                                  value: ORDER_TYPE.DINEIN,
                                },
                                {
                                  label: 'Takeaway',
                                  value: ORDER_TYPE.PICKUP,
                                },
                                {
                                  label: 'Delivery',
                                  value: ORDER_TYPE.DELIVERY,
                                },
                              ]}
                              placeholder="Order Type"
                              multipleText={'%d type(s) selected'}
                              customTickIcon={(press) => (
                                <Ionicons
                                  name={'md-checkbox'}
                                  color={
                                    press
                                      ? Colors.fieldtBgColor
                                      : Colors.primaryColor
                                  }
                                  size={25}
                                />
                              )}
                              onChangeItem={(items) => {
                                setHideInOrderTypes(items);
                              }}
                              defaultValue={hideInOrderTypes}
                              multiple={true}
                              dropDownMaxHeight={150}
                              dropDownStyle={{
                                width: 230,
                                height: 90,
                                backgroundColor: Colors.fieldtBgColor,
                                borderRadius: 10,
                                borderWidth: 1,
                                textAlign: 'left',
                                zIndex: 2,
                                fontSize: 14,
                              }}
                            /> */}

                            <DropDownPicker
                              style={{
                                backgroundColor: Colors.fieldtBgColor,
                                width: 210,
                                height: 40,
                                borderRadius: 10,
                                borderWidth: 1,
                                borderColor: "#E5E5E5",
                                flexDirection: "row",
                              }}
                              dropDownContainerStyle={{
                                width: 210,
                                backgroundColor: Colors.fieldtBgColor,
                                borderColor: "#E5E5E5",
                              }}
                              labelStyle={{
                                marginLeft: 5,
                                flexDirection: "row",
                              }}
                              textStyle={{
                                fontSize: 14,
                                fontFamily: 'NunitoSans-Regular',

                                marginLeft: 5,
                                paddingVertical: 10,
                                flexDirection: "row",
                              }}
                              selectedItemContainerStyle={{
                                flexDirection: "row",
                              }}

                              showArrowIcon={true}
                              ArrowDownIconComponent={({ style }) => (
                                <Ionicon
                                  size={25}
                                  color={Colors.fieldtTxtColor}
                                  style={{ paddingHorizontal: 5, marginTop: 5 }}
                                  name="chevron-down-outline"
                                />
                              )}
                              ArrowUpIconComponent={({ style }) => (
                                <Ionicon
                                  size={25}
                                  color={Colors.fieldtTxtColor}
                                  style={{ paddingHorizontal: 5, marginTop: 5 }}
                                  name="chevron-up-outline"
                                />
                              )}

                              showTickIcon={true}
                              TickIconComponent={({ press }) => (
                                <Ionicon
                                  style={{ paddingHorizontal: 5, marginTop: 5 }}
                                  color={
                                    press ? Colors.fieldtBgColor : Colors.primaryColor
                                  }
                                  name={'md-checkbox'}
                                  size={25}
                                />
                              )}
                              items={[
                                {
                                  label: 'Dine In',
                                  value: ORDER_TYPE.DINEIN,
                                },
                                {
                                  label: 'Takeaway',
                                  value: ORDER_TYPE.PICKUP,
                                },
                                {
                                  label: 'Delivery',
                                  value: ORDER_TYPE.DELIVERY,
                                },
                              ]}
                              value={hideInOrderTypes}
                              placeholder="Order Type"
                              placeholderStyle={{
                                color: Colors.fieldtTxtColor,
                                // marginTop: 15,
                              }}
                              multipleText={`${hideInOrderTypes.length} type(s) selected`}
                              onSelectItem={(items) => {
                                setHideInOrderTypes(items.map(item => item.value));
                              }}
                              multiple={true}
                              open={openDND}
                              setOpen={setOpenDND}
                              dropDownDirection="BOTTOM"
                            />
                          </View>
                        </View>
                      </View>
                    </View>
                    <View
                      style={{
                        flexDirection: "row",
                        marginTop: 10,
                        justifyContent: "space-evenly",
                        marginTop: 50,
                        marginBottom: 40,
                        width: "90%",
                        alignSelf: "center",
                        marginLeft:
                          Dimensions.get("screen").width *
                          Styles.sideBarWidth *
                          0.5,
                      }}
                    >
                      <View
                        style={{
                          flexDirection: 'row',
                          flex: 1,
                          alignItems: 'center',
                          // justifyContent: 'space-around',
                          width: '50%',
                        }}>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Regular',
                            fontSize: switchMerchant ? 10 : 14,
                            width: '20%',
                            textAlign: 'left',
                          }}>
                          Available On
                        </Text>
                        <View
                          style={{
                            // width: 200,
                            // marginLeft: 30,
                            zIndex: 1000,
                          }}
                        >
                          <DropDownPicker
                            style={{
                              backgroundColor: Colors.fieldtBgColor,
                              width: 210,
                              height: 40,
                              borderRadius: 10,
                              borderWidth: 1,
                              borderColor: "#E5E5E5",
                              flexDirection: "row",
                            }}
                            dropDownContainerStyle={{
                              width: 210,
                              backgroundColor: Colors.fieldtBgColor,
                              borderColor: "#E5E5E5",
                            }}
                            labelStyle={{
                              marginLeft: 5,
                              flexDirection: "row",
                            }}
                            textStyle={{
                              fontSize: 14,
                              fontFamily: 'NunitoSans-Regular',
                              color: !isAvailableDayActive
                                ? Colors.descriptionColor
                                : Colors.blackColor,
                              marginLeft: 5,
                              paddingVertical: 10,
                              flexDirection: "row",
                            }}
                            selectedItemContainerStyle={{
                              flexDirection: "row",
                            }}

                            showArrowIcon={true}
                            ArrowDownIconComponent={({ style }) => (
                              <Ionicon
                                size={25}
                                color={Colors.fieldtTxtColor}
                                style={{ paddingHorizontal: 5, marginTop: 5 }}
                                name="chevron-down-outline"
                              />
                            )}
                            ArrowUpIconComponent={({ style }) => (
                              <Ionicon
                                size={25}
                                color={Colors.fieldtTxtColor}
                                style={{ paddingHorizontal: 5, marginTop: 5 }}
                                name="chevron-up-outline"
                              />
                            )}

                            showTickIcon={true}
                            TickIconComponent={({ press }) => (
                              <Ionicon
                                style={{ paddingHorizontal: 5, marginTop: 5 }}
                                color={
                                  press ? Colors.fieldtBgColor : Colors.primaryColor
                                }
                                name={'md-checkbox'}
                                size={25}
                              />
                            )}
                            items={EFFECTIVE_DAY_DROPDOWN_LIST1}
                            value={selectedEffectiveTypeOptions}
                            placeholder={'Monday'}
                            placeholderStyle={{
                              color: Colors.fieldtTxtColor,
                              // marginTop: 15,
                            }}
                            multiple={true}
                            multipleText={`${selectedEffectiveTypeOptions.length} day(s) selected`}
                            onSelectItem={(items) => {
                              setSelectedEffectiveTypeOptions(items.map(item => item.value));
                            }}
                            open={openAO}
                            setOpen={setOpenAO}
                            disabled={!isAvailableDayActive}
                            dropDownDirection="BOTTOM"
                          />
                        </View>

                        <View style={{ paddingLeft: 30 }}>
                          <TouchableOpacity
                            disabled={!isAvailableDayActive}
                            onPress={() => {
                              setShowEffectiveStartTimePicker(true);
                              setShowEffectiveEndTimePicker(false);
                            }}
                          >
                            <View style={{ flexDirection: "row" }}>
                              <Text
                                style={{
                                  // color: Colors.fieldtTxtColor,
                                  fontFamily: "NunitoSans-Regular",
                                  color: !isAvailableDayActive
                                    ? Colors.descriptionColor
                                    : Colors.blackColor,
                                  fontSize: 14,
                                }}
                              >
                                {moment(effectiveStartTime).format("hh:mm A")}
                              </Text>
                              {/* <MaterialIcons
                          name="keyboard-arrow-down"
                          size={switchMerchant ? 15 : 20}
                          style={{
                            color: !isAvailableDayActive
                              ? Colors.descriptionColor
                              : Colors.blackColor,
                            paddingLeft: 5,
                          }}
                        /> */}
                              <ArrowDown />
                            </View>
                          </TouchableOpacity>
                          {showEffectiveStartTimePicker ? (
                            <View
                              style={{
                                position: "absolute",
                                left: -70,
                                top: 25,
                                zIndex: 1000,
                              }}
                            >
                              <TimeKeeper
                                time={moment(effectiveStartTime).format(
                                  "hh:mm A"
                                )}
                                onChange={(time) => {
                                  setEffectiveStartTime(
                                    moment(
                                      `${moment(effectiveStartTime).format(
                                        "MM/DD/YYYY"
                                      )} ${time.formatted12}`
                                    )
                                  );
                                }}
                                onDoneClick={() => {
                                  setShowEffectiveStartTimePicker(false);
                                }}
                              ></TimeKeeper>
                            </View>
                          ) : null}
                        </View>

                        <View style={{ paddingLeft: 10 }}>
                          <TouchableOpacity
                            disabled={!isAvailableDayActive}
                            onPress={() => {
                              setShowEffectiveStartTimePicker(false);
                              setShowEffectiveEndTimePicker(true);
                            }}
                          >
                            <View style={{ flexDirection: "row" }}>
                              <Text
                                style={{
                                  // color: Colors.fieldtTxtColor,
                                  fontFamily: "NunitoSans-Regular",
                                  color: !isAvailableDayActive
                                    ? Colors.descriptionColor
                                    : Colors.blackColor,
                                  fontSize: 14,
                                }}
                              >
                                {moment(effectiveEndTime).format("hh:mm A")}
                              </Text>
                              {/* <MaterialIcons
                          name="keyboard-arrow-down"
                          size={switchMerchant ? 15 : 20}
                          style={{
                            color: !isAvailableDayActive
                              ? Colors.descriptionColor
                              : Colors.blackColor,
                            paddingLeft: 5,
                          }}
                        /> */}
                              <ArrowDown />
                            </View>
                          </TouchableOpacity>
                          {showEffectiveEndTimePicker ? (
                            <View
                              style={{
                                position: "absolute",
                                right: -60,
                                top: 25,
                                zIndex: 1003,
                              }}
                            >
                              <TimeKeeper
                                time={moment(effectiveEndTime).format(
                                  "hh:mm A"
                                )}
                                onChange={(time) => {
                                  setEffectiveEndTime(
                                    moment(
                                      `${moment(effectiveEndTime).format(
                                        "MM/DD/YYYY"
                                      )} ${time.formatted12}`
                                    )
                                  );
                                }}
                                onDoneClick={() => {
                                  setShowEffectiveEndTimePicker(false);
                                }}
                              ></TimeKeeper>
                            </View>
                          ) : null}
                        </View>

                        <View style={{ marginLeft: 20 }}>
                          {/* <Switch
                      width={42}
                      style={
                        {
                          //flexDirection: 'row',
                          //width: '15%',
                          // marginRight: 20,
                          // marginLeft: 20,
                          //transform: Platform.OS == 'ios' ? [{ scaleX: .6 }, { scaleY: .6 }] : [{ scaleX: .8 }, { scaleY: .8 }]
                          // bottom: -2,
                        }
                      }
                      // disabled={true}
                      value={isAvailableDayActive}
                      circleColorActive={Colors.primaryColor}
                      circleColorInactive={Colors.fieldtTxtColor}
                      backgroundActive="#dddddd"
                      onSyncPress={(value) => {
                        setIsAvailableDayActive(value);
                      }}
                    /> */}
                          <Switch
                            onChange={(value) => {
                              setIsAvailableDayActive(value);
                            }}
                            checked={isAvailableDayActive}
                            width={35}
                            height={20}
                            handleDiameter={30}
                            uncheckedIcon={false}
                            checkedIcon={false}
                            boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
                            activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
                          />
                        </View>
                      </View>

                      <View
                        style={{
                          flexDirection: "row",
                          flex: 0.5,
                          alignItems: "center",
                          justifyContent: "center",
                          //width: '50%',
                        }}
                      ></View>
                    </View>
                    {/* ////////////////////////////// */}
                    {/* 2023-3-2 - Tag */}
                    <View
                      style={{
                        flexDirection: "row",
                        marginTop: 10,
                        justifyContent: "space-evenly",
                        marginTop: 50,
                        marginBottom: 40,
                        width: "90%",
                        alignSelf: "center",
                        zIndex: -1,
                        marginLeft:
                          Dimensions.get("screen").width *
                          Styles.sideBarWidth *
                          0.5,
                      }}
                    >
                      <View
                        style={{
                          flexDirection: 'row',
                          flex: 1,
                          alignItems: 'center',
                          // justifyContent: 'space-around',
                          width: '50%',
                        }}>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Regular',
                            fontSize: switchMerchant ? 10 : 14,
                            width: '13.5%',
                            textAlign: 'left',
                          }}>
                          Tags:
                        </Text>
                        <View style={{ width: switchMerchant ? '40%' : '30%', }}>
                          {selectedOutletCategoryEdit &&
                            selectedOutletCategoryEdit.crmUserTagIdList &&
                            selectedOutletCategoryEdit.crmUserTagIdList.length > 0 ? (
                            <View
                              style={{
                                alignItems: 'baseline',
                                alignSelf: 'baseline',
                                flexDirection: 'row',
                                flexWrap: 'wrap',
                              }}>
                              {selectedOutletCategoryEdit &&
                                selectedOutletCategoryEdit.crmUserTagIdList &&
                                selectedOutletCategoryEdit.crmUserTagIdList.map(
                                  (userTagId, i) => {
                                    var tagText = 'N/A';

                                    if (crmUserTagsDict[userTagId]) {
                                      tagText = crmUserTagsDict[userTagId].name;
                                    }
                                    return (
                                      <View
                                        style={{
                                          alignItems: 'baseline',
                                          marginRight: 5,
                                          alignSelf: 'baseline',
                                          flexDirection: 'row',
                                          marginBottom: 5,
                                        }}>
                                        <Text
                                          style={{
                                            fontSize: switchMerchant ? 10 : 14,
                                            borderColor: 'green',
                                            borderWidth: 1,
                                            borderRadius: 5,
                                            fontWeight: '500',
                                            color: Colors.blackColor,
                                            padding: 3,
                                            alignItems: 'baseline',
                                          }}>
                                          {tagText}
                                        </Text>
                                      </View>
                                    );
                                  },
                                )}
                            </View>
                          ) : (
                            <Text
                              style={{
                                fontSize: switchMerchant ? 10 : 14,
                                // width: switchMerchant ? '60%' : '50%',
                                paddingBottom: 6,
                                color: Colors.descriptionColor,
                              }}>
                              No Tag Yet
                            </Text>
                          )}
                        </View>

                        <View style={{ marginLeft: switchMerchant ? 10 : 20 }}>
                          {selectedOutletCategoryEdit !== null ? (
                            <TouchableOpacity
                              style={{
                                marginLeft: 8,
                                alignSelf: 'flex-start',
                                zIndex: 999,
                              }}
                              onPress={() => {
                                setTagModal(true);
                              }}>
                              <AntDesign
                                name="tagso"
                                size={switchMerchant ? 17 : 24}
                                style={{ color: Colors.primaryColor }}
                              />
                            </TouchableOpacity>
                          ) : (
                            <></>
                          )}
                        </View>
                      </View>
                    </View>
                    {/* ////////////////////////////// */}

                    {/* 2022-11-10 - Printer area */}

                    {/* {
                        printerAreaDropdownList.length > 0 ||
                          (selectedPrinterAreaList.length > 0 &&
                            printerAreaDropdownList.includes(
                              selectedPrinterAreaList[0],
                            ))
                          ?
                          ( */}
                    <View
                      style={{
                        flexDirection: "row",
                        marginTop: 10,
                        justifyContent: "space-evenly",
                        marginTop: 50,
                        marginBottom: 40,
                        width: "90%",
                        alignSelf: "center",
                        marginLeft:
                          Dimensions.get("screen").width *
                          Styles.sideBarWidth *
                          0.5,
                      }}
                    >
                      <View
                        style={{
                          flexDirection: 'row',
                          flex: 1,
                          alignItems: 'center',
                          // justifyContent: 'space-around',
                          width: '50%',
                        }}>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Regular',
                            fontSize: switchMerchant ? 10 : 14,
                            width: '13%',
                            textAlign: 'left',
                          }}>
                          Printer Area
                        </Text>
                        <View
                          style={{
                            // width: '50%',
                            zIndex: 1000,
                          }}>
                          {/* {(
                            selectedPrinterAreaList.every((val) =>
                              printerAreaDropdownList
                                .map((area) => area.value)
                                .includes(val)
                            )
                            ||
                            selectedPrinterAreaList.length === 0
                          ) ? ( */}
                          <DropDownPicker
                            style={{
                              backgroundColor: Colors.fieldtBgColor,
                              width: 210,
                              height: 40,
                              borderRadius: 10,
                              borderWidth: 1,
                              borderColor: "#E5E5E5",
                              flexDirection: "row",
                            }}
                            dropDownContainerStyle={{
                              width: 210,
                              backgroundColor: Colors.fieldtBgColor,
                              borderColor: "#E5E5E5",
                            }}
                            labelStyle={{
                              marginLeft: 5,
                              flexDirection: "row",
                            }}
                            textStyle={{
                              fontSize: 14,
                              fontFamily: 'NunitoSans-Regular',

                              marginLeft: 5,
                              paddingVertical: 10,
                              flexDirection: "row",
                            }}
                            selectedItemContainerStyle={{
                              flexDirection: "row",
                            }}
                            showArrowIcon={true}
                            ArrowDownIconComponent={({ style }) => (
                              <Ionicon
                                size={25}
                                color={Colors.fieldtTxtColor}
                                style={{ paddingHorizontal: 5, marginTop: 5 }}
                                name="chevron-down-outline"
                              />
                            )}
                            ArrowUpIconComponent={({ style }) => (
                              <Ionicon
                                size={25}
                                color={Colors.fieldtTxtColor}
                                style={{ paddingHorizontal: 5, marginTop: 5 }}
                                name="chevron-up-outline"
                              />
                            )}
                            showTickIcon={true}
                            TickIconComponent={({ press }) => (
                              <Ionicon
                                style={{ paddingHorizontal: 5, marginTop: 5 }}
                                color={
                                  press ? Colors.fieldtBgColor : Colors.primaryColor
                                }
                                name={'md-checkbox'}
                                size={25}
                              />
                            )}
                            placeholder="Choose Printer Area"
                            multipleText={`${selectedPrinterAreaList.length} printer area(s) selected`}
                            placeholderStyle={{
                              color: Colors.fieldtTxtColor,
                              // marginTop: 15,
                            }}
                            items={printerAreaDropdownList}
                            value={selectedPrinterAreaList}
                            onSelectItem={(items) => {
                              setSelectedPrinterAreaList(items.map(item => item.value));
                            }}
                            open={openPA}
                            setOpen={setOpenPA}
                            multiple={true}
                            dropDownDirection="BOTTOM"
                          />
                          {/* ) : (
                            <></>
                          )} */}
                        </View>
                      </View>
                    </View>
                    {/* )
                          :
                          <></>
                      } */}

                    <View
                      style={{
                        flexDirection: "row",
                        marginTop: 10,
                        justifyContent: "space-evenly",
                        marginTop: 50,
                        marginBottom: 40,
                        width: "90%",
                        alignSelf: "center",
                        marginLeft:
                          Dimensions.get("screen").width *
                          Styles.sideBarWidth *
                          0.5,
                        zIndex: -1,
                      }}
                    >
                      <View
                        style={{
                          flexDirection: 'row',
                          flex: 1,
                          alignItems: 'center',
                          // justifyContent: 'space-around',
                          width: '50%',
                        }}>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Regular',
                            fontSize: switchMerchant ? 10 : 14,
                            width: '26.5%',
                            textAlign: 'left',
                          }}>
                          Print KD Times
                        </Text>

                        {/* <Text style={{ color: '#adadad', marginLeft: 170, fontSize: 16, }}>P.O.1134</Text> */}

                        <View
                          style={{
                            width: '50%',
                            zIndex: 1000,
                          }}>
                          <DropDownPicker
                            style={{
                              backgroundColor: Colors.fieldtBgColor,
                              width: 210,
                              height: 40,
                              borderRadius: 10,
                              borderWidth: 1,
                              borderColor: "#E5E5E5",
                              flexDirection: "row",
                            }}
                            dropDownContainerStyle={{
                              width: 210,
                              backgroundColor: Colors.fieldtBgColor,
                              borderColor: "#E5E5E5",
                            }}
                            labelStyle={{
                              marginLeft: 5,
                              flexDirection: "row",
                            }}
                            textStyle={{
                              fontSize: 14,
                              fontFamily: 'NunitoSans-Regular',

                              marginLeft: 5,
                              paddingVertical: 10,
                              flexDirection: "row",
                            }}
                            selectedItemContainerStyle={{
                              flexDirection: "row",
                            }}
                            showArrowIcon={true}
                            ArrowDownIconComponent={({ style }) => (
                              <Ionicon
                                size={25}
                                color={Colors.fieldtTxtColor}
                                style={{ paddingHorizontal: 5, marginTop: 5 }}
                                name="chevron-down-outline"
                              />
                            )}
                            ArrowUpIconComponent={({ style }) => (
                              <Ionicon
                                size={25}
                                color={Colors.fieldtTxtColor}
                                style={{ paddingHorizontal: 5, marginTop: 5 }}
                                name="chevron-up-outline"
                              />
                            )}
                            showTickIcon={true}
                            TickIconComponent={({ press }) => (
                              <Ionicon
                                style={{ paddingHorizontal: 5, marginTop: 5 }}
                                color={
                                  press ? Colors.fieldtBgColor : Colors.primaryColor
                                }
                                name={'md-checkbox'}
                                size={25}
                              />
                            )}
                            placeholder="Print Times"
                            // multipleText={`${printKDNum.length} printer area(s) selected`}
                            placeholderStyle={{
                              color: Colors.fieldtTxtColor,
                              // marginTop: 15,
                            }}
                            items={[
                              {
                                label: '1',
                                value: 1,
                              },
                              {
                                label: '2',
                                value: 2,
                              },
                              {
                                label: '3',
                                value: 4,
                              },
                              {
                                label: '5',
                                value: 6,
                              },
                              {
                                label: '7',
                                value: 7,
                              },
                              {
                                label: '8',
                                value: 8,
                              },
                              {
                                label: '9',
                                value: 9,
                              },
                              {
                                label: '10',
                                value: 10,
                              },
                            ]}
                            value={printKDNum}
                            onSelectItem={(items) => {
                              setPrintKDNum(items.value);
                            }}
                            open={openPKDT}
                            setOpen={setOpenPKDT}
                            // multiple={true}
                            dropDownDirection="BOTTOM"
                          />
                        </View>
                      </View>

                      <View
                        style={{
                          flexDirection: 'row',
                          flex: 1,
                          alignItems: 'center',
                          width: '50%',
                        }}>
                      </View>
                    </View>

                    {
                      outletSectionDropdownList.length > 0
                        ?
                        (
                          <View
                            style={{
                              flexDirection: 'row',
                              marginTop: 10,
                              justifyContent: 'space-evenly',
                              marginTop: 50,
                              marginBottom: 40,
                              width: '90%',
                              alignSelf: 'center',
                              marginLeft: windowWidth * Styles.sideBarWidth * 0.5,

                              zIndex: -2,
                            }}>
                            <View
                              style={{
                                flexDirection: 'row',
                                flex: 1,
                                alignItems: 'center',
                                // justifyContent: 'space-around',
                                width: '50%',
                              }}>
                              <Text
                                style={{
                                  fontFamily: 'NunitoSans-Regular',
                                  fontSize: switchMerchant ? 10 : 14,
                                  width: '13%',
                                  textAlign: 'left',
                                }}>
                                Hide For Zone
                              </Text>
                              <View
                                style={{
                                  width: '50%',
                                  zIndex: 1000,
                                }}>
                                {(
                                  selectedHideOutletSectionIdList.every((val) =>
                                    outletSectionDropdownList
                                      .map((area) => area.value)
                                      .includes(val)
                                  )
                                  ||
                                  selectedHideOutletSectionIdList.length === 0
                                ) ? (
                                  <DropDownPicker
                                    style={{
                                      backgroundColor: Colors.fieldtBgColor,
                                      width: 185,
                                      height: 40,
                                      borderRadius: 10,
                                      borderWidth: 1,
                                      borderColor: "#E5E5E5",
                                      flexDirection: "row",
                                    }}
                                    dropDownContainerStyle={{
                                      width: 185,
                                      backgroundColor: Colors.fieldtBgColor,
                                      borderColor: "#E5E5E5",
                                    }}
                                    labelStyle={{
                                      marginLeft: 5,
                                      flexDirection: "row",
                                    }}
                                    textStyle={{
                                      fontSize: 14,
                                      fontFamily: 'NunitoSans-Regular',

                                      marginLeft: 5,
                                      paddingVertical: 10,
                                      flexDirection: "row",
                                    }}
                                    selectedItemContainerStyle={{
                                      flexDirection: "row",
                                    }}

                                    showArrowIcon={true}
                                    ArrowDownIconComponent={({ style }) => (
                                      <Ionicon
                                        size={25}
                                        color={Colors.fieldtTxtColor}
                                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                                        name="chevron-down-outline"
                                      />
                                    )}
                                    ArrowUpIconComponent={({ style }) => (
                                      <Ionicon
                                        size={25}
                                        color={Colors.fieldtTxtColor}
                                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                                        name="chevron-up-outline"
                                      />
                                    )}

                                    showTickIcon={true}
                                    TickIconComponent={({ press }) => (
                                      <Ionicon
                                        style={{ paddingHorizontal: 5, marginTop: 5 }}
                                        color={
                                          press ? Colors.fieldtBgColor : Colors.primaryColor
                                        }
                                        name={'md-checkbox'}
                                        size={25}
                                      />
                                    )}
                                    placeholderStyle={{
                                      color: Colors.fieldtTxtColor,
                                      // marginTop: 15,
                                    }}
                                    onSelectItem={(items) => {
                                      setSelectedUserTagList(items.map(item => item.value));
                                    }}
                                    multiple={true}
                                    open={openSection}
                                    setOpen={setOpenSection}
                                    items={outletSectionDropdownList}
                                    placeholder="Zone(s) to hide"
                                    multipleText={'%d zone(s) selected'}
                                    onChangeItem={(items) => {
                                      setSelectedHideOutletSectionIdList(items);
                                    }}
                                    value={selectedHideOutletSectionIdList}
                                  />
                                ) : (
                                  <></>
                                )}
                              </View>
                            </View>
                          </View>
                        )
                        :
                        <></>
                    }

                    <View
                      style={{
                        flexDirection: 'row',
                        marginTop: 10,
                        justifyContent: 'space-evenly',
                        marginTop: 50,
                        marginBottom: 200,
                        width: '90%',
                        alignSelf: 'center',
                        marginLeft: windowWidth * Styles.sideBarWidth * 0.5,
                        zIndex: -3,
                      }}>
                      <View
                        style={{
                          flexDirection: 'row',
                          flex: 1,
                          alignItems: 'center',
                          // justifyContent: 'space-around',
                          width: '50%',
                        }}>
                        <Text
                          style={{
                            fontFamily: 'NunitoSans-Regular',
                            fontSize: switchMerchant ? 10 : 14,
                            width: '13%',
                            textAlign: 'left',
                          }}>
                          {'Excluding From\nManual Discount'}
                        </Text>
                        <View
                          style={{
                            width: '50%',
                            zIndex: 1000,
                          }}>
                          <View
                            style={{
                              flexDirection: 'row',
                              flex: 1,
                              alignItems: 'center',
                            }}>
                            <Switch
                              onChange={(value) => {
                                setNoManualDisc(value);
                              }}
                              checked={noManualDisc}
                              width={35}
                              height={20}
                              handleDiameter={30}
                              uncheckedIcon={false}
                              checkedIcon={false}
                              boxShadow="0px 1px 5px rgba(0, 0, 0, 0.6)"
                              activeBoxShadow="0px 0px 1px 10px rgba(0, 0, 0, 0.2)"
                            />
                          </View>
                        </View>
                      </View>
                    </View>
                    {/* <View
                      style={{
                        flexDirection: "row",
                        backgroundColor: "#ffffff",
                        justifyContent: "center",
                        padding: 18,
                        height: 100,
                      }}
                    ></View> */}
                  </ScrollView>
                </View>
              </View>
            ) : null}

            {editPurchase ? (
              <View style={{ height: Dimensions.get("window").height - 200 }}>
                <View>
                  <ScrollView>
                    <View
                      style={{ borderBottomWidth: StyleSheet.hairlineWidth }}
                    >
                      <View>
                        <Text
                          style={{
                            alignSelf: "center",
                            marginTop: 30,
                            fontSize: 40,
                            fontWeight: "bold",
                          }}
                        >
                          Edit Purchase Order
                        </Text>
                        <Text
                          style={{
                            alignSelf: "center",
                            fontSize: 16,
                            color: "#adadad",
                          }}
                        >
                          Edit your purchase order information
                        </Text>
                      </View>

                      <View
                        style={{
                          flexDirection: "row",
                          marginTop: 10,
                          justifyContent: "space-evenly",
                          marginTop: 50,
                        }}
                      >
                        <View style={{ flexDirection: "row", flex: 1 }}>
                          <Text style={{ fontSize: 16, marginLeft: 80 }}>
                            P.O.ID
                          </Text>
                          <Text
                            style={{
                              color: "#adadad",
                              marginLeft: 170,
                              fontSize: 16,
                            }}
                          >
                            P.O.1134
                          </Text>
                        </View>
                        <View style={{ flexDirection: "row", flex: 1 }}>
                          <Text style={{ fontSize: 16, marginLeft: 80 }}>
                            Supplier
                          </Text>
                          <Text
                            style={{
                              color: "#adadad",
                              marginLeft: 100,
                              fontSize: 16,
                            }}
                          >
                            My Burgers Enterprise
                          </Text>
                        </View>
                      </View>

                      <View
                        style={{
                          flexDirection: "row",
                          marginTop: 10,
                          justifyContent: "space-evenly",
                          marginTop: 50,
                        }}
                      >
                        <View style={{ flexDirection: "row", flex: 1 }}>
                          <Text style={{ fontSize: 16, marginLeft: 80 }}>
                            Current status
                          </Text>
                          <View
                            style={{
                              paddingHorizontal: 18,
                              paddingVertical: 10,
                              alignItems: "center",
                              backgroundColor: "#838387",
                              borderRadius: 10,
                              marginLeft: 100,
                            }}
                          >
                            <Text style={{ color: Colors.whiteColor }}>
                              Partially received
                            </Text>
                          </View>
                        </View>
                        <View style={{ flexDirection: "row", flex: 1 }}>
                          <Text style={{ fontSize: 16, marginLeft: 80 }}>
                            Target Store
                          </Text>
                          <Text
                            style={{
                              color: "#adadad",
                              marginLeft: 70,
                              fontSize: 16,
                            }}
                          >
                            MyBurgerlab (Seapark)
                          </Text>
                        </View>
                      </View>

                      <View
                        style={{
                          flexDirection: "row",
                          marginTop: 10,
                          justifyContent: "space-evenly",
                          marginTop: 50,
                          marginBottom: 40,
                        }}
                      >
                        <View style={{ flexDirection: "row", flex: 1 }}>
                          <Text style={{ fontSize: 16, marginLeft: 80 }}>
                            Estimated Arrival Time
                          </Text>
                          <Text
                            style={{
                              color: "#adadad",
                              marginLeft: 50,
                              fontSize: 16,
                            }}
                          >
                            1/10/2020
                          </Text>
                        </View>
                        <View style={{ flexDirection: "row", flex: 1 }}>
                          <Text style={{ fontSize: 16, marginLeft: 80 }}>
                            Order Date
                          </Text>
                          <Text
                            style={{
                              color: "#adadad",
                              marginLeft: 80,
                              fontSize: 16,
                            }}
                          >
                            5/10/2020
                          </Text>
                        </View>
                      </View>
                    </View>

                    <View>
                      <Text
                        style={{
                          alignSelf: "center",
                          marginTop: 30,
                          fontSize: 25,
                          fontWeight: "bold",
                        }}
                      >
                        Items to order
                      </Text>
                    </View>

                    <View
                      style={{
                        backgroundColor: "#ffffff",
                        flexDirection: "row",
                        paddingVertical: 20,
                        paddingHorizontal: 20,
                        marginTop: 10,
                        borderBottomWidth: StyleSheet.hairlineWidth,
                      }}
                    >
                      <Text style={{ width: "8%" }}></Text>
                      <Text style={{ width: "14%", alignSelf: "center" }}>
                        Product name
                      </Text>
                      <Text style={{ width: "16%", alignSelf: "center" }}>
                        SKU
                      </Text>
                      <Text style={{ width: "14%", alignSelf: "center" }}>
                        Ordered qty
                      </Text>
                      <Text style={{ width: "16%", alignSelf: "center" }}>
                        Received qty
                      </Text>
                      <Text style={{ width: "18%", alignSelf: "center" }}>
                        Supplier Price{" "}
                      </Text>
                      <Text style={{ width: "16%", alignSelf: "center" }}>
                        Total (RM)
                      </Text>
                    </View>
                    <FlatList
                      data={itemsToOrder}
                      extraData={itemsToOrder}
                      //renderItem={renderItemsToOrder}
                      keyExtractor={(item, index) => String(index)}
                    />

                    <View style={{ flexDirection: "row" }}>
                      <View>
                        <TouchableOpacity
                          style={styles.submitText2}
                          onPress={() => {
                            // add one row

                            setSupplierItems([
                              ...supplierItems,
                              {
                                supplyItemId: "",
                                name: "",
                                sku: "",
                                unit: "",
                                price: 0,
                              },
                            ]);
                          }}
                        >
                          <View style={{ flexDirection: "row" }}>
                            <Icon1
                              name="plus-circle"
                              size={20}
                              color={Colors.primaryColor}
                            />
                            <Text
                              style={{
                                marginLeft: 10,
                                color: Colors.primaryColor,
                              }}
                            >
                              Add supply item slot
                            </Text>
                          </View>
                        </TouchableOpacity>
                      </View>
                    </View>

                    <View
                      style={{
                        flexDirection: "row",
                        alignSelf: "center",
                        justifyContent: "space-evenly",
                        marginTop: 20,
                      }}
                    >
                      <View
                        style={{
                          backgroundColor: Colors.primaryColor,
                          width: 200,
                          height: 40,
                          marginVertical: 15,
                          borderRadius: 5,
                          alignSelf: "center",
                        }}
                      >
                        <TouchableOpacity
                        //onPress={() => { editStockOrder() }}
                        >
                          <Text
                            style={{
                              color: Colors.whiteColor,
                              alignSelf: "center",
                              marginVertical: 10,
                            }}
                          >
                            SAVE
                          </Text>
                        </TouchableOpacity>
                      </View>
                      <View
                        style={{
                          backgroundColor: Colors.primaryColor,
                          width: 200,
                          height: 40,
                          marginVertical: 15,
                          borderRadius: 5,
                          alignSelf: "center",
                          marginLeft: 40,
                        }}
                      >
                        <TouchableOpacity onPress={() => { }}>
                          <Text
                            style={{
                              color: Colors.whiteColor,
                              alignSelf: "center",
                              marginVertical: 10,
                            }}
                          >
                            SAVE & SEND
                          </Text>
                        </TouchableOpacity>
                      </View>
                    </View>

                    <View
                      style={{
                        flexDirection: "row",
                        backgroundColor: "#ffffff",
                        justifyContent: "center",
                        padding: 18,
                      }}
                    >
                      <View style={{ alignItems: "center" }}>
                        <Text style={{ fontSize: 30, fontWeight: "bold" }}>
                          {outletCategories.length}
                        </Text>
                        <Text>CATEGORIES</Text>
                      </View>
                    </View>
                  </ScrollView>
                </View>
              </View>
            ) : null}
          </View>
        </ScrollView>
      </View>
    </View>
    // </UserIdleWrapper>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.highlightColor,
    flexDirection: "row",
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  list1: {
    backgroundColor: Colors.whiteColor,
    height: Dimensions.get("screen").height * 0.7,
    width: Dimensions.get("screen").width * 0.87,
    marginTop: 30,
    marginHorizontal: 30,
    marginBottom: 30,
    alignSelf: "center",
    borderRadius: 5,
    shadowOpacity: 0,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  list1_PhoneAdjustment: {
    backgroundColor: Colors.whiteColor,
    minHeight: Dimensions.get("screen").height * 0.01,
    width: Dimensions.get("screen").width * 0.79,
    marginTop: 30,
    //marginHorizontal: 30,
    marginBottom: 30,
    //alignSelf: 'center',
    borderRadius: 5,
    shadowOpacity: 0,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
  },
  ContainerList: {
    backgroundColor: Colors.whiteColor,
    width: Dimensions.get("screen").width * 0.87,
    minHeight: Dimensions.get("screen").height * 0.01,
    borderRadius: 5,
    //padding: Dimensions.get('screen').width * 0.03,
    alignItems: "center",
    justifyContent: "center",
    marginTop: 30,
    marginHorizontal: 30,
    marginBottom: 30,
    shadowOpacity: 0,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
  },
  list: {
    paddingVertical: 20,
    paddingHorizontal: 30,
    flexDirection: "row",
    alignItems: "center",
  },
  listItem: {
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  sidebar: {
    width: Dimensions.get("screen").width * Styles.sideBarWidth,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 8,
    },
    shadowOpacity: 0.44,
    shadowRadius: 10.32,

    elevation: 16,
  },
  content: {
    padding: 16,
    width: Dimensions.get("screen").width * (1 - Styles.sideBarWidth),
  },
  submitText: {
    height: 40,
    paddingVertical: 5,
    paddingHorizontal: 10,
    flexDirection: "row",
    color: "#4cd964",
    textAlign: "center",
    borderRadius: 10,
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    justifyContent: "center",
    alignContent: "center",
    alignItems: "center",
    marginRight: 10,
  },
  submitText1: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    flexDirection: "row",
    color: "#4cd964",
    textAlign: "center",
    borderRadius: 10,
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    alignSelf: "flex-end",
    marginRight: 20,
    marginTop: 15,
  },
  submitText2: {
    paddingVertical: 10,
    paddingHorizontal: 20,
    flexDirection: "row",
    color: "#4cd964",
    textAlign: "center",
    alignSelf: "flex-end",
    marginRight: 20,
    marginTop: 15,
  },
  /* textInput: {
    width: 300,
    height: '10%',
    padding: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    borderRadius: 5,
    paddingTop: 20,
  }, */

  textInput: {
    width: 200,
    height: 40,
    // padding:5,
    backgroundColor: Colors.whiteColor,
    borderRadius: 0,
    marginRight: "35%",
    flexDirection: "row",
    alignContent: "center",
    alignItems: "flex-end",

    shadowOffset: {
      width: 0,
      height: 7,
    },
    shadowOpacity: 0.43,
    shadowRadius: 0.51,
    elevation: 15,
  },
  searchIcon: {
    backgroundColor: Colors.whiteColor,
    height: 40,
    padding: 10,
    shadowOffset: {
      width: 0,
      height: 7,
    },
    shadowOpacity: 0.43,
    shadowRadius: 9.51,

    elevation: 15,
  },
  textInput1: {
    width: 160,
    padding: 5,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    borderRadius: 5,
    paddingTop: 5,
  },
  textInput2: {
    width: 100,
    padding: 5,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    borderRadius: 5,
    paddingTop: 5,
    textAlign: "center",
  },
  confirmBox: {
    width: 450,
    height: 450,
    borderRadius: 12,
    backgroundColor: Colors.whiteColor,
  },
  textFieldInput: {
    height: 80,
    width: 900,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    borderRadius: 5,
  },
  circleIcon: {
    width: 30,
    height: 30,
    // resizeMode: 'contain',
    marginRight: 10,
    alignSelf: "center",
  },
  headerLeftStyle: {
    width: Dimensions.get("screen").width * 0.17,
    justifyContent: "center",
    alignItems: "center",
  },
  // modalView: {
  //   height: Dimensions.get('screen').width * 0.22,
  //   width: Dimensions.get('screen').width * 0.48,
  //   backgroundColor: Colors.whiteColor,
  //   borderRadius: Dimensions.get('screen').width * 0.03,
  //   padding: Dimensions.get('screen').width * 0.03,
  //   paddingHorizontal: Dimensions.get('screen').width * 0.02,
  //   alignItems: 'center',
  //   justifyContent: 'space-between',
  modalContainer: {
    flex: 1,
    backgroundColor: Colors.modalBgColor,
    alignItems: "center",
    justifyContent: "center",
  },
  modalView: {
    height: Dimensions.get("screen").width * 0.2,
    width: Dimensions.get("screen").width * 0.3,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: Dimensions.get("screen").width * 0.03,
    alignItems: "center",
    justifyContent: "center",
  },
  modalView1: {
    height: Dimensions.get("screen").width * 0.25,
    width: Dimensions.get("screen").width * 0.4,
    backgroundColor: Colors.whiteColor,
    borderRadius: 12,
    padding: Dimensions.get("screen").width * 0.035,
    paddingTop: Dimensions.get("screen").width * 0.05,
    alignItems: "center",
    justifyContent: "space-between",
  },
  closeButton: {
    position: "absolute",
    right: Dimensions.get("screen").width * 0.02,
    top: Dimensions.get("screen").width * 0.02,

    elevation: 1000,
    zIndex: 1000,
  },
  modalTitle: {
    alignItems: "center",
    top: "20%",
    position: "absolute",
  },
  modalTitleText: {
    fontFamily: "NunitoSans-Bold",
    textAlign: "center",
    fontSize: 20,
  },
  modalDescText: {
    fontFamily: "NunitoSans-SemiBold",
    fontSize: 18,
    color: Colors.fieldtTxtColor,
  },
  modalSaveButton: {
    width: Dimensions.get("screen").width * 0.15,
    backgroundColor: Colors.fieldtBgColor,
    height: 40,
    alignItems: "center",
    justifyContent: "center",
    borderRadius: 8,

    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 1,

    marginVertical: 10,
  },
  tagModalView: {
    backgroundColor: Colors.whiteColor,
    borderRadius: Dimensions.get('window').width * 0.03,
    padding: 20,
    paddingTop: 25,
  },
  textInput4: {
    borderRadius: 10,
  },
});
export default ProductCategoryScreen;
