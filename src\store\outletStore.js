import { Store } from 'pullstate';

export const OutletStore = new Store({
    outletItems: [],

    sortedOutletItems: [],

    outletItemsDict: {},
    outletItemsSkuDict: {},
    outletCategories: [],
    outletCategoriesDict: {},

    allOutletsItems: [],
    allOutletsItemsSkuDict: {},
    allOutletsCategories: [],
    allOutletsCategoriesNameDict: {},
    allOutletsCategoriesDict: {},

    allOutletsCategoriesUnique: [],

    outletSections: [],
    outletSectionsDict: {},

    outletTables: [],
    outletTablesDict: {},
    outletTablesCodeDict: {},

    outletTableCombinations: [],
    outletTableCombinationsDict: {},

    userOrders: [],
    userOrdersDict: {}, // { user_order_id -> user_order, ... }
    userOrdersTableDict: {}, // { outlet_table_id -> [ user_order, .... ], ... }

    userOrdersAllStatus: [],
    userOrdersPrinting: [],
    userOrdersPrintingTimestamp: Date.now(),

    ///////////////////////////////////

    userOrdersNormal: [],
    userOrdersTableDictNormal: {},
    userOrdersPrintingNormal: [],

    userOrdersReservation: [],
    userOrdersTableDictReservation: {},
    userOrdersPrintingReservation: [],

    ///////////////////////////////////

    userReservations: [],
    userReservationsDict: {},
    userReservationsUserIdDict: {},

    userReservationsWaitList: [],
    userReservationsWaitListDict: {},
    userReservationsWaitListUserIdDict: {},

    userQueues: [],
    userQueuesDict: {},
    userRings: [],
    userRingsDict: {},

    allOutletUserOrderDoneProcessed: [],

    allOutletsUserOrdersDone: [],
    allOutletsUserOrders: [],

    allOutletsUserOrdersDoneRealTime: [],
    allOutletsUserOrdersRealTime: [],
    allOutletsUserOrdersDoneCache: [],
    allOutletsUserOrdersCache: [],

    allOutletsUserOrdersLoyaltyDone: [],
    allOutletsUserOrdersLoyalty: [],

    allOutletsUserOrdersLoyaltyDoneRealTime: [],
    allOutletsUserOrdersLoyaltyRealTime: [],
    allOutletsUserOrdersLoyaltyDoneCache: [],
    allOutletsUserOrdersLoyaltyCache: [],

    userOrdersExpandedDict: {}, // { user_order_id -> true, ... } | used for KitchenOrder
    // userORdersExpandedFooterDict: {}, // { user_order_id -> true, ... } | used for KitchenScreen, footer

    currOutletShift: {},
    currOutletShiftStatus: 'CLOSED',

    allOutletShifts: [],
    reportOutletShifts: [],

    allOutletsEmployees: [],
    allOutletsEmployeesUserActionsDict: {},

    outletsOpeningDict: {},

    beerDocketCategories: [],
    beerDocketCategoriesDict: {},

    beerDockets: [],
    beerDocketsDict: {},

    userBeerDockets: [],

    userOrderBeerDocketUBDIdDict: {},

    promotions: [],
    promotionsDict: {},

    linkedMerchantIdUsers: [],
    linkedOutletFavoriteUsers: [],
    favoriteMerchantIdUsers: [],
    crmUsersRaw: [],
    crmUsers: [],
    crmUsersDict: {},
    crmUserTags: [],
    crmUserTagsDict: {},
    crmSegments: [],

    crmNextBatch: [],
    crmLastDoc: [],
    lastDocArr: [],

    filteredCRMList: [],

    selectedCustomerOrdersUserId: [],
    selectedCustomerDineInOrdersUserId: [],
    selectedCustomerOrdersPhone: [],
    selectedCustomerDineInOrdersPhone: [],

    selectedCustomerOrders: [],
    selectedCustomerDineInOrders: [],

    selectedCustomerAddresses: [],
    // selectedCustomerPointsTransactions: [],
    // selectedCustomerPointsBalance: 0,
    selectedCustomerReservations: [],
    //selectedCustomerVouchers: [],
    selectedCustomerVoucherRedemptions: [],
    selectedCustomerUserBeerDockets: [],

    selectedCustomerUserBeerDocketsEmail: [],
    selectedCustomerUserBeerDocketsUserId: [],

    selectedCustomerCashbackAmount: 0,

    selectedCustomerLCCTransactions: [], // loyalty campaign credit transactions
    selectedCustomerLCCBalance: 0, // loyalty campaign credit balance

    selectedCustomerLCCTransactionsEmail: [], // loyalty campaign credit transactions
    selectedCustomerLCCBalanceEmail: 0, // loyalty campaign credit balance
    selectedCustomerLCCTransactionsPhone: [], // loyalty campaign credit transactions
    selectedCustomerLCCBalancePhone: 0, // loyalty campaign credit balance
    // selectedCustomerLCCTransactionsUserId: [], // loyalty campaign credit transactions
    // selectedCustomerLCCBalanceUserId: 0, // loyalty campaign credit balance

    selectedCustomerPointsTransactions: [],
    selectedCustomerPointsBalance: 0,
    selectedCustomerPointsTransactionsEmail: [],
    selectedCustomerPointsBalanceEmail: 0,
    selectedCustomerPointsTransactionsPhone: [],
    selectedCustomerPointsBalancePhone: 0,

    selectedCustomerUserLoyaltyCampaigns: [],

    selectedCustomerUserTaggableVouchers: [],
    selectedCustomerUserTaggableVouchersView: [],

    selectedCustomerUserLoyaltyStamps: [],

    preorderPackages: [],

    pointsRedeemPackages: [],
    pointsRedeemPackagesDict: {},

    outletPrinters: [],
    sunmiPrinters: [],
    iminPrinters: [],
    blePrinters: [],
    usbPrinters: [],

    outletPaymentMethods: [],

    loyaltyStamps: [],
    loyaltyStampsType: [],

    loyaltyCampaigns: [],
    availableLoyaltyCampaigns: [],

    upsellingCampaigns: [],

    loyaltyTier: {},

    currTableQRUrl: '',
    currOrderRegisterUrl: '',

    taggableVouchers: [],
    availableTaggableVouchers: [],

    employeeClockDict: {},

    userTaggableVouchers: [],

    /////////////////////////////////////////

    selectedCustomerApplicableVoucherIdList: [],

    /////////////////////////////////////////

    topupCreditTypes: [],

    /////////////////////////////////////////

    // currMerchantPayment: {
    //     uniqueId: '',

    //     outletId: '',
    //     merchantId: '',

    //     merchantName: '',
    //     merchantEmail: '',
    //     companyName: '',
    //     companyRegistrationNo: '',
    //     companyAddress: '',
    //     directorName: '',
    //     startDate: Date.now(),

    //     email: '',
    //     name: '',
    //     userId: '',

    //     agreementId: '',
    //     attachmentList: [],

    //     appliedVoucherCode: '',

    //     invoiceAmount: '',
    //     sstAmount: '',
    //     invoiceAndSSTAmount: '',
    //     invoiceAmountRaw: 100,
    //     sstAmountRaw: 10,
    //     invoiceAndSSTAmountRaw: 110,
    //     processingFee: 6,

    //     ///////////////////////////////////////////////////

    //     invoiceCount: 1,

    //     ///////////////////////////////////////////////////

    //     // 2022-10-26 - new fields

    //     accountEmail: '',
    //     accountPhone: '',
    //     processingFeeType: 'QR_SALES',

    //     invoiceAmountOriginal: '',
    //     sstAmountOriginal: '',
    //     invoiceAndSSTAmountOriginal: 'invoiceAndSSTAmountOriginal',
    //     invoiceAmountRawOriginal: 200,
    //     sstAmountRawOriginal: 20,
    //     invoiceAndSSTAmountRawOriginal: 220,
    //     processingFeeOriginal: 10,

    //     toGenerateVoucherCode: true,
    //     voucherCode: 'A',

    //     ///////////////////////////////////////////////////

    //     type: 'REGISTRATION',

    //     status: 'PENDING_SIGN',

    //     ///////////////////////////////////////////////////

    //     isVoucherCodeApplied: true, // if user applied

    //     agreementLink: 'https://koodooprod.s3.ap-southeast-1.amazonaws.com/pdf/test-sign.pdf',
    //     agreementSignedLink: '',

    //     invoiceLink: '',
    //     receiptLink: '',

    //     ////////////////////////////////////////////////////////

    //     bankType: '',
    //     bankCode: '',
    //     bankAccountName: '',
    //     bankAccountNumber: '',
    //     contactEmail: '',
    //     contactMobile: '',

    //     picFullName: '',
    //     picNRICPassport: '',
    //     picIdType: '',
    //     companyName: '',

    //     country: '',

    //     outletId: '',

    //     companyRobRoc: '',

    //     acceptedPrivacyPolicy: true,
    //     acceptedTermsConditions: true,

    //     ////////////////////////////////////////////////////////

    //     paymentDate: null,
    //     paymentDetails: {},

    //     isPaidOnline: false,
    //     isPaidOffline: false,

    // }, // default is null

    currMerchantPayment: null,

    smsCreditBalance: 0,
    whatsappCreditBalance: 0,

    upsellingCampaigns: [],

    ptTimestamp: Date.now(),
    pteTimestamp: Date.now(),

    payoutTransactions: [],
    payoutTransactionsExtend: [],

    reportDisplayType: 'DAY',

    outletReviews: [],

    reservationConfig: null,

    queueConfig: null,

    recommendedLoyalty: null,
    selectedRecommendedLoyalty: null,

    reportingApiLoading: false,
    outletCatalog: []
});   
