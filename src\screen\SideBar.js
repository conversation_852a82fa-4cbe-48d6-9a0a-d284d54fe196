import React, { Component, useState, useEffect, useRef } from "react";
import {
  ScrollView,
  Text,
  TouchableOpacity,
  TouchableHighlight,
  View,
  StyleSheet,
  Image,
  Dimensions,
  Alert,
  Modal,
  useWindowDimensions,
} from "react-native";
import Colors from "../constant/Colors";
import Styles from "../constant/Styles";
import { ReactComponent as Dashboard } from "../assets/svg/Dashboard.svg";
import { ReactComponent as DashboardG } from "../assets/svg/Dashboard.svg";
import { ReactComponent as CRM } from "../assets/svg/CRM.svg";
import { ReactComponent as Employees } from "../assets/svg/Employees.svg";
import { ReactComponent as Inventory } from "../assets/svg/Inventory.svg";
import { ReactComponent as Operation } from "../assets/svg/Operation.svg";
import { ReactComponent as Product } from "../assets/svg/Product.svg";
import { ReactComponent as Promotions } from "../assets/svg/Promotions.svg";
import { ReactComponent as Redemption } from "../assets/svg/Redemption.svg";
import { ReactComponent as RedemptionG } from "../assets/svg/Redemption.svg";
import { ReactComponent as Report } from "../assets/svg/Report.svg";
import { ReactComponent as Settings } from "../assets/svg/Settings.svg";
import { ReactComponent as Transactions } from "../assets/svg/Transactions.svg";
import { ReactComponent as CRMG } from "../assets/svg/CRMG.svg";
import { ReactComponent as Loyalty } from "../assets/svg/Loyalty.svg";
import { ReactComponent as Logout } from "../assets/svg/Logout.svg";
import { ReactComponent as TicketConfirmationOutline } from "../assets/svg/TicketConfirmationOutline.svg";
// import GCoin from '../assets/svg/GCoin';
// import Coins from '../assets/svg/Coins';
import { ReactComponent as EmployeesG } from "../assets/svg/EmployeesG.svg";
import { ReactComponent as InventoryG } from "../assets/svg/InventoryG.svg";
import { ReactComponent as OperationG } from "../assets/svg/OperationG.svg";
import { ReactComponent as ProductG } from "../assets/svg/ProductG.svg";
import { ReactComponent as PromotionsG } from "../assets/svg/PromotionsG.svg";
import { ReactComponent as ReportG } from "../assets/svg/ReportG.svg";
import { ReactComponent as SettingsG } from "../assets/svg/SettingsG.svg";
import { ReactComponent as TransactionsG } from "../assets/svg/TransactionsG.svg";

// import Arrow from '../assets/svg/Arrow';
import { ReactComponent as Arrow } from "../assets/svg/Arrow.svg";
import * as User from "../util/User";
import AsyncStorage from "@react-native-async-storage/async-storage";
import MaterialIcons from "react-native-vector-icons/MaterialIcons";
// import {isTablet} from 'react-native-device-detection';
import { CommonStore } from "../store/commonStore";
// import {color, onChange} from 'react-native-reanimated';
import { EXPAND_TAB_TYPE, MODE_ADD_CART, PRIVILEGES_NAME, ROLE_TYPE } from "../constant/common";
// import {suppressDeprecationWarnings} from 'moment';
import ApiClient from "../util/ApiClient";
import API from "../constant/API";
// import IdleTimer from 'react-idle-timer'

import { useLinkTo, useRoute } from "@react-navigation/native";
import imgLogo from "../asset/image/logo.png";
import { isMobile } from "../util/common";
import { DataStore } from "../store/dataStore";
import { prefix } from "../constant/env";
import { MerchantStore } from "../store/merchantStore";
import { UserStore } from "../store/userStore";
import { OutletStore } from '../store/outletStore';

const isTablet = true;

const SideBar = (props) => {
  // Used for navigation
  const { navigation } = props;

  const { height: windowHeight, width: windowWidth } = useWindowDimensions();

  const linkTo = useLinkTo();

  const [expandOperation, setExpandOperation] = useState(
    props.expandOperation === undefined ? false : props.expandOperation
  );
  const [expandProduct, setExpandProduct] = useState(
    props.expandProduct === undefined ? false : props.expandProduct
  );
  const [expandInventory, setExpandInventory] = useState(
    props.expandInventory === undefined ? false : props.expandInventory
  );
  const [expandSales, setExpandSales] = useState(
    props.expandSales === undefined ? false : props.expandSales
  );
  const [expandPromotions, setExpandPromotions] = useState(
    props.expandPromotions === undefined ? false : props.expandPromotions
  );
  const [expandCRM, setExpandCRM] = useState(
    props.expandCRM === undefined ? false : props.expandCRM
  );
  const [expandLoyaltyPoints, setExpandLoyaltyPoints] = useState(
    props.expandLoyaltyPoints === undefined ? false : props.expandLoyaltyPoints
  );
  const [expandVoucher, setExpandVoucher] = useState(
    props.expandVoucher === undefined ? false : props.expandVoucher
  );
  const [expandTransactions, setExpandTransactions] = useState(
    props.expandTransactions === undefined ? false : props.expandTransactions
  );
  const [expandReport, setExpandReport] = useState(
    props.expandReport === undefined ? false : props.expandReport
  );
  const [expandEmployees, setExpandEmployees] = useState(
    props.expandEmployees === undefined ? false : props.expandEmployees
  );
  const [expandSettings, setExpandSettings] = useState(
    props.expandSettings === undefined ? false : props.expandSettings
  );
  const [expandRedemption, setExpandRedemption] = useState(
    props.expandRedemption === undefined ? false : props.expandRedemption
  );
  const [selectedTab, setSelectedTab] = useState(
    props.selectedTab === undefined ? 0 : props.selectedTab
  );
  const [switchMerchant, setSwitchMerchant] = useState(isTablet ? false : true);

  const myScroll = useRef(null);

  // const [currPage, setCurrPage] = useState('');

  const currPage = CommonStore.useState((s) => s.currPage);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  const expandTab = CommonStore.useState((s) => s.expandTab);

  const [isMounted, setIsMounted] = useState(false);

  const role = UserStore.useState((s) => s.role);
  const currOutlet = MerchantStore.useState((s) => s.currOutlet);

  const privileges_state = UserStore.useState((s) => s.privileges);
  const screensToBlock = UserStore.useState((s) => s.screensToBlock);

  const [privileges, setPrivileges] = useState([]);

  // const payoutTransactions = OutletStore.useState((s) => s.payoutTransactions);

  // useEffect(() => {
  //     expandAction(selectedTab);
  // }, [selectedTab])

  // componentDidMount() {
  //     expandAction(selectedTab)

  //     // retrieveAsyncStorage();
  // }

  // async retrieveAsyncStorage() {
  //     const switchMerchant = await AsyncStorage.getItem('switchMerchant');

  //     if (switchMerchant === '1') {
  //         setState({
  //             switchMerchant: true,
  //         });
  //     }
  // }

  const logOutButton = async () => {
    if (window.confirm("Logout: Do you want to logout?")) {
      UserStore.update((s) => {
        s.avatar = '';
        s.dob = null;
        s.email = '';
        s.gender = '';
        s.name = '';
        s.number = '';
        s.outletId = '';
        s.race = '';
        s.state = '';
        s.uniqueName = '';
        s.updatedAt = null;
        s.merchantId = '';
        s.role = '';
        s.refreshToken = '';
        s.firebaseUid = '';
        s.privileges = [];
      });

      MerchantStore.update((s) => {
        s.allOutlets = [];
        s.allOutletsDict = {};
        s.currOutletId = '';
        s.currOutlet = {
          uniqueId: '',
          privileges: [],
        };
      });

      await AsyncStorage.multiRemove([
        'accessToken',
        'userData',
        'refreshToken',

        'merchantLogoUrl',

        // 'lastActivity',

        'email',
        'password',

        'printerList',

        'supportCodeData',

        // 'isPrintingKDAndOS',
      ]);

      const merchantId = await AsyncStorage.getItem('merchantId');
      const currOutletId = await AsyncStorage.getItem('currOutletId');
      const tokenFcm = await AsyncStorage.getItem('tokenFcm');

      const body = {
        role: role,
        merchantId: merchantId,
        outletId: currOutlet.uniqueId,
        tokenFcm: tokenFcm,
      };

      ApiClient.POST(API.logoutUser, body).then((result) => {
        User.setlogin(false);
        User.setMerchantId(null);
        User.setUserData(null);
        User.setUserId(null);
        User.setRefreshToken(null);
        User.setOutletId(null);
        // User.getRefreshMainScreen();

        global.privileges = [];

        linkTo && linkTo(`${prefix}/login`);
      });
    }

    return;
  };

  // const logOutButton = async () => {
  //     const [signoutTime, setSignoutTime] = useState(0);
  //         let logoutTimeout;

  //     const setTimeout = () => {
  //         logoutTimeout = setTimeout(logout, signoutTime);
  //     };

  //     const clearTimeout = () => {
  //         if (logoutTimeout) clearTimeout(logoutTimeout);
  //     };

  //     // useEffect(() => {
  //     //     setSignoutTime(20000);
  //     //     const events = [
  //     //         'load',
  //     //         'mousemove',
  //     //         'mousedown',
  //     //         'click',
  //     //         'scroll',
  //     //         'keypress'
  //     //     ];

  //     const setSignoutTime = 2000; //2 second

  //     const resetTimeout = () => {
  //         clearTimeout();
  //         setTimeout();
  //     };

  //     for (var i in events) {
  //         window.addEventListener(events[i], resetTimeout);
  //     }

  //     setTimeout();
  //     };

  //     if (signoutTime) {
  //         return (
  //             Alert.alert(
  //                 "Session Timeout",
  //                 "Timeout! You want to stay?",
  //                 [
  //                     {
  //                         text: 'Yes',
  //                         // onPress={async () => {
  //                         //     await AsyncStorage.clear();
  //                         //     User.setlogin(false);
  //                         //     User.getRefreshMainScreen();
  //                         // }}
  //                         onPress: () => {
  //                             User.setlogin(false);
  //                             User.getRefreshMainScreen();

  //                         },
  //                     },

  //                     {
  //                         text: 'No',
  //                         onPress: () => { },
  //                     }
  //                 ]
  //             )
  //         )
  //     }

  //             Alert.alert(
  //                 'Logout',
  //                 "Do you want to logout?",
  //                 [
  //                     {
  //                         text: 'Yes',
  //                         // onPress={async () => {
  //                         //     await AsyncStorage.clear();
  //                         //     User.setlogin(false);
  //                         //     User.getRefreshMainScreen();
  //                         // }}
  //                         onPress: () => {
  //                             User.setlogin(false);
  //                             User.getRefreshMainScreen();

  //                         },
  //                     },

  //                     {
  //                         text: 'No',
  //                         onPress: () => { },
  //                     }
  //                 ]
  //             )
  // }

  // Modal.setAppElement('#root')

  // const IdleTimerContainer = () => {
  //     const [isLoggedIn, setIsLoggedIn] = useState(true)
  //     const [modalIsOpen, setModalIsOpen] = useState(false)
  //     const  idleTimerRef = useRef(null)
  //     const sessionTimeoutRef = usseRef(null)

  //     const onIdle = () => {
  //         setModalIsOpen(true)
  //     }

  //     const stayActive = () => {
  //         setModalIsOpen(false)
  //         clearTimeout(sessionTimeoutRef.current)

  //     }

  //     const stayActive = () => {
  //         setModalIsOpen(false)
  //         clearTimeout(sessionTimeoutRef.current)

  //     }

  //     const logOut = () => {
  //         setModalIsOpen(false)
  //         setIsLoggedIn(false)
  //         clearTimeout(sessionTimeoutRef.current)

  //     }

  //     return (
  //     <div>
  //         {isLoggedIn ? <h2> Hello Vishawa </h2>:  <h2> Hello Guest</h2>}
  //         <Modal isOpen = {modalIsOpen}>
  //             <h2> You've been idle for a while! </h2>
  //             <p> You will be logged out soon </p>
  //             <div>
  //                 <button onClick = {logOut}> Log me out </button>
  //                 <button onClick = {stayActive}> Keep me signed in </button>
  //             </div>
  //         </Modal>
  //         <IdlerTimer ref = {idleTimerRef}
  //                     timeout = {5*1000}
  //                     onIdle = {onIdle}/>

  //     </div>
  //     )
  // }

  useEffect(() => {
    // admin full access

    // const enteredPinNo = await AsyncStorage.getItem('enteredPinNo');
    // const enteredPinNo = storageMMKV.getString('enteredPinNo');

    if (role === ROLE_TYPE.ADMIN
      // && pinNo === enteredPinNo
    ) {
      setPrivileges(privileges_state || []);

      // setPrivileges([
      //   "EMPLOYEES",
      //   "OPERATION",
      //   "PRODUCT",
      //   "INVENTORY",
      //   "INVENTORY_COMPOSITE",
      //   "DOCKET",
      //   "VOUCHER",
      //   "PROMOTION",
      //   "CRM",
      //   "LOYALTY",
      //   "TRANSACTIONS",
      //   "REPORT",
      //   "RESERVATIONS",

      //   // for action
      //   'REFUND_ORDER',

      //   'SETTINGS',

      //   'QUEUE',

      //   'OPEN_CASH_DRAWER',

      //   'KDS',

      //   'UPSELLING',

      //   // for Kitchen

      //   'REJECT_ITEM',
      //   'CANCEL_ORDER',
      //   //'REFUND_tORDER',
      // ]);
    } else {
      setPrivileges(privileges_state || []);

      // setPrivileges([
      //   "EMPLOYEES",
      //   "OPERATION",
      //   "PRODUCT",
      //   "INVENTORY",
      //   "INVENTORY_COMPOSITE",
      //   "DOCKET",
      //   "VOUCHER",
      //   "PROMOTION",
      //   "CRM",
      //   "LOYALTY",
      //   "TRANSACTIONS",
      //   "REPORT",
      //   "RESERVATIONS",

      //   // for action
      //   'REFUND_ORDER',

      //   'SETTINGS',

      //   'QUEUE',

      //   'OPEN_CASH_DRAWER',

      //   'KDS',

      //   'UPSELLING',
      // ]);
    }
  }, [role, privileges_state]);

  useEffect(() => {
    if (linkTo) {
      DataStore.update((s) => {
        s.linkToFunc = linkTo;
      });
    }
  }, [linkTo]);

  useEffect(() => {
    // reset states
    CommonStore.update((s) => {
      // s.selectedProductEdit = null;
      // s.selectedOutletCategoryEdit = null;
      // s.selectedSupplierEdit = null;
      // s.selectedPurchaseOrderEdit = null;
      // s.selectedStockTransferEdit = null;
      // s.selectedStockTakeEdit = null;
      // s.selectedVoucherEdit = null;
      // s.selectedOutletEmployeeEdit = null;

      // s.selectedPromotionEdit = null;

      // s.selectedCustomerEdit = null;

      // experimental
      s.modeAddCart = MODE_ADD_CART.NORMAL;
    });
  }, [currPage]);

  useEffect(() => {
    if (isMounted) {
      if (expandTab == EXPAND_TAB_TYPE.DASHBOARD) {
        myScroll && myScroll.current && myScroll.current.scrollTo({ y: 0 });
      } else if (expandTab == EXPAND_TAB_TYPE.OPERATION) {
        myScroll &&
          myScroll.current &&
          myScroll.current.scrollTo({
            y: windowHeight * 0,
          });
      } else if (expandTab == EXPAND_TAB_TYPE.PRODUCT) {
        // myScroll && myScroll.current && myScroll.current.scrollTo({ y: 173 })
        myScroll &&
          myScroll.current &&
          myScroll.current.scrollTo({
            y: windowHeight * 0,
          });
      } else if (expandTab == EXPAND_TAB_TYPE.INVENTORY) {
        // myScroll && myScroll.current && myScroll.current.scrollTo({ y: 255.5 })
        myScroll &&
          myScroll.current &&
          myScroll.current.scrollTo({
            y: windowHeight * 0.17,
          });
        // } else if (expandTab == EXPAND_TAB_TYPE.INVENTORY_COMPOSITE) {
        //   // myScroll && myScroll.current && myScroll.current.scrollTo({ y: 255.5 })
        //   myScroll &&
        //     myScroll.current &&
        //     myScroll.current.scrollTo({
        //       y: windowHeight * 0.1,
        //     });
      } else if (expandTab == EXPAND_TAB_TYPE.DOCKET) {
        myScroll &&
          myScroll.current &&
          myScroll.current.scrollTo({
            y: windowHeight * 0.25,
          });
      } else if (expandTab == EXPAND_TAB_TYPE.VOUCHER) {
        myScroll &&
          myScroll.current &&
          myScroll.current.scrollTo({
            y: windowHeight * 0.3,
          });
      } else if (expandTab == EXPAND_TAB_TYPE.CRM) {
        myScroll && myScroll.current && myScroll.current.scrollToEnd();
      } else if (expandTab == EXPAND_TAB_TYPE.TRANSACTIONS) {
        myScroll && myScroll.current && myScroll.current.scrollToEnd();
      } else if (expandTab == EXPAND_TAB_TYPE.REPORT) {
        myScroll && myScroll.current && myScroll.current.scrollTo({ y: windowHeight * 0 });
      } else if (expandTab == EXPAND_TAB_TYPE.EMPLOYEES) {
        myScroll && myScroll.current && myScroll.current.scrollToEnd();
      } else if (expandTab == EXPAND_TAB_TYPE.SETTINGS) {
        myScroll && myScroll.current && myScroll.current.scrollToEnd();
      } else if (expandTab == EXPAND_TAB_TYPE.PROMOTION) {
        myScroll && myScroll.current && myScroll.current.scrollToEnd();
      } else if (expandTab == EXPAND_TAB_TYPE.LOYALTY) {
        myScroll &&
          myScroll.current &&
          myScroll.current.scrollTo({
            y: windowHeight * 0.55,
          });
      }
    } else {
      setIsMounted(true);
    }
  }, [expandTab]);

  const expandAction = (selectedTabParam) => {
    console.log("selectedTabParam");
    console.log(selectedTabParam);

    // reset states
    CommonStore.update((s) => {
      s.selectedProductEdit = null;
      s.selectedOutletCategoryEdit = null;
      s.selectedSupplierEdit = null;
      s.selectedPurchaseOrderEdit = null;
      s.selectedStockTransferEdit = null;
      s.selectedStockTakeEdit = null;
      s.selectedVoucherEdit = null;
      s.selectedOutletEmployeeEdit = null;

      s.selectedPromotionEdit = null;

      s.selectedCustomerEdit = null;

      // experimental
      s.modeAddCart = MODE_ADD_CART.NORMAL;
    });

    if (selectedTabParam == 0) {
      myScroll && myScroll.current && myScroll.current.scrollTo({ y: 0 });
      // setState({
      //     expandOperation: false,
      //     expandProduct: false,
      //     expandInventory: false,
      //     expandSales: false,
      //     expandPromotions: false,
      //     expandCRM: false,
      //     expandTransactions: false,
      //     expandReport: false,
      //     expandEmployees: false,
      //     expandSettings: false,
      //     expandRedemption: false,
      // })
      setExpandOperation(false);
      setExpandProduct(false);
      setExpandInventory(false);
      setExpandSales(false);
      setExpandPromotions(false);
      setExpandCRM(false);
      setExpandLoyaltyPoints(false);
      setExpandTransactions(false);
      setExpandReport(false);
      setExpandEmployees(false);
      setExpandSettings(false);
      setExpandRedemption(false);
      setExpandVoucher(false);
    } else if (selectedTabParam == 1) {
      myScroll &&
        myScroll.current &&
        myScroll.current.scrollTo({ y: windowHeight * 0.1 });
      // setState({
      //     expandOperation: true,
      //     expandProduct: false,
      //     expandInventory: false,
      //     expandSales: false,
      //     expandPromotions: false,
      //     expandCRM: false,
      //     expandTransactions: false,
      //     expandReport: false,
      //     expandEmployees: false,
      //     expandSettings: false,
      //     expandRedemption: false,
      // })
      setExpandOperation(true);
      setExpandProduct(false);
      setExpandInventory(false);
      setExpandSales(false);
      setExpandPromotions(false);
      setExpandCRM(false);
      setExpandLoyaltyPoints(false);
      setExpandTransactions(false);
      setExpandReport(false);
      setExpandEmployees(false);
      setExpandSettings(false);
      setExpandRedemption(false);
      setExpandVoucher(false);
    } else if (selectedTabParam == 2) {
      // myScroll && myScroll.current && myScroll.current.scrollTo({ y: 173 })
      myScroll &&
        myScroll.current &&
        myScroll.current.scrollTo({
          y: windowHeight * 0.15,
        });

      // setState({
      //     expandOperation: false,
      //     expandProduct: true,
      //     expandInventory: false,
      //     expandSales: false,
      //     expandPromotions: false,
      //     expandCRM: false,
      //     expandTransactions: false,
      //     expandReport: false,
      //     expandEmployees: false,
      //     expandSettings: false,
      //     expandRedemption: false,
      // })
      setExpandOperation(false);
      setExpandProduct(true);
      setExpandInventory(false);
      setExpandSales(false);
      setExpandPromotions(false);
      setExpandCRM(false);
      setExpandLoyaltyPoints(false);
      setExpandTransactions(false);
      setExpandReport(false);
      setExpandEmployees(false);
      setExpandSettings(false);
      setExpandRedemption(false);
      setExpandVoucher(false);
    } else if (selectedTabParam == 3) {
      // myScroll && myScroll.current && myScroll.current.scrollTo({ y: 255.5 })
      myScroll &&
        myScroll.current &&
        myScroll.current.scrollTo({ y: windowHeight * 0.2 });

      // setState({
      //     expandOperation: false,
      //     expandProduct: false,
      //     expandInventory: true,
      //     expandSales: false,
      //     expandPromotions: false,
      //     expandCRM: false,
      //     expandTransactions: false,
      //     expandReport: false,
      //     expandEmployees: false,
      //     expandSettings: false,
      //     expandRedemption: false,
      // })
      setExpandOperation(false);
      setExpandProduct(false);
      setExpandInventory(true);
      setExpandSales(false);
      setExpandPromotions(false);
      setExpandCRM(false);
      setExpandLoyaltyPoints(false);
      setExpandTransactions(false);
      setExpandReport(false);
      setExpandEmployees(false);
      setExpandSettings(false);
      setExpandRedemption(false);
      setExpandVoucher(false);
    } else if (selectedTabParam == 4) {
      myScroll &&
        myScroll.current &&
        myScroll.current.scrollTo({ y: windowHeight * 0.3 });
      // setState({
      //     expandOperation: false,
      //     expandProduct: false,
      //     expandInventory: false,
      //     expandSales: false,
      //     expandPromotions: false,
      //     expandCRM: false,
      //     expandTransactions: false,
      //     expandReport: false,
      //     expandEmployees: false,
      //     expandSettings: false,
      //     expandRedemption: true,
      // })
      setExpandOperation(false);
      setExpandProduct(false);
      setExpandInventory(false);
      setExpandSales(false);
      setExpandPromotions(false);
      setExpandCRM(false);
      setExpandLoyaltyPoints(false);
      setExpandTransactions(false);
      setExpandReport(false);
      setExpandEmployees(false);
      setExpandSettings(false);
      setExpandRedemption(true);
      setExpandVoucher(false);
    } else if (selectedTabParam == 5) {
      myScroll &&
        myScroll.current &&
        myScroll.current.scrollTo({
          y: windowHeight * 0.35,
        });
      // setState({
      //     expandOperation: false,
      //     expandProduct: false,
      //     expandInventory: false,
      //     expandSales: false,
      //     expandPromotions: true,
      //     expandCRM: false,
      //     expandTransactions: false,
      //     expandReport: false,
      //     expandEmployees: false,
      //     expandSettings: false,
      //     expandRedemption: false,
      // })
      setExpandOperation(false);
      setExpandProduct(false);
      setExpandInventory(false);
      setExpandSales(false);
      setExpandPromotions(false);
      setExpandCRM(false);
      setExpandLoyaltyPoints(false);
      setExpandTransactions(false);
      setExpandReport(false);
      setExpandEmployees(false);
      setExpandSettings(false);
      setExpandRedemption(false);
      setExpandVoucher(true);
    } else if (selectedTabParam == 6) {
      myScroll && myScroll.current && myScroll.current.scrollToEnd();
      // setState({
      //     expandOperation: false,
      //     expandProduct: false,
      //     expandInventory: false,
      //     expandSales: false,
      //     expandPromotions: false,
      //     expandCRM: true,
      //     expandTransactions: false,
      //     expandReport: false,
      //     expandEmployees: false,
      //     expandSettings: false,
      //     expandRedemption: false,
      // })
      setExpandOperation(false);
      setExpandProduct(false);
      setExpandInventory(false);
      setExpandSales(false);
      setExpandPromotions(false);
      setExpandCRM(true);
      setExpandLoyaltyPoints(false);
      setExpandTransactions(false);
      setExpandReport(false);
      setExpandEmployees(false);
      setExpandSettings(false);
      setExpandRedemption(false);
      setExpandVoucher(false);
    } else if (selectedTabParam == 7) {
      myScroll && myScroll.current && myScroll.current.scrollToEnd();
      // setState({
      //     expandOperation: false,
      //     expandProduct: false,
      //     expandInventory: false,
      //     expandSales: false,
      //     expandPromotions: false,
      //     expandCRM: false,
      //     expandTransactions: true,
      //     expandReport: false,
      //     expandEmployees: false,
      //     expandSettings: false,
      //     expandRedemption: false,
      // })
      setExpandOperation(false);
      setExpandProduct(false);
      setExpandInventory(false);
      setExpandSales(false);
      setExpandPromotions(false);
      setExpandCRM(false);
      setExpandLoyaltyPoints(false);
      setExpandTransactions(true);
      setExpandReport(false);
      setExpandEmployees(false);
      setExpandSettings(false);
      setExpandRedemption(false);
      setExpandVoucher(false);
    } else if (selectedTabParam == 8) {
      myScroll && myScroll.current && myScroll.current.scrollToEnd();
      // setState({
      //     expandOperation: false,
      //     expandProduct: false,
      //     expandInventory: false,
      //     expandSales: false,
      //     expandPromotions: false,
      //     expandCRM: false,
      //     expandTransactions: false,
      //     expandReport: true,
      //     expandEmployees: false,
      //     expandSettings: false,
      //     expandRedemption: false,
      // })
      setExpandOperation(false);
      setExpandProduct(false);
      setExpandInventory(false);
      setExpandSales(false);
      setExpandPromotions(false);
      setExpandCRM(false);
      setExpandLoyaltyPoints(false);
      setExpandTransactions(false);
      setExpandReport(true);
      setExpandEmployees(false);
      setExpandSettings(false);
      setExpandRedemption(false);
      setExpandVoucher(false);
    } else if (selectedTabParam == 9) {
      myScroll && myScroll.current && myScroll.current.scrollToEnd();
      // setState({
      //     expandOperation: false,
      //     expandProduct: false,
      //     expandInventory: false,
      //     expandSales: false,
      //     expandPromotions: false,
      //     expandCRM: false,
      //     expandTransactions: false,
      //     expandReport: false,
      //     expandEmployees: true,
      //     expandSettings: false,
      //     expandRedemption: false,
      // })
      setExpandOperation(false);
      setExpandProduct(false);
      setExpandInventory(false);
      setExpandSales(false);
      setExpandPromotions(false);
      setExpandCRM(false);
      setExpandLoyaltyPoints(false);
      setExpandTransactions(false);
      setExpandReport(false);
      setExpandEmployees(true);
      setExpandSettings(false);
      setExpandRedemption(false);
      setExpandVoucher(false);
    } else if (selectedTabParam == 10) {
      myScroll && myScroll.current && myScroll.current.scrollToEnd();
      // setState({
      //     expandOperation: false,
      //     expandProduct: false,
      //     expandInventory: false,
      //     expandSales: false,
      //     expandPromotions: false,
      //     expandCRM: false,
      //     expandTransactions: false,
      //     expandReport: false,
      //     expandEmployees: false,
      //     expandSettings: true,
      //     expandRedemption: false,
      // })
      setExpandOperation(false);
      setExpandProduct(false);
      setExpandInventory(false);
      setExpandSales(false);
      setExpandPromotions(false);
      setExpandCRM(false);
      setExpandLoyaltyPoints(false);
      setExpandTransactions(false);
      setExpandReport(false);
      setExpandEmployees(false);
      setExpandSettings(true);
      setExpandRedemption(false);
      setExpandVoucher(false);
    } else if (selectedTabParam == 11) {
      myScroll && myScroll.current && myScroll.current.scrollToEnd();
      // setState({
      //     expandOperation: false,
      //     expandProduct: false,
      //     expandInventory: false,
      //     expandSales: false,
      //     expandPromotions: false,
      //     expandCRM: false,
      //     expandTransactions: false,
      //     expandReport: false,
      //     expandEmployees: false,
      //     expandSettings: true,
      //     expandRedemption: false,
      // })
      setExpandOperation(false);
      setExpandProduct(false);
      setExpandInventory(false);
      setExpandSales(false);
      setExpandPromotions(true);
      setExpandCRM(false);
      setExpandLoyaltyPoints(false);
      setExpandTransactions(false);
      setExpandReport(false);
      setExpandEmployees(false);
      setExpandSettings(false);
      setExpandRedemption(false);
      setExpandVoucher(false);
    } else if (selectedTabParam == 12) {
      myScroll && myScroll.current && myScroll.current.scrollToEnd();
      // setState({
      //     expandOperation: false,
      //     expandProduct: false,
      //     expandInventory: false,
      //     expandSales: false,
      //     expandPromotions: false,
      //     expandCRM: false,
      //     expandTransactions: false,
      //     expandReport: false,
      //     expandEmployees: false,
      //     expandSettings: true,
      //     expandRedemption: false,
      // })
      setExpandOperation(false);
      setExpandProduct(false);
      setExpandInventory(false);
      setExpandSales(false);
      setExpandPromotions(false);
      setExpandCRM(false);
      setExpandLoyaltyPoints(true);
      setExpandTransactions(false);
      setExpandReport(false);
      setExpandEmployees(false);
      setExpandSettings(false);
      setExpandRedemption(false);
      setExpandVoucher(false);
    }
  };

  var sidebarFontSizeTablet = 14;

  if (Dimensions.get("screen").width <= 1024) {
    sidebarFontSizeTablet = 12;
    styles.sidebarArrow = { paddingRight: 20 };

    if (isTablet) {
      styles.expandedItems = {
        fontSize: 12,
        alignSelf: "center",
        textAlign: "center",
      };
    } else {
      styles.expandedItems = {
        fontSize: 12,
        alignSelf: "center",
        textAlign: "center",
      };
    }
    //remember chg back to fontsize 12
  }

  const sidebarTextStyleScale = [
    styles.sidebarTextStyle,
    !isTablet
      ? {
        fontSize:
          Dimensions.get("screen").width <= 720
            ? 9
            : switchMerchant
              ? 10
              : 12,
      }
      : {
        fontSize: sidebarFontSizeTablet,
      },
  ];

  const sideBarItemsContainerStyleScale = [
    // styles.sidebarItems,
    !isTablet
      ? {
        // height: Dimensions.get('screen').height * 0.1,
      }
      : undefined,
    //:{width:Dimensions.get('screen').width * 0.1, display:none},
  ];

  const sideBarItemsStyleScale = [
    styles.sidebarItems,
    {
      width: Dimensions.get("screen").width * Styles.sideBarWidth,
    },
    !isTablet
      ? {
        //width: Dimensions.get('screen').width * 0.08,
        height: Dimensions.get("screen").height * 0.15,
        // backgroundColor: 'red'
      }
      : undefined,
  ];

  const sidebarIconStyleScale = [
    // sidebarIconStyleScale,
    !isTablet
      ? {
        // marginRight: 10,
      }
      : {
        // marginRight: 12,
      },
  ];

  const arrowSize = !isTablet ? 17 : 12;

  const iconSize = !isTablet ? 0.04 : 0.02;

  const highlightStyle = {
    // backgroundColor: '#405d27',
    // opacity: 0,
  };

  return (
    <ScrollView
      style={[
        styles.scrollView,
        {
          // backgroundColor: 'red',
          width: windowWidth * Styles.sideBarWidth,
          ...isMobile() && {
            width: windowWidth * 0.23,
          },
          //width: 100,
          height: windowHeight,
        },
        switchMerchant
          ? {
            // width: '10%'
          }
          : {},
      ]}
      showsVerticalScrollIndicator={false}
      ref={myScroll}
      contentContainerStyle={{
        paddingBottom: Dimensions.get("window").height * 0.2,
      }}
    >
      {/* <TouchableHighlight
        style={[
          { sideBarItemsStyleScale },
          switchMerchant
            ? {
                // borderWidth: 1,
                justifyContent: "center",
              }
            : {},
        ]}
        // underlayColor={Colors.primaryColor}
        underlayColor={Colors.lightPrimary}
        onPress={() => {
          if (expandTab !== EXPAND_TAB_TYPE.DASHBOARD) {
            CommonStore.update((s) => {
              s.expandTab = EXPAND_TAB_TYPE.DASHBOARD;
            });
          }

          // props.navigation.navigate('Dashboard');
          linkTo && linkTo(`${prefix}/dashboard`);
        }}
      >
        {expandTab == EXPAND_TAB_TYPE.DASHBOARD ? (
          <View
            style={[
              styles.sidebarView,
              { backgroundColor: "Colors.lightPrimary" },
              switchMerchant
                ? {
                    // width: '100%'
                  }
                : {},
            ]}
          >
            <View style={sidebarIconStyleScale}>
              <DashboardG
                width={
                  switchMerchant
                    ? Dimensions.get("screen").width * 0.02
                    : Dimensions.get("screen").width * iconSize
                }
                height={
                  switchMerchant
                    ? Dimensions.get("screen").width * 0.02
                    : Dimensions.get("screen").width * iconSize
                }
              />
            </View>
            <View style={styles.sidebarText}>
              <Text
                style={[sidebarTextStyleScale, { color: Colors.primaryColor }]}
              >
                Dashboard
              </Text>
            </View>
          </View>
        ) : (
          <View
            style={[
              styles.sidebarView,
              ,
              switchMerchant
                ? {
                    // width: '100%'
                  }
                : {},
            ]}
          >
            <View style={sidebarIconStyleScale}>
              <Dashboard
                width={
                  switchMerchant
                    ? Dimensions.get("screen").width * 0.02
                    : Dimensions.get("screen").width * iconSize
                }
                height={
                  switchMerchant
                    ? Dimensions.get("screen").width * 0.02
                    : Dimensions.get("screen").width * iconSize
                }
              />
            </View>
            <View style={styles.sidebarText}>
              <Text style={sidebarTextStyleScale}>Dashboard</Text>
            </View>
          </View>
        )}
      </TouchableHighlight> */}

      {(false && (currOutlet && currOutlet.privileges &&
        currOutlet.privileges.includes(PRIVILEGES_NAME.OPERATION))
        &&
        (privileges.includes(PRIVILEGES_NAME.OPERATION) || privileges.includes(PRIVILEGES_NAME.KDS)))
        ?
        <View style={{
          ...isMobile() && {
            width: windowWidth * 0.23,
            //backgroundColor:'yellow',
          },
        }}>{/*sideBarwidth:80ItemsContainerStyleScale*,paddingLeft:'auto',paddingRight:'auto'/}
          {/* operation hide first */}
          <TouchableHighlight
            underlayColor={Colors.lightPrimary}
            onPress={() => {
              // setState({ expandOperation: !expandOperation, selectedTab: 1 });

              setExpandOperation(!expandOperation);
              setSelectedTab(1);
              expandAction(1);

              if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                });
              }
              else {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.NONE;
                });
              }
            }}
            style={sideBarItemsStyleScale}
          //style={{paddingLeft:'auto',paddingRight:'auto'}}
          >
            {expandTab == EXPAND_TAB_TYPE.OPERATION ? (
              <View style={{}}>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: Colors.lightPrimary,
                      height: isTablet
                        ? windowHeight * 0.08
                        : windowHeight * 0.16,
                      width: isTablet
                        ? windowWidth * Styles.sideBarWidth
                        : "100%",
                      ...isMobile() && {
                        width: windowWidth * 0.23,
                        marginLeft: 'auto',
                        marginRight: 'auto',
                      },
                    },
                  ]}
                >
                  <View style={[sidebarIconStyleScale, {
                    ...(isMobile() && {
                      width: 18,
                      height: 18
                    }),
                  },
                  ]}>
                    <OperationG
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      {...(isMobile() && {
                        width: 18,
                        height: 18
                      })}
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text
                      style={[
                        sidebarTextStyleScale,
                        { color: Colors.primaryColor },
                      ]}
                    >
                      Operation
                    </Text>
                  </View>
                </View>
              </View>
            ) : (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      //backgroundColor: 'red',
                      height: isTablet
                        ? windowHeight * 0.08
                        : windowHeight * 0.16,
                      width: isTablet
                        ? windowWidth * Styles.sideBarWidth
                        : "100%",
                      ...(isMobile() && {
                        width: windowWidth * 0.23,
                      }),
                    },
                  ]}
                >
                  <View style={[sidebarIconStyleScale, {
                    ...(isMobile() && {
                      width: 18,
                      height: 18
                    }),
                  },
                  ]}>
                    <Operation
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      {...(isMobile() && {
                        width: 18,
                        height: 18
                      })}
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text style={[
                      sidebarTextStyleScale, {/*
                          ...(isMobile() && {
                            marginLeft: 'auto',
                            marginRight: 'auto',
                          }),
                        */}
                    ]}>Operation</Text>{/*sidebarTextStyleScale*/}
                  </View>
                </View>
              </View>
            )}
          </TouchableHighlight>
          {/* trick to hide first */}

          {expandTab == EXPAND_TAB_TYPE.OPERATION
            // && expandTab !== EXPAND_TAB_TYPE.OPERATION 
            ? (
              <View
                style={[
                  {
                    paddingVertical: 16,
                    paddingTop: 5,
                    backgroundColor:
                      expandTab == EXPAND_TAB_TYPE.OPERATION
                        ? Colors.lightPrimary
                        : null,
                    alignItems: "center",
                  },
                ]}>

                {/* <TouchableOpacity
              onPress={() => {
                // props.navigation.navigate('Kitchen');
                // setSelectedTab(1);
                // expandAction(1);
                // setCurrPage('Kitchen');
                CommonStore.update((s) => {
                  s.currPage = "Kitchen";
                  s.currPageStack = [...currPageStack, "Kitchen"];
                });

                if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                  CommonStore.update((s) => {
                    s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                  });
                }
              }}
              style={[
                styles.subBar,
                {
                  ...(currPage === "Kitchen" && highlightStyle),
                },
              ]}
            >
              <Text
                style={[
                  styles.expandedItems,
                  {
                    color:
                      currPage === "Kitchen"
                        ? Colors.primaryColor
                        : Colors.descriptionColor,
                  },
                  switchMerchant ? { fontSize: 10 } : {},
                ]}
              >
                Kitchen
              </Text>
            </TouchableOpacity> */}
                {
                  ((currOutlet && currOutlet.privileges &&
                    currOutlet.privileges.includes(PRIVILEGES_NAME.OPERATION))
                    && privileges.includes(PRIVILEGES_NAME.OPERATION) && !screensToBlock.includes('Ordering'))
                    ?
                    <TouchableOpacity
                      onPress={() => {
                        // props.navigation.navigate('Order'),
                        // setSelectedTab(1);
                        // expandAction(1);
                        CommonStore.update((s) => {
                          s.currPage = "Ordering - KooDoo BackOffice";
                          s.currPageStack = [
                            ...currPageStack,
                            "Ordering - KooDoo BackOffice",
                          ];
                          linkTo && linkTo(`${prefix}/ordering`);
                        });

                        if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                          CommonStore.update((s) => {
                            s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                          });
                        }
                      }}
                      style={[
                        styles.subBar,
                        {
                          ...(currPage === "Ordering - KooDoo BackOffice" &&
                            highlightStyle),
                        },
                      ]}
                    >
                      <Text
                        style={[
                          styles.expandedItems,
                          {
                            color:
                              currPage == "Ordering - KooDoo BackOffice"
                                ? Colors.primaryColor
                                : Colors.descriptionColor,
                          },
                          switchMerchant ? { fontSize: 9, height: "150%" } : {},
                        ]}
                      >
                        Ordering
                      </Text>
                    </TouchableOpacity>
                    :
                    <></>
                }
                {
                  ((currOutlet && currOutlet.privileges &&
                    currOutlet.privileges.includes(PRIVILEGES_NAME.OPERATION))
                    && privileges.includes(PRIVILEGES_NAME.OPERATION) && !screensToBlock.includes('Kitchen'))
                    ?
                    <TouchableOpacity
                      onPress={() => {
                        //props.navigation.navigate('Kitchen');
                        //setSelectedTab(1);
                        //expandAction(1);  
                        // setCurrPage('Kitchen');
                        CommonStore.update((s) => {
                          s.currPage = "Kitchen - KooDoo BackOffice";
                          s.currPageStack = [...currPageStack,
                            "Kitchen - KooDoo BackOffice"];
                          linkTo && linkTo(`${prefix}/kitchen`);

                        });

                        if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                          CommonStore.update((s) => {
                            s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                          });
                        }
                      }}
                      style={[
                        styles.subBar,
                        {
                          ...(currPage === "Kitchen - KooDoo BackOffice" &&
                            highlightStyle),
                        },
                      ]}
                    >
                      <Text
                        style={[
                          styles.expandedItems,
                          {
                            color:
                              currPage === "Kitchen - KooDoo BackOffice"
                                ? Colors.primaryColor
                                : Colors.descriptionColor,
                          },
                          switchMerchant ? { fontSize: 9, height: "150%" } : {},
                        ]}
                      >
                        Kitchen
                      </Text>
                    </TouchableOpacity>
                    :
                    <></>
                }
                {
                  ((currOutlet && currOutlet.privileges &&
                    currOutlet.privileges.includes(PRIVILEGES_NAME.OPERATION))
                    && privileges.includes(PRIVILEGES_NAME.OPERATION) && !screensToBlock.includes('Table'))
                    ?
                    <TouchableOpacity
                      onPress={() => {
                        // props.navigation.navigate('Table');
                        // setSelectedTab(1);
                        // expandAction(1);
                        // setCurrPage('Table');
                        CommonStore.update((s) => {
                          s.currPage = "Table - KooDoo BackOffice";
                          s.currPageStack = [...currPageStack, "Table - KooDoo BackOffice"];
                          linkTo && linkTo(`${prefix}/table`);
                        });


                        if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                          CommonStore.update((s) => {
                            s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                          });
                        }
                      }}
                      style={[
                        styles.subBar,
                        {
                          ...(currPage === "Table - KooDoo BackOffice" && highlightStyle),
                        },
                      ]}
                    >
                      <Text
                        style={[
                          styles.expandedItems,
                          {
                            color:
                              currPage === "Table - KooDoo BackOffice"
                                ? Colors.primaryColor
                                : Colors.descriptionColor,
                          },
                          switchMerchant ? { fontSize: 10 } : {},
                        ]}
                      >
                        Table
                      </Text>
                    </TouchableOpacity>
                    :
                    <></>
                }
                {
                  ((currOutlet && currOutlet.privileges &&
                    currOutlet.privileges.includes(PRIVILEGES_NAME.OPERATION))
                    && privileges.includes(PRIVILEGES_NAME.OPERATION) && !screensToBlock.includes('Dine_In'))
                    ?
                    <TouchableOpacity
                      onPress={() => {
                        // props.navigation.navigate('Order'),
                        // setSelectedTab(1);
                        // expandAction(1);
                        CommonStore.update((s) => {
                          s.currPage = "Order - KooDoo BackOffice";
                          s.currPageStack = [
                            ...currPageStack,
                            "Order - KooDoo BackOffice",
                          ];
                          linkTo && linkTo(`${prefix}/order`);
                        });

                        if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                          CommonStore.update((s) => {
                            s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                          });
                        }
                      }}
                      style={[
                        styles.subBar,
                        {
                          ...(currPage === "Order - KooDoo BackOffice" &&
                            highlightStyle),
                        },
                      ]}
                    >
                      <Text
                        style={[
                          styles.expandedItems,
                          {
                            color:
                              currPage == "Order - KooDoo BackOffice"
                                ? Colors.primaryColor
                                : Colors.descriptionColor,
                          },
                          switchMerchant ? { fontSize: 9, height: "150%" } : {},
                        ]}
                      >
                        Dine-In
                      </Text>
                    </TouchableOpacity>
                    :
                    <></>
                }

                {
                  ((currOutlet && currOutlet.privileges &&
                    currOutlet.privileges.includes(PRIVILEGES_NAME.OPERATION))
                    && privileges.includes(PRIVILEGES_NAME.OPERATION) && !screensToBlock.includes('Takeaway'))
                    ?
                    <TouchableOpacity
                      onPress={() => {
                        // props.navigation.navigate('Takeaway');
                        // setSelectedTab(1), expandAction(1),
                        CommonStore.update((s) => {
                          s.currPage = "Takeaway - KooDoo BackOffice";
                          s.currPageStack = [
                            ...currPageStack,
                            "Takeaway - KooDoo BackOffice",
                          ];
                          linkTo && linkTo(`${prefix}/takeaway`);
                        });

                        if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                          CommonStore.update((s) => {
                            s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                          });
                        }
                      }}
                      style={[
                        styles.subBar,
                        {
                          ...(currPage === "Takeaway - KooDoo BackOffice" &&
                            highlightStyle),
                        },
                      ]}
                    >
                      <Text
                        style={[
                          styles.expandedItems,
                          {
                            color:
                              currPage == "Takeaway - KooDoo BackOffice"
                                ? Colors.primaryColor
                                : Colors.descriptionColor,
                          },
                          switchMerchant ? { fontSize: 9, height: "150%" } : {},
                        ]}
                      >
                        Takeaway
                      </Text>
                    </TouchableOpacity>
                    :
                    <></>
                }

                {
                  ((currOutlet && currOutlet.privileges &&
                    currOutlet.privileges.includes(PRIVILEGES_NAME.OPERATION))
                    && privileges.includes(PRIVILEGES_NAME.OPERATION) && !screensToBlock.includes('Other_D'))
                    ?
                    <TouchableOpacity
                      onPress={() => {
                        CommonStore.update((s) => {
                          s.currPage = "Other Delivery - KooDoo BackOffice";
                          s.currPageStack = [
                            ...currPageStack,
                            "Other Delivery - KooDoo BackOffice",
                          ];
                          linkTo && linkTo(`${prefix}/other-delivery`);
                        });

                        if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                          CommonStore.update((s) => {
                            s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                          });
                        }
                      }}
                      style={[
                        styles.subBar,
                        {
                          ...(currPage === "Other Delivery - KooDoo BackOffice" &&
                            highlightStyle),
                        },
                      ]}
                    >
                      <Text
                        style={[
                          styles.expandedItems,
                          {
                            color:
                              currPage == "Other Delivery - KooDoo BackOffice"
                                ? Colors.primaryColor
                                : Colors.descriptionColor,
                          },
                          switchMerchant ? { fontSize: 9, height: "150%" } : {},
                        ]}
                      >
                        Other D.
                      </Text>
                    </TouchableOpacity>
                    :
                    <></>
                }

                {/* <TouchableOpacity
              onPress={() => {
                // props.navigation.navigate('Reservation');
                // setSelectedTab(1), expandAction(1),
                CommonStore.update((s) => {
                  s.currPage = "Reservation";
                  s.currPageStack = [...currPageStack, "Reservation"];
                });

                if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                  CommonStore.update((s) => {
                    s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                  });
                }
              }}
              style={[
                styles.subBar,
                {
                  ...(currPage === "Reservation" && highlightStyle),
                },
              ]}
            >
              <Text
                style={[
                  styles.expandedItems,
                  {
                    color:
                      currPage == "Reservation"
                        ? Colors.primaryColor
                        : Colors.descriptionColor,
                  },
                  switchMerchant ? { fontSize: 10 } : {},
                ]}
              >
                Reservation
              </Text>
            </TouchableOpacity> */}
                {/* <TouchableOpacity
              onPress={() => {
                // props.navigation.navigate('Queue');
                // setSelectedTab(1), expandAction(1),
                CommonStore.update((s) => {
                  s.currPage = "Queue";
                  s.currPageStack = [...currPageStack, "Queue"];
                });

                if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                  CommonStore.update((s) => {
                    s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                  });
                }
              }}
              style={[
                styles.subBar,
                {
                  ...(currPage === "Queue" && highlightStyle),
                },
              ]}
            >
              <Text
                style={[
                  styles.expandedItems,
                  {
                    color:
                      currPage == "Queue"
                        ? Colors.primaryColor
                        : Colors.descriptionColor,
                  },
                  switchMerchant ? { fontSize: 10 } : {},
                ]}
              >
                Queue
              </Text>
            </TouchableOpacity> */}

                {
                  ((currOutlet && currOutlet.privileges &&
                    currOutlet.privileges.includes(PRIVILEGES_NAME.OPERATION))
                    && privileges.includes(PRIVILEGES_NAME.OPERATION) && !screensToBlock.includes('History'))
                    ?
                    <TouchableOpacity
                      onPress={() => {
                        // props.navigation.navigate('History');
                        // setSelectedTab(1), expandAction(1),
                        CommonStore.update((s) => {
                          s.currPage = "History - KooDoo BackOffice";
                          s.currPageStack = [
                            ...currPageStack,
                            "History - KooDoo BackOffice",
                          ];
                          linkTo && linkTo(`${prefix}/history`);
                        });

                        if (expandTab !== EXPAND_TAB_TYPE.OPERATION) {
                          CommonStore.update((s) => {
                            s.expandTab = EXPAND_TAB_TYPE.OPERATION;
                          });
                        }
                      }}
                      style={[
                        styles.subBar,
                        {
                          ...(currPage === "History - KooDoo BackOffice" &&
                            highlightStyle),
                        },
                      ]}
                    >
                      <Text
                        style={[
                          styles.expandedItems,
                          {
                            color:
                              currPage == "History - KooDoo BackOffice"
                                ? Colors.primaryColor
                                : Colors.descriptionColor,
                          },
                          switchMerchant ? { fontSize: 9, height: "150%" } : {},
                        ]}
                      >
                        History
                      </Text>
                    </TouchableOpacity>
                    :
                    <></>
                }
              </View>
            ) : null}
        </View>
        :
        <></>
      }

      {
        (currOutlet && currOutlet.privileges &&
          currOutlet.privileges.includes(PRIVILEGES_NAME.RESERVATIONS))
          && privileges.includes(PRIVILEGES_NAME.RESERVATIONS) ?
          <View style={{
            ...sideBarItemsContainerStyleScale,
            ...(isMobile() && { width: windowWidth * 0.23 }),
          }}>
            <TouchableHighlight
              underlayColor={Colors.lightPrimary}
              onPress={() => {
                // setState({ expandEmployees: !expandEmployees, selectedTab: 9 }),
                // setExpandEmployees(!expandEmployees);
                // setSelectedTab(9);
                // expandAction(9)

                if (expandTab !== EXPAND_TAB_TYPE.RESERVATIONS) {
                  CommonStore.update((s) => {
                    s.expandTab = EXPAND_TAB_TYPE.RESERVATIONS;
                  });
                } else {
                  CommonStore.update((s) => {
                    s.expandTab = EXPAND_TAB_TYPE.NONE;
                  });
                }
              }}
              style={sideBarItemsStyleScale}
            >
              {expandTab == EXPAND_TAB_TYPE.RESERVATIONS ? (
                <View>
                  <View
                    style={[
                      styles.sidebarView,
                      {
                        backgroundColor: Colors.lightPrimary,
                        height: isTablet
                          ? windowHeight * 0.08
                          : windowHeight * 0.16,
                        width: isTablet
                          ? windowWidth * Styles.sideBarWidth
                          : "100%",
                        ...isMobile() && {
                          width: windowWidth * 0.23,
                          marginLeft: 'auto',
                          marginRight: 'auto',
                        },
                      },
                    ]}
                  >
                    <View style={[sidebarIconStyleScale, {
                      ...(isMobile() && {
                        width: 18,
                        height: 18
                      }),
                    },
                    ]}>
                      <InventoryG
                        width={
                          switchMerchant
                            ? windowWidth * 0.02
                            : windowWidth * iconSize
                        }
                        height={
                          switchMerchant
                            ? windowWidth * 0.02
                            : windowWidth * iconSize
                        }
                        {...(isMobile() && {
                          width: 18,
                          height: 18
                        })}
                      />
                    </View>
                    <View style={[styles.sidebarText, {}]}>
                      <Text
                        style={[
                          sidebarTextStyleScale,
                          {
                            color: Colors.primaryColor,
                            textAlign: "center",
                          },
                        ]}
                      >
                        Reservations
                      </Text>
                    </View>
                  </View>
                </View>
              ) : (
                <View>
                  <View
                    style={[
                      styles.sidebarView,
                      {
                        height: isTablet
                          ? windowHeight * 0.08
                          : windowHeight * 0.16,
                        width: isTablet
                          ? windowWidth * Styles.sideBarWidth
                          : "100%",
                        ...(isMobile() && {
                          width: windowWidth * 0.23,
                        }),
                      },
                    ]}
                  >
                    <View style={[sidebarIconStyleScale, {
                      ...(isMobile() && {
                        width: 18,
                        height: 18
                      }),
                    },
                    ]}>
                      <Inventory
                        width={
                          switchMerchant
                            ? windowWidth * 0.02
                            : windowWidth * iconSize
                        }
                        height={
                          switchMerchant
                            ? windowWidth * 0.02
                            : windowWidth * iconSize
                        }
                        {...(isMobile() && {
                          width: 18,
                          height: 18
                        })}
                      />
                    </View>
                    <View style={styles.sidebarText}>
                      <Text style={sidebarTextStyleScale}>Reservations</Text>
                    </View>
                  </View>
                </View>
              )}
            </TouchableHighlight>
            {expandTab === EXPAND_TAB_TYPE.RESERVATIONS ? (
              <View
                style={{
                  paddingVertical: 16,
                  paddingTop: 5,
                  backgroundColor:
                    expandTab === EXPAND_TAB_TYPE.RESERVATIONS
                      ? Colors.lightPrimary
                      : null,
                  alignItems: "center",
                }}
              >
                {!screensToBlock.includes('Manage_Reservation') ?
                  <TouchableOpacity
                    onPress={() => {
                      // props.navigation.navigate('Dashboard'),
                      // setSelectedTab(8), expandAction(8),
                      CommonStore.update((s) => {
                        s.currPage = 'Manage Reservation - KooDoo BackOffice';
                        s.currPageStack = [
                          ...currPageStack,
                          'Manage Reservation - KooDoo BackOffice',
                        ];
                        linkTo && linkTo(`${prefix}/manage-reservations`);
                      });

                      if (expandTab !== EXPAND_TAB_TYPE.RESERVATIONS) {
                        CommonStore.update((s) => {
                          s.expandTab = EXPAND_TAB_TYPE.RESERVATIONS;
                        });
                      }
                    }}
                    style={[
                      styles.subBar,
                      {
                        ...(currPage === 'Manage Reservation - KooDoo BackOffice' &&
                          highlightStyle),
                      },
                    ]}
                  >
                    <Text
                      style={[
                        styles.expandedItems,
                        {
                          color:
                            currPage == 'Manage Reservation - KooDoo BackOffice'
                              ? Colors.primaryColor
                              : Colors.descriptionColor,
                        },
                        switchMerchant ? { fontSize: 9 } : {},
                      ]}
                    >
                      Manage
                    </Text>
                  </TouchableOpacity>
                  :
                  <>
                  </>
                }

                <TouchableOpacity
                  onPress={() => {
                    // props.navigation.navigate('Dashboard'),
                    // setSelectedTab(8), expandAction(8),
                    CommonStore.update((s) => {
                      s.currPage = 'Calendar - KooDoo BackOffice';
                      s.currPageStack = [
                        ...currPageStack,
                        'Calendar - KooDoo BackOffice',
                      ];
                      linkTo && linkTo(`${prefix}/calendar`);
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.RESERVATIONS) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.RESERVATIONS;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'Calendar - KooDoo BackOffice' &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'Calendar - KooDoo BackOffice'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}
                  >
                    Overview
                  </Text>
                </TouchableOpacity>

                {!screensToBlock.includes('Analytics') ?
                  <TouchableOpacity
                    onPress={() => {
                      // props.navigation.navigate('Dashboard'),
                      // setSelectedTab(8), expandAction(8),
                      CommonStore.update((s) => {
                        s.currPage = 'Reservation Analytic - KooDoo BackOffice';
                        s.currPageStack = [
                          ...currPageStack,
                          'Reservation Analytic - KooDoo BackOffice',
                        ];
                        linkTo && linkTo(`${prefix}/reservation-analytic`);
                      });

                      if (expandTab !== EXPAND_TAB_TYPE.RESERVATIONS) {
                        CommonStore.update((s) => {
                          s.expandTab = EXPAND_TAB_TYPE.RESERVATIONS;
                        });
                      }
                    }}
                    style={[
                      styles.subBar,
                      {
                        ...(currPage === 'Reservation Analytic - KooDoo BackOffice' &&
                          highlightStyle),
                      },
                    ]}
                  >
                    <Text
                      style={[
                        styles.expandedItems,
                        {
                          color:
                            currPage == 'Reservation Analytic - KooDoo BackOffice'
                              ? Colors.primaryColor
                              : Colors.descriptionColor,
                        },
                        switchMerchant ? { fontSize: 9 } : {},
                      ]}
                    >
                      Analytics
                    </Text>
                  </TouchableOpacity>
                  :
                  <></>
                }

                {/* {!screensToBlock.includes('Deposit_Setting') ?
                  <TouchableOpacity
                    onPress={() => {
                      // props.navigation.navigate('Dashboard'),
                      // setSelectedTab(8), expandAction(8),
                      CommonStore.update((s) => {
                        s.currPage = 'Reservation Setting Deposit - KooDoo BackOffice';
                        s.currPageStack = [
                          ...currPageStack,
                          'Reservation Setting Deposit - KooDoo BackOffice',
                        ];
                        linkTo && linkTo(`${prefix}/reservation-setting-deposit`);
                      });

                      if (expandTab !== EXPAND_TAB_TYPE.RESERVATIONS) {
                        CommonStore.update((s) => {
                          s.expandTab = EXPAND_TAB_TYPE.RESERVATIONS;
                        });
                      }
                    }}
                    style={[
                      styles.subBar,
                      {
                        ...(currPage === 'Reservation Setting Deposit - KooDoo BackOffice' &&
                          highlightStyle),
                      },
                    ]}
                  >
                    <Text
                      style={[
                        styles.expandedItems,
                        {
                          color:
                            currPage == 'Reservation Setting Deposit - KooDoo BackOffice'
                              ? Colors.primaryColor
                              : Colors.descriptionColor,
                        },
                        switchMerchant ? { fontSize: 9 } : {},
                      ]}
                    >
                      {'Setting\nDeposit'}
                    </Text>
                  </TouchableOpacity>
                  :
                  <></>
                } */}
                {!screensToBlock.includes('Reservation_Setting') ?
                  <TouchableOpacity
                    onPress={() => {
                      // props.navigation.navigate('SettingReservation');

                      CommonStore.update((s) => {
                        s.currPage = "SettingsReservation";
                        s.currPageStack = [...currPageStack, "SettingsReservation"];
                        linkTo && linkTo(`${prefix}/setting-reservation`);
                      });

                      if (expandTab !== EXPAND_TAB_TYPE.RESERVATIONS) {
                        CommonStore.update((s) => {
                          s.expandTab = EXPAND_TAB_TYPE.RESERVATIONS;
                        });
                      }
                    }}
                    style={[
                      styles.subBar,
                      {
                        ...(currPage === "SettingsReservation" && highlightStyle),
                      },
                    ]}
                  >
                    <Text
                      style={[
                        styles.expandedItems,
                        {
                          color:
                            currPage == "SettingsReservation"
                              ? Colors.primaryColor
                              : Colors.descriptionColor,
                        },
                        switchMerchant ? { fontSize: 9 } : {},
                      ]}
                    >
                      Settings
                    </Text>
                  </TouchableOpacity>
                  :
                  <></>}
              </View>
            ) : null}
          </View>
          :
          <></>
      }

      {((currOutlet && currOutlet.privileges &&
        currOutlet.privileges.includes(PRIVILEGES_NAME.PRODUCT))
        && privileges.includes(PRIVILEGES_NAME.PRODUCT))
        ?
        <View style={{
          ...sideBarItemsContainerStyleScale,
          ...(isMobile() && { width: windowWidth * 0.23 }),
        }}>
          <TouchableHighlight
            underlayColor={Colors.lightPrimary}
            onPress={() => {
              // setState({
              //     expandProduct: !expandProduct,
              //     selectedTab: 2
              // });
              // setExpandProduct(!expandProduct);
              // setSelectedTab(2);
              // expandAction(2)

              if (expandTab !== EXPAND_TAB_TYPE.PRODUCT) {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.PRODUCT;
                });
              } else {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.NONE;
                });
              }
            }}
            style={sideBarItemsStyleScale}
          >
            {expandTab == EXPAND_TAB_TYPE.PRODUCT ? (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: Colors.lightPrimary,
                      height: isTablet
                        ? windowHeight * 0.08
                        : windowHeight * 0.16,
                      width: isTablet
                        ? windowWidth * Styles.sideBarWidth
                        : "100%",
                      ...isMobile() && {
                        width: windowWidth * 0.23,
                        marginLeft: 'auto',
                        marginRight: 'auto',
                      },
                    },
                  ]}
                >
                  <View style={sidebarIconStyleScale}>
                    <ProductG
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      {...(isMobile() && {
                        width: 18,
                        height: 18
                      })}
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text
                      style={[
                        sidebarTextStyleScale,
                        { color: Colors.primaryColor },
                      ]}
                    >
                      Product
                    </Text>
                  </View>
                </View>
              </View>
            ) : (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: Colors.whiteColor,
                      height: isTablet
                        ? windowHeight * 0.08
                        : windowHeight * 0.16,
                      width: isTablet
                        ? windowWidth * Styles.sideBarWidth
                        : "100%",
                      ...(isMobile() && {
                        width: windowWidth * 0.23,
                      }),
                    },
                  ]}
                >
                  <View style={[sidebarIconStyleScale, {
                    ...(isMobile() && {
                      width: 18,
                      height: 18
                    }),
                  },
                  ]}>
                    <Product
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      {...(isMobile() && {
                        width: 18,
                        height: 18
                      })}
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text style={sidebarTextStyleScale}>Product</Text>
                  </View>
                </View>
              </View>
            )}
          </TouchableHighlight>
          {expandTab === EXPAND_TAB_TYPE.PRODUCT ? (
            <View
              style={{
                paddingVertical: 16,
                paddingTop: 5,
                backgroundColor:
                  expandTab == EXPAND_TAB_TYPE.PRODUCT
                    ? Colors.lightPrimary
                    : null,
                alignItems: "center",
              }}
            >
              {!screensToBlock.includes('Product_Category') ?
                <TouchableOpacity
                  onPress={() => {
                    // props.navigation.navigate('ProductCategory');
                    // setSelectedTab(2), expandAction(2),
                    CommonStore.update((s) => {
                      s.currPage = "Product Category - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Product Category - KooDoo BackOffice",
                      ];
                    });

                    linkTo && linkTo(`${prefix}/product-category`);

                    if (expandTab !== EXPAND_TAB_TYPE.PRODUCT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.PRODUCT;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Product Category - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Product Category - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 10, height: "150%" } : {},
                    ]}
                  >{`Product\nCategory`}</Text>
                </TouchableOpacity>
                :
                <></>
              }
              {!screensToBlock.includes('Variant_AddOn') ?
                <TouchableOpacity
                  onPress={() => {
                    // props.navigation.navigate('ProductCategory');
                    // setSelectedTab(2), expandAction(2),
                    CommonStore.update((s) => {
                      s.currPage = "Variant Add-on - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Variant Add-on - KooDoo BackOffice",
                      ];
                    });

                    linkTo && linkTo(`${prefix}/variant-addon`);

                    if (expandTab !== EXPAND_TAB_TYPE.PRODUCT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.PRODUCT;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Variant Add-on - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'Variant Add-on - KooDoo BackOffice'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 10, height: '150%' } : {},
                    ]}>{`Variant\nAdd-On`}</Text>
                </TouchableOpacity>
                : <></>}

              {!screensToBlock.includes('Menu_Display') ?
                <TouchableOpacity
                  onPress={() => {
                    // props.navigation.navigate('ProductMenu');
                    // setSelectedTab(2), expandAction(2),
                    CommonStore.update((s) => {
                      s.currPage = "Product Menu - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Product Menu - KooDoo BackOffice",
                      ];
                    });

                    linkTo && linkTo(`${prefix}/product-menu`);

                    if (expandTab !== EXPAND_TAB_TYPE.PRODUCT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.PRODUCT;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Product Menu - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Product Menu - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: "150%" } : {},
                    ]}
                  >
                    {"Menu\nDisplay"}
                  </Text>
                </TouchableOpacity>
                :
                <></>
              }

              {!screensToBlock.includes('Manage_Preorder') ?
                <TouchableOpacity
                  onPress={() => {
                    // props.navigation.navigate('PreorderPackage');
                    // setSelectedTab(2), expandAction(2),
                    CommonStore.update((s) => {
                      s.currPage = "Preorder Package - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Preorder Package - KooDoo BackOffice",
                      ];
                    });

                    linkTo && linkTo(`${prefix}/preorder-package`);

                    if (expandTab !== EXPAND_TAB_TYPE.PRODUCT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.PRODUCT;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Preorder Package - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Preorder Package - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: "150%" } : {},
                    ]}
                  >{`Manage\nPreorder`}</Text>
                </TouchableOpacity>
                :
                <></>
              }

              {!screensToBlock.includes('Catalog') ?
                <TouchableOpacity
                  onPress={() => {
                    // props.navigation.navigate('PreorderPackage');
                    // setSelectedTab(2), expandAction(2),
                    CommonStore.update((s) => {
                      s.currPage = "Catalog - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Catalog - KooDoo BackOffice",
                      ];
                    });

                    linkTo && linkTo(`${prefix}/catalog`);

                    if (expandTab !== EXPAND_TAB_TYPE.PRODUCT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.PRODUCT;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Catalog - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Catalog - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: "150%" } : {},
                    ]}
                  >{`Catalog`}</Text>
                </TouchableOpacity>
                :
                <></>
              }
            </View>
          ) : null}
        </View>
        :
        <></>
      }

      {((currOutlet && currOutlet.privileges &&
        currOutlet.privileges.includes(PRIVILEGES_NAME.INVENTORY))
        && privileges.includes(PRIVILEGES_NAME.INVENTORY))
        ?
        <View style={sideBarItemsContainerStyleScale}>
          <TouchableHighlight
            underlayColor={Colors.lightPrimary}
            onPress={() => {
              // setState({ expandInventory: !expandInventory, selectedTab: 3 });
              // setExpandInventory(!expandInventory);
              // setSelectedTab(3);
              // expandAction(3)

              if (expandTab !== EXPAND_TAB_TYPE.INVENTORY) {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.INVENTORY;
                });
              } else {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.NONE;
                });
              }
            }}
            style={sideBarItemsStyleScale}
          >
            {expandTab == EXPAND_TAB_TYPE.INVENTORY ? (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: Colors.lightPrimary,
                      height: isTablet
                        ? windowHeight * 0.08
                        : windowHeight * 0.16,
                      width: isTablet
                        ? windowWidth * Styles.sideBarWidth
                        : "100%",
                      ...isMobile() && {
                        width: windowWidth * 0.23,
                        marginLeft: 'auto',
                        marginRight: 'auto',
                      },
                    },
                  ]}
                >
                  <View style={[sidebarIconStyleScale, {
                    ...(isMobile() && {
                      width: 18,
                      height: 18
                    }),
                  },
                  ]}>
                    <InventoryG
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      {...(isMobile() && {
                        width: 18,
                        height: 18
                      })}
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text
                      style={[
                        sidebarTextStyleScale,
                        { color: Colors.primaryColor },
                      ]}
                    >
                      Inventory
                    </Text>
                  </View>
                </View>
              </View>
            ) : (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: Colors.whiteColor,
                      height: isTablet
                        ? windowHeight * 0.08
                        : windowHeight * 0.16,
                      width: isTablet
                        ? windowWidth * Styles.sideBarWidth
                        : "100%",
                      ...(isMobile() && {
                        width: windowWidth * 0.23,
                      }),
                    },
                  ]}
                >
                  <View style={[sidebarIconStyleScale, {
                    ...(isMobile() && {
                      width: 18,
                      height: 18
                    }),
                  },
                  ]}>
                    <Inventory
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      {...(isMobile() && {
                        width: 18,
                        height: 18
                      })}
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text style={sidebarTextStyleScale}>Inventory</Text>
                  </View>
                </View>
              </View>
            )}
          </TouchableHighlight>
          {expandTab === EXPAND_TAB_TYPE.INVENTORY ? (
            <View
              style={{
                paddingVertical: 16,
                paddingTop: 5,
                backgroundColor:
                  expandTab == EXPAND_TAB_TYPE.INVENTORY
                    ? Colors.lightPrimary
                    : null,
                alignItems: "center",
              }}
            >
              {!screensToBlock.includes('Supplier') ?
                <TouchableOpacity
                  onPress={() => {
                    props.navigation.navigate("Supplier Product - KooDoo BackOffice", {
                      supplier: true,
                    });
                    // setSelectedTab(3), expandAction(3),
                    CommonStore.update((s) => {
                      s.currPage = "Supplier Product - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Supplier Product - KooDoo BackOffice",
                      ];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.INVENTORY) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.INVENTORY;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Supplier Product - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Supplier Product - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}
                  >
                    Supplier
                  </Text>
                </TouchableOpacity>
                :
                <></>
              }

              {!screensToBlock.includes('Inventory_Overview') ?
                <TouchableOpacity
                  onPress={() => {
                    props.navigation.navigate("Inventory Product - KooDoo BackOffice", {
                      lowStockAlert: true,
                    });
                    // setSelectedTab(3), expandAction(3),
                    CommonStore.update((s) => {
                      s.currPage = "Inventory Product - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Inventory Product - KooDoo BackOffice",
                      ];
                    });

                    linkTo && linkTo(`${prefix}/inventory-product`);

                    if (expandTab !== EXPAND_TAB_TYPE.INVENTORY) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.INVENTORY;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Inventory Product - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Inventory Product - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: "150%" } : {},
                    ]}
                  >{`Inventory\nOverview`}</Text>
                </TouchableOpacity>
                :
                <></>
              }

              {!screensToBlock.includes('Purchase_Order') ?
                <TouchableOpacity
                  onPress={() => {
                    // props.navigation.navigate('PurchaseOrder', {
                    //   purchaseOrder: true,
                    // });
                    // setSelectedTab(3), expandAction(3),
                    CommonStore.update((s) => {
                      s.currPage = "PurchaseOrder Product - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "PurchaseOrder - KooDoo BackOffice",
                      ];
                    });

                    linkTo && linkTo(`${prefix}/purchaseorder-product`);

                    if (expandTab !== EXPAND_TAB_TYPE.INVENTORY) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.INVENTORY;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "PurchaseOrder Product - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "PurchaseOrder Product - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: "150%" } : {},
                    ]}
                  >{`Purchase\nOrder`}</Text>
                </TouchableOpacity>
                :
                <></>
              }

              {!screensToBlock.includes('Stock_Transfer') ?
                <TouchableOpacity
                  onPress={() => {
                    // props.navigation.navigate('StockTransfer', {
                    //   stockTransfer: true,
                    // }),
                    // setSelectedTab(3), expandAction(3),
                    CommonStore.update((s) => {
                      s.currPage = "Stock Transfer Product - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Stock Transfer Product - KooDoo BackOffice",
                      ];
                    });

                    linkTo && linkTo(`${prefix}/stock-transfer-product`);

                    if (expandTab !== EXPAND_TAB_TYPE.INVENTORY) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.INVENTORY;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Stock Transfer Product - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Stock Transfer Product - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: "150%" } : {},
                    ]}
                  >
                    {"Stock\nTransfer"}
                  </Text>
                </TouchableOpacity>
                :
                <></>
              }

              {!screensToBlock.includes('Stock_Take') ?
                <TouchableOpacity
                  onPress={() => {
                    // props.navigation.navigate('StockTake', {stockTake: true}),
                    // setSelectedTab(3), expandAction(3),
                    CommonStore.update((s) => {
                      s.currPage = "StockTake Product - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "StockTake Product - KooDoo BackOffice",
                      ];
                    });

                    linkTo && linkTo(`${prefix}/stocktake-product`);

                    if (expandTab !== EXPAND_TAB_TYPE.INVENTORY) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.INVENTORY;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "StockTake Product - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "StockTake Product - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: "150%" } : {},
                    ]}
                  >
                    {"Stock\nTake"}
                  </Text>
                </TouchableOpacity>
                :
                <></>
              }

              {!screensToBlock.includes('Stock_Refund') ?
                <TouchableOpacity
                  onPress={() => {
                    // props.navigation.navigate('StockTakeProductScreen', { stockTake: true }),
                    // setSelectedTab(3), expandAction(3),
                    CommonStore.update((s) => {
                      s.currPage = "Stock Return Product - KooDoo BackOffice";
                      s.currPageStack = [...currPageStack, "StockTake"];
                    });
                    linkTo && linkTo(`${prefix}/stock-return-product`);

                    if (expandTab !== EXPAND_TAB_TYPE.INVENTORY) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.INVENTORY;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Stock Return Product - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Stock Return Product - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: "150%" } : {},
                    ]}
                  >
                    {"Stock\nReturn"}
                  </Text>
                </TouchableOpacity>
                :
                <></>
              }

            </View>
          ) : null}
        </View>
        :
        <></>
      }

      {((currOutlet && currOutlet.privileges &&
        currOutlet.privileges.includes(PRIVILEGES_NAME.INVENTORY_COMPOSITE))
        && privileges.includes(PRIVILEGES_NAME.INVENTORY_COMPOSITE))
        ?
        <View style={{
          ...sideBarItemsContainerStyleScale,
          ...(isMobile() && { width: windowWidth * 0.23 }),
        }}>
          <TouchableHighlight
            underlayColor={Colors.lightPrimary}
            onPress={() => {
              // setState({ expandInventory: !expandInventory, selectedTab: 3 });
              // setExpandInventory(!expandInventory);
              // setSelectedTab(3);
              // expandAction(3)

              if (expandTab !== EXPAND_TAB_TYPE.INVENTORY_COMPOSITE) {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.INVENTORY_COMPOSITE;
                });
              } else {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.NONE;
                });
              }
            }}
            style={[
              sideBarItemsStyleScale,
              // {
              //   marginTop: "10%",
              // },
            ]}
          >
            {expandTab == EXPAND_TAB_TYPE.INVENTORY_COMPOSITE ? (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: Colors.lightPrimary,
                      height: isTablet
                        ? windowHeight * 0.08
                        : windowHeight * 0.16,
                      width: isTablet
                        ? windowWidth * Styles.sideBarWidth
                        : "100%",
                      ...isMobile() && {
                        width: windowWidth * 0.23,
                        marginLeft: 'auto',
                        marginRight: 'auto',
                      },
                    },
                    switchMerchant
                      ? {
                        // borderWidth: 1,
                        // width: '48%'
                      }
                      : {},
                  ]}
                >
                  <View style={sidebarIconStyleScale}>
                    <InventoryG
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      {...(isMobile() && {
                        width: 18,
                        height: 18
                      })}
                    />
                  </View>
                  <View style={[styles.sidebarText, {}]}>
                    <Text
                      style={[
                        sidebarTextStyleScale,
                        {
                          color: Colors.primaryColor,
                          textAlign: "center",
                        },
                      ]}
                    >
                      {"Composite"}
                    </Text>
                  </View>
                  {/*<View style={[styles.sidebarArrow]}>
                    <View style={{ transform: [{ rotate: '90deg' }], width: arrowSize }}>
                      <Arrow width={arrowSize} height={arrowSize} />
                    </View>
                  </View>*/}
                </View>
              </View>
            ) : (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: Colors.whiteColor,
                      height: isTablet
                        ? windowHeight * 0.08
                        : windowHeight * 0.16,
                      width: isTablet
                        ? windowWidth * Styles.sideBarWidth
                        : "100%",
                      ...(isMobile() && {
                        width: windowWidth * 0.23,
                      }),
                    },
                    switchMerchant
                      ? {
                        // borderWidth: 1,
                        // width: '48%'
                      }
                      : {},
                  ]}
                >
                  <View style={[sidebarIconStyleScale, {
                    ...(isMobile() && {
                      width: 18,
                      height: 18
                    }),
                  },
                  ]}>
                    <Inventory
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      {...(isMobile() && {
                        width: 18,
                        height: 18
                      })}
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text
                      style={[
                        sidebarTextStyleScale,
                        {
                          textAlign: "center",
                        },
                      ]}
                    >
                      {"Composite"}
                    </Text>
                  </View>
                  {/*<View style={[styles.sidebarArrow]}>
                    <Arrow width={arrowSize} height={arrowSize} />
                  </View>*/}
                </View>
              </View>
            )}
          </TouchableHighlight>

          {expandTab === EXPAND_TAB_TYPE.INVENTORY_COMPOSITE ? (
            <View
              style={{
                //marginTop: '10%',
                paddingVertical: 16,
                // paddingTop: 5,
                paddingTop: 5,
                backgroundColor:
                  expandTab == EXPAND_TAB_TYPE.INVENTORY_COMPOSITE
                    ? Colors.lightPrimary
                    : null,
                alignItems: "center",
              }}
            >
              {!screensToBlock.includes('Supplier_Composite') ?
                <TouchableOpacity
                  onPress={() => {
                    // props.navigation.navigate('SupplierProduct', { supplier: true });
                    // setSelectedTab(3), expandAction(3),
                    CommonStore.update((s) => {
                      s.currPage = "Supplier - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Supplier - KooDoo BackOffice",
                      ];
                    });
                    linkTo && linkTo(`${prefix}/supplier`);

                    if (expandTab !== EXPAND_TAB_TYPE.INVENTORY_COMPOSITE) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.INVENTORY_COMPOSITE;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Supplier - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Supplier - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}
                  >
                    Supplier
                  </Text>
                </TouchableOpacity>
                :
                <></>
              }

              {!screensToBlock.includes('Inventory_Overview_Composite') ?
                <TouchableOpacity
                  onPress={() => {
                    // props.navigation.navigate('InventoryProduct', { lowStockAlert: true });
                    // setSelectedTab(3), expandAction(3),
                    CommonStore.update((s) => {
                      s.currPage = "Inventory - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Inventory - KooDoo BackOffice",
                      ];
                    });
                    linkTo && linkTo(`${prefix}/inventory`);

                    if (expandTab !== EXPAND_TAB_TYPE.INVENTORY_COMPOSITE) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.INVENTORY_COMPOSITE;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Inventory - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Inventory - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: "150%" } : {},
                    ]}
                  >{`Inventory\nOverview`}</Text>
                </TouchableOpacity>
                :
                <>
                </>
              }

              {!screensToBlock.includes('Purchase_Order_Composite') ?
                <TouchableOpacity
                  onPress={() => {
                    // props.navigation.navigate('PurchaseOrderProductScreen', {
                    //   purchaseOrder: true,
                    // });
                    // setSelectedTab(3), expandAction(3),
                    CommonStore.update((s) => {
                      s.currPage = "PurchaseOrder - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "PurchaseOrder - KooDoo BackOffice",
                      ];
                    });
                    linkTo && linkTo(`${prefix}/purchaseorder`);

                    if (expandTab !== EXPAND_TAB_TYPE.INVENTORY_COMPOSITE) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.INVENTORY_COMPOSITE;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage ===
                        "PurchaseOrder - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "PurchaseOrder - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: "150%" } : {},
                    ]}
                  >{`Purchase\nOrder`}</Text>
                </TouchableOpacity>
                :
                <></>
              }

              {!screensToBlock.includes('Stock_Transfer_Composite') ?
                <TouchableOpacity
                  onPress={() => {
                    // props.navigation.navigate('StockTransferProductScreen', {
                    //   stockTransfer: true,
                    // }),
                    // setSelectedTab(3), expandAction(3),
                    CommonStore.update((s) => {
                      s.currPage = "Stock Transfer - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Stock Transfer - KooDoo BackOffice",
                      ];
                    });
                    linkTo && linkTo(`${prefix}/stock-transfer`);

                    if (expandTab !== EXPAND_TAB_TYPE.INVENTORY_COMPOSITE) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.INVENTORY_COMPOSITE;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage ===
                        "Stock Transfer - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Stock Transfer - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: "150%" } : {},
                    ]}
                  >
                    {"Stock\nTransfer"}
                  </Text>
                </TouchableOpacity>
                :
                <></>
              }

              {!screensToBlock.includes('Stock_Take_Composite') ?
                <TouchableOpacity
                  onPress={() => {
                    // props.navigation.navigate('StockTakeProductScreen', { stockTake: true }),
                    // setSelectedTab(3), expandAction(3),
                    CommonStore.update((s) => {
                      s.currPage = "StockTake - KooDoo BackOffice";
                      s.currPageStack = [...currPageStack, "StockTake"];
                    });
                    linkTo && linkTo(`${prefix}/stocktake`);

                    if (expandTab !== EXPAND_TAB_TYPE.INVENTORY_COMPOSITE) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.INVENTORY_COMPOSITE;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "StockTake - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "StockTake - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: "150%" } : {},
                    ]}
                  >
                    {"Stock\nTake"}
                  </Text>
                </TouchableOpacity>
                :
                <></>
              }

              <TouchableOpacity
                onPress={() => {
                  // props.navigation.navigate('StockTakeProductScreen', { stockTake: true }),
                  // setSelectedTab(3), expandAction(3),
                  CommonStore.update((s) => {
                    s.currPage = "Work Order Item - KooDoo BackOffice";
                    s.currPageStack = [...currPageStack, "WorkOrderItem"];
                  });
                  linkTo && linkTo(`${prefix}/work-order-item`);

                  if (expandTab !== EXPAND_TAB_TYPE.INVENTORY_COMPOSITE) {
                    CommonStore.update((s) => {
                      s.expandTab = EXPAND_TAB_TYPE.INVENTORY_COMPOSITE;
                    });
                  }
                }}
                style={[
                  styles.subBar,
                  {
                    ...(currPage === "Work Order Item - KooDoo BackOffice" &&
                      highlightStyle),
                  },
                ]}
              >
                <Text
                  style={[
                    styles.expandedItems,
                    {
                      color:
                        currPage == "Work Order Item - KooDoo BackOffice"
                          ? Colors.primaryColor
                          : Colors.descriptionColor,
                    },
                    switchMerchant ? { fontSize: 9, height: "150%" } : {},
                  ]}
                >
                  {"Work Order\nItem List"}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={() => {
                  // props.navigation.navigate('StockTakeProductScreen', { stockTake: true }),
                  // setSelectedTab(3), expandAction(3),
                  CommonStore.update((s) => {
                    s.currPage = "Work Order - KooDoo BackOffice";
                    s.currPageStack = [...currPageStack, "WorkOrder"];
                  });
                  linkTo && linkTo(`${prefix}/work-order`);

                  if (expandTab !== EXPAND_TAB_TYPE.INVENTORY_COMPOSITE) {
                    CommonStore.update((s) => {
                      s.expandTab = EXPAND_TAB_TYPE.INVENTORY_COMPOSITE;
                    });
                  }
                }}
                style={[
                  styles.subBar,
                  {
                    ...(currPage === "Work Order - KooDoo BackOffice" &&
                      highlightStyle),
                  },
                ]}
              >
                <Text
                  style={[
                    styles.expandedItems,
                    {
                      color:
                        currPage == "Work Order - KooDoo BackOffice"
                          ? Colors.primaryColor
                          : Colors.descriptionColor,
                    },
                    switchMerchant ? { fontSize: 9, height: "150%" } : {},
                  ]}
                >
                  {"Work Order\nList"}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={() => {
                  // props.navigation.navigate('StockTakeProductScreen', { stockTake: true }),
                  // setSelectedTab(3), expandAction(3),
                  CommonStore.update((s) => {
                    s.currPage = "Composite Report - KooDoo BackOffice";
                    s.currPageStack = [...currPageStack, "Composite"];
                  });
                  linkTo && linkTo(`${prefix}/composite-report`);

                  if (expandTab !== EXPAND_TAB_TYPE.INVENTORY_COMPOSITE) {
                    CommonStore.update((s) => {
                      s.expandTab = EXPAND_TAB_TYPE.INVENTORY_COMPOSITE;
                    });
                  }
                }}
                style={[
                  styles.subBar,
                  {
                    ...(currPage === "Composite Report - KooDoo BackOffice" &&
                      highlightStyle),
                  },
                ]}
              >
                <Text
                  style={[
                    styles.expandedItems,
                    {
                      color:
                        currPage == "Composite Report - KooDoo BackOffice"
                          ? Colors.primaryColor
                          : Colors.descriptionColor,
                    },
                    switchMerchant ? { fontSize: 9, height: "150%" } : {},
                  ]}
                >
                  {"Composite\nReport"}
                </Text>
              </TouchableOpacity>

            </View>
          ) : null}
        </View>

        :
        <></>
      }

      {((currOutlet && currOutlet.privileges &&
        currOutlet.privileges.includes(PRIVILEGES_NAME.LOYALTY))
        && privileges.includes(PRIVILEGES_NAME.LOYALTY))
        ?
        <View style={{
          ...sideBarItemsContainerStyleScale,
          ...(isMobile() && { width: windowWidth * 0.23 }),
        }}>
          <TouchableHighlight
            underlayColor={Colors.lightPrimary}
            onPress={() => {
              // setExpandLoyaltyPoints(!expandLoyaltyPoints);
              // setSelectedTab(12);
              // expandAction(12)

              if (expandTab !== EXPAND_TAB_TYPE.LOYALTY) {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.LOYALTY;
                });
              } else {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.NONE;
                });
              }
            }}
            style={sideBarItemsStyleScale}
          >
            {expandTab == EXPAND_TAB_TYPE.LOYALTY ? (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: Colors.lightPrimary,
                      height: isTablet
                        ? windowHeight * 0.08
                        : windowHeight * 0.16,
                      width: isTablet
                        ? windowWidth * Styles.sideBarWidth
                        : "100%",
                      ...isMobile() && {
                        width: windowWidth * 0.23,
                        marginLeft: 'auto',
                        marginRight: 'auto',
                      },
                    },
                  ]}
                >
                  <View style={[sidebarIconStyleScale, {}]}>
                    <Loyalty
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      {...(isMobile() && {
                        width: 18,
                        height: 18
                      })}
                      color={Colors.primaryColor}
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text
                      style={[
                        sidebarTextStyleScale,
                        { color: Colors.primaryColor },
                      ]}
                    >
                      Loyalty
                    </Text>
                  </View>
                </View>
              </View>
            ) : (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: Colors.whiteColor,
                      height: isTablet
                        ? windowHeight * 0.08
                        : windowHeight * 0.16,
                      width: isTablet
                        ? windowWidth * Styles.sideBarWidth
                        : "100%",
                      ...(isMobile() && {
                        width: windowWidth * 0.23,
                      }),
                    },
                  ]}
                >
                  <View style={[sidebarIconStyleScale, {
                    ...(isMobile() && {
                      width: 18,
                      height: 18
                    }),
                  },
                  ]}>
                    <Loyalty
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      {...(isMobile() && {
                        width: 18,
                        height: 18
                      })}
                      color={Colors.descriptionColor}
                      style={{ opacity: 0.75 }}
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text style={sidebarTextStyleScale}>Loyalty</Text>
                  </View>
                </View>
              </View>
            )}
          </TouchableHighlight>
          {expandTab === EXPAND_TAB_TYPE.LOYALTY ? (
            <View
              style={{
                paddingVertical: 16,
                paddingTop: 5,
                backgroundColor:
                  expandTab == EXPAND_TAB_TYPE.LOYALTY
                    ? Colors.lightPrimary
                    : null,
                alignItems: "center",
              }}
            >
              {!screensToBlock.includes('Loyalty_Campaign') ?
                <TouchableOpacity
                  onPress={() => {
                    // props.navigation.navigate('Employee');
                    // setSelectedTab(9), expandAction(9),
                    CommonStore.update((s) => {
                      s.currPage = "Loyalty Campaign - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Loyalty Campaign - KooDoo BackOffice",
                      ];
                      s.selectedLoyaltyCampaignEdit = null;

                      linkTo && linkTo(`${prefix}/loyalty-campaign`);
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.LOYALTY) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.LOYALTY;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Loyalty Campaign - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage === "Loyalty Campaign - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: "150%" } : {},
                    ]}
                  >
                    {"Campaign"}
                  </Text>
                </TouchableOpacity>
                :
                <></>
              }

              {!screensToBlock.includes('Stamps') ?
                <TouchableOpacity
                  onPress={() => {
                    // props.navigation.navigate('LoyaltyStampScreen');
                    // setSelectedTab(12), expandAction(12),
                    CommonStore.update((s) => {
                      s.currPage = "Loyalty Stamps - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Loyalty Stamps - KooDoo BackOffice",
                      ];
                      linkTo && linkTo(`${prefix}/loyalty-stamps-list`);
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.LOYALTY) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.LOYALTY;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Loyalty Stamps - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Loyalty Stamps - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: "150%" } : {},
                    ]}
                  >
                    {"Stamps"}
                  </Text>
                </TouchableOpacity>
                :
                <></>
              }

              {/*!screensToBlock.includes('Sign_Up_Reward') ?
                <TouchableOpacity
                  onPress={() => {
                    // props.navigation.navigate('Employee');
                    // setSelectedTab(9), expandAction(9),
                    CommonStore.update((s) => {
                      s.currPage = "Sign Up Reward - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Sign Up Reward - KooDoo BackOffice",
                      ];
                      linkTo && linkTo(`${prefix}/sign-up-reward`);
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.LOYALTY) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.LOYALTY;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Sign Up Reward - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage === "Sign Up Reward - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: "150%" } : {},
                    ]}
                  >
                    {"Sign Up\nReward"}
                  </Text>
                </TouchableOpacity>
                :
                <></>
              */}

              {/* <TouchableOpacity
              onPress={() => {
                // props.navigation.navigate('SettingCredit');
                // setSelectedTab(12), expandAction(12),
                CommonStore.update((s) => {
                  s.currPage = "Loyalty Points - KooDoo BackOffice";
                  s.currPageStack = [...currPageStack, "Loyalty Points - KooDoo BackOffice"];
                  linkTo && linkTo(`${prefix}/loyalty-point-list`);
                });

                if (expandTab !== EXPAND_TAB_TYPE.LOYALTY) {
                  CommonStore.update((s) => {
                    s.expandTab = EXPAND_TAB_TYPE.LOYALTY;
                  });
                }
              }}
              style={[
                styles.subBar,
                {
                  ...(currPage === "Loyalty Points - KooDoo BackOffice" && highlightStyle),
                },
              ]}
            >
              <Text
                style={[
                  styles.expandedItems,
                  {
                    color:
                      currPage === "Loyalty Points - KooDoo BackOffice"
                        ? Colors.primaryColor
                        : Colors.descriptionColor,
                  },
                  switchMerchant ? { fontSize: 9, height: "150%" } : {},
                ]}
              >
                {"Loyalty\nPoints"}
              </Text>
            </TouchableOpacity> */}

              {!screensToBlock.includes('Pay_Earn') ?
                <TouchableOpacity
                  onPress={() => {
                    // props.navigation.navigate('LoyaltyStampScreen');
                    // setSelectedTab(12), expandAction(12),
                    CommonStore.update((s) => {
                      s.currPage = "Pay & Earn - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Pay & Earn - KooDoo BackOffice",
                      ];
                      linkTo && linkTo(`${prefix}/loyalty-pay-n-earn`);
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.LOYALTY) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.LOYALTY;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Pay & Earn - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Pay & Earn - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: "150%" } : {},
                    ]}
                  >
                    {`Pay & \n Earn`}
                  </Text>
                </TouchableOpacity>
                :
                <></>
              }

              {!screensToBlock.includes('Reward_Redemption') ?
                <TouchableOpacity
                  onPress={() => {
                    // props.navigation.navigate('LoyaltyStampScreen');
                    // setSelectedTab(12), expandAction(12),
                    CommonStore.update((s) => {
                      s.currPage = "Reward & Redemption - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Reward & Redemption - KooDoo BackOffice",
                      ];
                      linkTo && linkTo(`${prefix}/loyalty-reward-n-redemption`);
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.LOYALTY) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.LOYALTY;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Reward & Redemption - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Reward & Redemption - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: "150%" } : {},
                    ]}
                  >
                    {"Reward &\nRedemption"}
                  </Text>
                </TouchableOpacity>
                :
                <></>
              }

              {!screensToBlock.includes('Credit_Type') ?
                <TouchableOpacity
                  onPress={() => {
                    CommonStore.update((s) => {
                      s.currPage = "Credit Type - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Credit Type - KooDoo BackOffice",
                      ];
                      linkTo && linkTo(`${prefix}/credit-type-list`);
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.LOYALTY) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.LOYALTY;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Credit Type - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Credit Type - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: "150%" } : {},
                    ]}
                  >
                    {"Credit\nType"}
                  </Text>
                </TouchableOpacity>
                :
                <></>
              }

              {!screensToBlock.includes('Credit_Type_Report') ?
                <TouchableOpacity
                  onPress={() => {
                    CommonStore.update((s) => {
                      s.currPage = "Credit Type Report - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Credit Type Report - KooDoo BackOffice",
                      ];
                      linkTo && linkTo(`${prefix}/credit-type-report`);
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.LOYALTY) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.LOYALTY;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Credit Type Report - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Credit Type Report - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: "150%" } : {},
                    ]}
                  >
                    {`Credit\nType\nReport`}
                  </Text>
                </TouchableOpacity>
                :
                <></>
              }

              {!screensToBlock.includes('Loyalty_Report') ?
                <TouchableOpacity
                  onPress={() => {
                    CommonStore.update((s) => {
                      s.currPage = "Loyalty Report - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Loyalty Report - KooDoo BackOffice",
                      ];
                      linkTo && linkTo(`${prefix}/loyalty-report`);
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.LOYALTY) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.LOYALTY;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Loyalty Report - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Loyalty Report - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: "150%" } : {},
                    ]}
                  >
                    {"Loyalty\nReport"}
                  </Text>
                </TouchableOpacity>
                :
                <></>
              }

              {!screensToBlock.includes('Loyalty_Setting') ?
                <TouchableOpacity
                  onPress={() => {
                    CommonStore.update((s) => {
                      s.currPage = "Loyalty Setting - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Loyalty Setting - KooDoo BackOffice",
                      ];
                      linkTo && linkTo(`${prefix}/setting-loyalty`);
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.LOYALTY) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.LOYALTY;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Loyalty Setting - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Loyalty Setting - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: "150%" } : {},
                    ]}
                  >
                    {`Loyalty\nSettings`}
                  </Text>
                </TouchableOpacity>
                :
                <></>
              }

            </View>
          ) : null}
        </View>
        :
        <></>
      }

      {((currOutlet && currOutlet.privileges &&
        currOutlet.privileges.includes(PRIVILEGES_NAME.PROMOTION))
        && privileges.includes(PRIVILEGES_NAME.PROMOTION))
        ?
        <View style={{
          ...sideBarItemsContainerStyleScale,
          ...(isMobile() && { width: windowWidth * 0.23 }),
        }}>
          <TouchableHighlight
            underlayColor={Colors.lightPrimary}
            onPress={() => {
              // setExpandPromotions(!expandPromotions);
              //  setSelectedTab(11);
              //  expandAction(11)
              //  props.navigation.navigate("PromotionList")
              // setExpandCRM(!expandPromotions);
              // setSelectedTab(11);
              // expandAction(11)

              if (expandTab !== EXPAND_TAB_TYPE.PROMOTION) {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.PROMOTION;
                });
              } else {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.NONE;
                });
              }
            }}
            style={sideBarItemsStyleScale}
          >
            {expandTab == EXPAND_TAB_TYPE.PROMOTION ? (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: Colors.lightPrimary,
                      height: isTablet
                        ? windowHeight * 0.08
                        : windowHeight * 0.16,
                      width: isTablet
                        ? windowWidth * Styles.sideBarWidth
                        : "100%",
                      ...isMobile() && {
                        width: windowWidth * 0.23,
                        marginLeft: 'auto',
                        marginRight: 'auto',
                      },
                    },
                  ]}
                >
                  <View style={sidebarIconStyleScale}>
                    <PromotionsG
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      {...(isMobile() && {
                        width: 18,
                        height: 18
                      })}
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text
                      style={[
                        sidebarTextStyleScale,
                        { color: Colors.primaryColor },
                      ]}
                    >
                      Promotion
                    </Text>
                  </View>
                </View>
              </View>
            ) : (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: Colors.whiteColor,
                      height: isTablet
                        ? windowHeight * 0.08
                        : windowHeight * 0.16,
                      width: isTablet
                        ? windowWidth * Styles.sideBarWidth
                        : "100%",
                      ...(isMobile() && {
                        width: windowWidth * 0.23,
                      }),
                    },
                  ]}
                >
                  <View style={[sidebarIconStyleScale, {
                    ...(isMobile() && {
                      width: 18,
                      height: 18
                    }),
                  },
                  ]}>
                    <Promotions
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      {...(isMobile() && {
                        width: 18,
                        height: 18
                      })}
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text style={sidebarTextStyleScale}>Promotion</Text>
                  </View>
                </View>
              </View>
            )}
          </TouchableHighlight>
          {expandTab === EXPAND_TAB_TYPE.PROMOTION ? (
            <View>
              <View
                style={{
                  paddingVertical: 16,
                  paddingTop: 5,
                  backgroundColor:
                    expandTab == EXPAND_TAB_TYPE.PROMOTION
                      ? Colors.lightPrimary
                      : null,
                  alignItems: "center",
                }}
              >
                {!screensToBlock.includes('Promotion_List') ?
                  <TouchableOpacity
                    onPress={() => {
                      // props.navigation.navigate('PromotionList', {selectedTab: 11}),
                      // setSelectedTab(11), expandAction(11),
                      CommonStore.update((s) => {
                        s.currPage = "Promotion List - KooDoo BackOffice";
                        s.currPageStack = [
                          ...currPageStack,
                          "Promotion List - KooDoo BackOffice",
                        ];
                      });

                      linkTo && linkTo(`${prefix}/promotion-list`);

                      if (expandTab !== EXPAND_TAB_TYPE.PROMOTION) {
                        CommonStore.update((s) => {
                          s.expandTab = EXPAND_TAB_TYPE.PROMOTION;
                        });
                      }
                    }}
                    style={[
                      styles.subBar,
                      {
                        ...(currPage === "Promotion List - KooDoo BackOffice" &&
                          highlightStyle),
                      },
                    ]}
                  >
                    <Text
                      style={[
                        styles.expandedItems,
                        {
                          color:
                            currPage == "Promotion List - KooDoo BackOffice"
                              ? Colors.primaryColor
                              : Colors.descriptionColor,
                        },
                        switchMerchant ? { fontSize: 9, height: "150%" } : {},
                      ]}
                    >
                      {"Promotion\nList"}
                    </Text>
                  </TouchableOpacity>
                  :
                  <></>
                }

                {!screensToBlock.includes('Add_Promotion') ?
                  <TouchableOpacity
                    onPress={() => {
                      // props.navigation.navigate('PromotionList', {selectedTab: 11}),
                      // setSelectedTab(11), expandAction(11),
                      CommonStore.update((s) => {
                        s.currPage = "Promotion - KooDoo BackOffice";
                        s.currPageStack = [
                          ...currPageStack,
                          "Promotion - KooDoo BackOffice",
                        ];
                      });

                      linkTo && linkTo(`${prefix}/promotion`);

                      if (expandTab !== EXPAND_TAB_TYPE.PROMOTION) {
                        CommonStore.update((s) => {
                          s.expandTab = EXPAND_TAB_TYPE.PROMOTION;
                        });
                      }
                    }}
                    style={[
                      styles.subBar,
                      {
                        ...(currPage === "Promotion - KooDoo BackOffice" &&
                          highlightStyle),
                      },
                    ]}
                  >
                    <Text
                      style={[
                        styles.expandedItems,
                        {
                          color:
                            currPage == "Promotion - KooDoo BackOffice"
                              ? Colors.primaryColor
                              : Colors.descriptionColor,
                        },
                        switchMerchant ? { fontSize: 9, height: "150%" } : {},
                      ]}
                    >
                      {"Add\nPromotion"}
                    </Text>
                  </TouchableOpacity>
                  :
                  <></>
                }

                {!screensToBlock.includes('Promotion_Report') ?
                  <TouchableOpacity
                    onPress={() => {
                      //   props.navigation.navigate('PromotionReport', {
                      //     selectedTab: 11,
                      //   }),
                      // setSelectedTab(11), expandAction(11),
                      CommonStore.update((s) => {
                        s.currPage = "Promotion Report - KooDoo BackOffice";
                        s.currPageStack = [
                          ...currPageStack,
                          "Promotion Report - KooDoo BackOffice",
                        ];
                      });

                      linkTo && linkTo(`${prefix}/promotion-report`);

                      if (expandTab !== EXPAND_TAB_TYPE.PROMOTION) {
                        CommonStore.update((s) => {
                          s.expandTab = EXPAND_TAB_TYPE.PROMOTION;
                        });
                      }
                    }}
                    style={[
                      styles.subBar,
                      {
                        ...(currPage === "Promotion Report - KooDoo BackOffice" &&
                          highlightStyle),
                      },
                    ]}
                  >
                    <Text
                      style={[
                        styles.expandedItems,
                        {
                          color:
                            currPage == "Promotion Report - KooDoo BackOffice"
                              ? Colors.primaryColor
                              : Colors.descriptionColor,
                        },
                        switchMerchant ? { fontSize: 9, height: "150%" } : {},
                      ]}
                    >{`Promotion\nReport`}</Text>
                  </TouchableOpacity>
                  :
                  <></>
                }

              </View>
            </View>
          ) : null}
        </View>
        :
        <></>
      }

      {((currOutlet && currOutlet.privileges &&
        currOutlet.privileges.includes(PRIVILEGES_NAME.UPSELLING))
        && privileges.includes(PRIVILEGES_NAME.UPSELLING))
        ?
        <View style={{
          ...sideBarItemsContainerStyleScale,
          ...(isMobile() && { width: windowWidth * 0.23 }),
        }}>
          <TouchableHighlight
            underlayColor={Colors.lightPrimary}
            onPress={() => {
              // setExpandLoyaltyPoints(!expandLoyaltyPoints);
              // setSelectedTab(12);
              // expandAction(12)

              if (expandTab !== EXPAND_TAB_TYPE.UPSELLING) {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.UPSELLING;
                });
              } else {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.NONE;
                });
              }
            }}
            style={sideBarItemsStyleScale}>
            {expandTab == EXPAND_TAB_TYPE.UPSELLING ? (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: Colors.lightPrimary,
                      height: isTablet
                        ? windowHeight * 0.08
                        : windowHeight * 0.16,
                      width: isTablet
                        ? windowWidth * Styles.sideBarWidth
                        : "100%",
                      ...isMobile() && {
                        width: windowWidth * 0.23,
                        marginLeft: 'auto',
                        marginRight: 'auto',
                      },
                    },
                  ]}
                >
                  <View style={sidebarIconStyleScale}>
                    <ReportG
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      {...(isMobile() && {
                        width: 18,
                        height: 18
                      })}
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text
                      style={[
                        sidebarTextStyleScale,
                        { color: Colors.primaryColor },
                      ]}>
                      Upselling
                    </Text>
                  </View>
                </View>
              </View>
            ) : (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: Colors.whiteColor,
                      height: isTablet
                        ? windowHeight * 0.08
                        : windowHeight * 0.16,
                      width: isTablet
                        ? windowWidth * Styles.sideBarWidth
                        : "100%",
                      ...(isMobile() && {
                        width: windowWidth * 0.23,
                      }),
                    },
                  ]}
                >
                  <View style={[sidebarIconStyleScale, {
                    ...(isMobile() && {
                      width: 18,
                      height: 18
                    }),
                  },
                  ]}>
                    <Report
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      {...(isMobile() && {
                        width: 18,
                        height: 18
                      })}
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text style={sidebarTextStyleScale}>Upselling</Text>
                  </View>
                </View>
              </View>
            )}
          </TouchableHighlight>

          {expandTab === EXPAND_TAB_TYPE.UPSELLING ? (
            <View
              style={{
                paddingVertical: 16,
                paddingTop: 5,
                backgroundColor:
                  expandTab == EXPAND_TAB_TYPE.UPSELLING
                    ? Colors.lightPrimary
                    : null,
                alignItems: 'center',
              }}>

              {!screensToBlock.includes('Upselling_List') ?
                <TouchableOpacity
                  onPress={() => {
                    CommonStore.update((s) => {
                      s.currPage = "Upselling List - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Upselling List - KooDoo BackOffice",
                      ];
                      linkTo && linkTo(`${prefix}/upselling-list`);
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.UPSELLING) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.UPSELLING;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Upselling List - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'Upselling List - KooDoo BackOffice'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: '150%' } : {},
                    ]}>
                    {'Upselling\nList'}
                  </Text>
                </TouchableOpacity>
                : <></>}

              {!screensToBlock.includes('Upselling_Campaign') ?
                <TouchableOpacity
                  onPress={() => {
                    CommonStore.update((s) => {
                      s.currPage = "Upselling Campaign - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Upselling Campaign - KooDoo BackOffice",
                      ];
                      linkTo && linkTo(`${prefix}/upselling-campaign`);

                      s.selectedUpsellingCampaignEdit = null;
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.UPSELLING) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.UPSELLING;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Upselling Campaign - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'Upselling Campaign - KooDoo BackOffice'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: '150%' } : {},
                    ]}>
                    {'Upselling\nCampaign'}
                  </Text>
                </TouchableOpacity>
                : <></>}

              {/* {!screensToBlock.includes('Upselling_Report') ?
                <TouchableOpacity
                  onPress={() => {
                    props.navigation.navigate('UpsellingReport');
                    // setSelectedTab(9), expandAction(9),
                    CommonStore.update((s) => {
                      s.currPage = 'UpsellingReport';
                      s.currPageStack = [...currPageStack, 'UpsellingReport'];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.UPSELLING) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.UPSELLING;
                      });
                    }

                    logEventAnalytics({
                      eventName: ANALYTICS.MODULE_UPSELLING_REPORT,
                      eventNameParsed: ANALYTICS_PARSED.MODULE_UPSELLING_REPORT,
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'UpsellingReport' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'UpsellingReport'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: '150%' } : {},
                    ]}>{`Upselling\nReport`}</Text>
                </TouchableOpacity>
                : <></>} */}
            </View>
          ) : null}
        </View>
        :
        <></>
      }

      <View style={sideBarItemsContainerStyleScale}>
        <View style={{
          ...sideBarItemsContainerStyleScale,
          ...(isMobile() && { width: windowWidth * 0.23 }),
        }}>
          <TouchableHighlight
            underlayColor={Colors.lightPrimary}
            onPress={() => {
              // setState({ expandReport: !expandReport, selectedTab: 8 }),
              // setExpandReport(!expandReport);
              // setSelectedTab(8);
              // expandAction(8)

              if (expandTab !== EXPAND_TAB_TYPE.REPORT) {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.REPORT;
                });
              } else {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.NONE;
                });
              }
            }}
            style={sideBarItemsStyleScale}
          >
            {expandTab == EXPAND_TAB_TYPE.REPORT ? (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: Colors.lightPrimary,
                      height: isTablet
                        ? windowHeight * 0.08
                        : windowHeight * 0.16,
                      width: isTablet
                        ? windowWidth * Styles.sideBarWidth
                        : "100%",
                      ...isMobile() && {
                        width: windowWidth * 0.23,
                        marginLeft: 'auto',
                        marginRight: 'auto',
                      },
                    },
                  ]}
                >
                  <View style={sidebarIconStyleScale}>
                    <ReportG
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      {...(isMobile() && {
                        width: 18,
                        height: 18
                      })}
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text
                      style={[
                        sidebarTextStyleScale,
                        { color: Colors.primaryColor },
                      ]}
                    >
                      Report
                    </Text>
                  </View>
                </View>
              </View>
            ) : (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: Colors.whiteColor,
                      height: isTablet
                        ? windowHeight * 0.08
                        : windowHeight * 0.16,
                      width: isTablet
                        ? windowWidth * Styles.sideBarWidth
                        : "100%",
                      ...(isMobile() && {
                        width: windowWidth * 0.23,
                      }),
                    },
                  ]}
                >
                  <View style={[sidebarIconStyleScale, {
                    ...(isMobile() && {
                      width: 18,
                      height: 18
                    }),
                  },
                  ]}>
                    <Report
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      {...(isMobile() && {
                        width: 18,
                        height: 18
                      })}
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text style={sidebarTextStyleScale}>Report</Text>
                  </View>
                </View>
              </View>
            )}
          </TouchableHighlight>
          {expandTab == EXPAND_TAB_TYPE.REPORT ? (
            <View
              style={{
                paddingVertical: 16,
                paddingTop: 5,
                backgroundColor:
                  expandTab === EXPAND_TAB_TYPE.REPORT
                    ? Colors.lightPrimary
                    : null,
                alignItems: "center",
              }}
            >
              {/* <TouchableOpacity
                            onPress={() => { props.navigation.navigate("ReportStockValue"), setSelectedTab(8), expandAction(8) }}
                            style={styles.subBar}>
                            <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>Stock Value</Text>
                        </TouchableOpacity> */}
              <TouchableOpacity
                onPress={() => {
                  // props.navigation.navigate('Dashboard'),
                  // setSelectedTab(8), expandAction(8),
                  CommonStore.update((s) => {
                    s.currPage = "Dashboard - KooDoo BackOffice";
                    s.currPageStack = [
                      ...currPageStack,
                      "Dashboard - KooDoo BackOffice",
                    ];
                    linkTo && linkTo(`${prefix}/dashboard`);
                  });

                  if (expandTab !== EXPAND_TAB_TYPE.REPORT) {
                    CommonStore.update((s) => {
                      s.expandTab = EXPAND_TAB_TYPE.REPORT;
                    });
                  }
                }}
                style={[
                  styles.subBar,
                  {
                    ...(currPage === "Dashboard - KooDoo BackOffice" &&
                      highlightStyle),
                  },
                ]}
              >
                <Text
                  style={[
                    styles.expandedItems,
                    {
                      color:
                        currPage == "Dashboard - KooDoo BackOffice"
                          ? Colors.primaryColor
                          : Colors.descriptionColor,
                    },
                    switchMerchant ? { fontSize: 9 } : {},
                  ]}
                >
                  Dashboard
                </Text>
              </TouchableOpacity>

              {!screensToBlock.includes('Aov_Report') ?
                <TouchableOpacity
                  onPress={() => {
                    CommonStore.update((s) => {
                      s.currPage = "AOV Report - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "AOV Report - KooDoo BackOffice",
                      ];
                      linkTo && linkTo(`${prefix}/aov-report`);
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.REPORT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.REPORT;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "AOV Report - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "AOV Report - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: "150%" } : {},
                    ]}
                  >
                    AOV
                  </Text>
                </TouchableOpacity>
                :
                <></>
              }

              {!screensToBlock.includes('Order_Count_Report') ?
                <TouchableOpacity
                  onPress={() => {
                    CommonStore.update((s) => {
                      s.currPage = "Order Count Report - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Order Count Report - KooDoo BackOffice",
                      ];
                      linkTo && linkTo(`${prefix}/order-count-report`);
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.REPORT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.REPORT;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Order Count Report - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Order Count Report - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: "150%" } : {},
                    ]}
                  >
                    {'Order\nCount'}
                  </Text>
                </TouchableOpacity>
                :
                <></>
              }

              {!screensToBlock.includes('Revisit_Count_Report') ?
                <TouchableOpacity
                  onPress={() => {
                    CommonStore.update((s) => {
                      s.currPage = "Revisit Count Report - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Revisit Count Report - KooDoo BackOffice",
                      ];
                      linkTo && linkTo(`${prefix}/revisit-count-report`);
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.REPORT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.REPORT;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Revisit Count Report - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Revisit Count Report - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: "150%" } : {},
                    ]}
                  >
                    {'Revisit\nCount'}
                  </Text>
                </TouchableOpacity>
                :
                <></>
              }

              {!screensToBlock.includes('Overview_Report') ?
                <TouchableOpacity
                  onPress={() => {
                    CommonStore.update((s) => {
                      s.currPage = "Overview Report - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Overview Report - KooDoo BackOffice",
                      ];
                      linkTo && linkTo(`${prefix}/overview-report`);
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.REPORT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.REPORT;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Overview Report - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Overview Report - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: "150%" } : {},
                    ]}
                  >
                    Overview
                  </Text>
                </TouchableOpacity>
                :
                <></>
              }

              {(!screensToBlock.includes('Upselling_Report')) ?
                <TouchableOpacity
                  onPress={() => {
                    CommonStore.update((s) => {
                      s.currPage = "Upselling Report - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Upselling Report - KooDoo BackOffice",
                      ];
                      linkTo && linkTo(`${prefix}/upselling-report`);
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.REPORT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.REPORT;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'Upselling Report - KooDoo BackOffice' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'Upselling Report - KooDoo BackOffice'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}>
                    Upselling
                  </Text>
                </TouchableOpacity>
                : <></>}

              {(!screensToBlock.includes('Upselling_Revenue_Report')) ?
                <TouchableOpacity
                  onPress={() => {
                    CommonStore.update((s) => {
                      s.currPage = "Upselling Revenue Report - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Upselling Revenue Report - KooDoo BackOffice",
                      ];
                      linkTo && linkTo(`${prefix}/upselling-revenue-report`);
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.REPORT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.REPORT;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === 'Upselling Revenue Report - KooDoo BackOffice' && highlightStyle),
                    },
                  ]}>
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == 'Upselling Revenue Report - KooDoo BackOffice'
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}>
                    {`Upselling\nRevenue`}
                  </Text>
                </TouchableOpacity>
                : <></>}

              {!screensToBlock.includes('Product_Report') ?
                <TouchableOpacity
                  onPress={() => {
                    CommonStore.update((s) => {
                      s.currPage = "Product Report - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Product Report - KooDoo BackOffice",
                      ];
                      linkTo && linkTo(`${prefix}/sales-by-product-report`);
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.REPORT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.REPORT;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Product Report - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Product Report - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: "150%" } : {},
                    ]}
                  >
                    Product
                  </Text>
                </TouchableOpacity>
                :
                <></>
              }

              {/* <TouchableOpacity
                            onPress={() => { props.navigation.navigate('Inventory'), setSelectedTab(8), expandAction(8) }}
                            style={[styles.subBar, { left: Dimensions.get('screen').width * 0.045 }]}>
                            <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>Inventory</Text>
                        </TouchableOpacity> */}
              {/* {!screensToBlock.includes('Category_Report') ?
                <TouchableOpacity
                  onPress={() => {
                    CommonStore.update((s) => {
                      s.currPage = "Category Report - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Category Report - KooDoo BackOffice",
                      ];
                      linkTo && linkTo(`${prefix}/sales-by-category-report`);
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.REPORT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.REPORT;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Category Report - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Category Report - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: "150%" } : {},
                    ]}
                  >
                    Category
                  </Text>
                </TouchableOpacity>
                :
                <></>
              } */}

              {!screensToBlock.includes('Category_Product_Report') ?
                <TouchableOpacity
                  onPress={() => {
                    CommonStore.update((s) => {
                      s.currPage = "Product Category Report - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Product Category Report - KooDoo BackOffice",
                      ];
                      linkTo && linkTo(`${prefix}/sales-by-product-category-report`);
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.REPORT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.REPORT;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Product Category Report - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Product Category Report - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: "150%" } : {},
                    ]}
                  >
                    Product & Category
                  </Text>
                </TouchableOpacity>
                :
                <></>
              }

              {/* hide first 2025-05-23 */}
              {!screensToBlock.includes('Variant_Report') ?
                <TouchableOpacity
                  onPress={() => {
                    CommonStore.update((s) => {
                      s.currPage = "Variant Report - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Variant Report - KooDoo BackOffice",
                      ];
                      linkTo && linkTo(`${prefix}/sales-by-variant-report`);
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.REPORT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.REPORT;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Variant Report - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Variant Report - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: "150%" } : {},
                    ]}
                  >
                    Variants
                  </Text>
                </TouchableOpacity>
                :
                <></>
              }

              {/* hide first 2025-05-23 */}
              {!screensToBlock.includes('Add_On_Report') ?
                <TouchableOpacity
                  onPress={() => {
                    CommonStore.update((s) => {
                      s.currPage = "Add Ons Report - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Add Ons Report - KooDoo BackOffice",
                      ];
                      linkTo && linkTo(`${prefix}/sales-by-addons-report`);
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.REPORT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.REPORT;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Add Ons Report - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Add Ons Report - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: "150%" } : {},
                    ]}
                  >
                    Add-Ons
                  </Text>
                </TouchableOpacity>
                :
                <></>
              }

              {/* <TouchableOpacity
                            onPress={() => { props.navigation.navigate('ReportSalesSKU'), setSelectedTab(8), expandAction(8) }}
                            style={[styles.subBar,]}>
                            <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>SKU</Text>
                        </TouchableOpacity> */}

              {/* hide first 2025-05-23 */}
              {!screensToBlock.includes('Channel_Report') ?
                <TouchableOpacity
                  onPress={() => {
                    CommonStore.update((s) => {
                      s.currPage = "Transaction Report - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Transaction Report - KooDoo BackOffice",
                      ];
                      linkTo && linkTo(`${prefix}/sales-by-transaction-report`);
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.REPORT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.REPORT;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Transaction Report - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Transaction Report - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: "150%" } : {},
                    ]}
                  >
                    Channel
                  </Text>
                </TouchableOpacity>
                :
                <></>
              }

              {/* hide first 2025-05-23 */}
              {!screensToBlock.includes('Payment_Report') ?
                <TouchableOpacity
                  onPress={() => {
                    CommonStore.update((s) => {
                      s.currPage = "Payment Method Report - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Payment Method Report - KooDoo BackOffice",
                      ];
                      linkTo && linkTo(`${prefix}/sales-by-payment-method-report`);
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.REPORT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.REPORT;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage ===
                        "Payment Method Report - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Payment Method Report - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: "150%" } : {},
                    ]}
                  >
                    Payment
                  </Text>
                </TouchableOpacity>
                :
                <></>
              }

              {/* <TouchableOpacity
                            onPress={() => {
                                props.navigation.navigate('ReportSalesByRedemption'), setSelectedTab(8), expandAction(8), CommonStore.update(s => {
                                    s.currPage = 'ReportSalesByRedemption';
                                });
                            }}
                            style={[styles.subBar, {
                                ...(currPage === 'ReportSalesByRedemption') && highlightStyle,
                            }]}>
                            <Text style={[styles.expandedItems, { color: currPage == "ReportSalesByRedemption" ? Colors.primaryColor : Colors.descriptionColor }]}>Redemption</Text>
                        </TouchableOpacity> */}

              {!screensToBlock.includes('Shift_Report') ?
                <TouchableOpacity
                  onPress={() => {
                    CommonStore.update((s) => {
                      s.currPage = "Shift Report - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Shift Report - KooDoo BackOffice",
                      ];
                      linkTo && linkTo(`${prefix}/sale-by-shift-report`);
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.REPORT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.REPORT;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Shift Report - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Shift Report - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: "150%" } : {},
                    ]}
                  >
                    Shift
                  </Text>
                </TouchableOpacity>
                :
                <></>
              }

              {/* hide first 2025-05-23 */}
              {!screensToBlock.includes('PayIn_Out_Report') ?
                <TouchableOpacity
                  onPress={() => {
                    // setSelectedTab(8), expandAction(8),
                    CommonStore.update((s) => {
                      s.currPage = "Pay In & Out Shift Report - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Pay In & Out Shift Report - KooDoo BackOffice",
                      ];
                      linkTo &&
                        linkTo(`${prefix}/sales-by-pay-in-n-out-shift-report`);
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.REPORT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.REPORT;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Pay In & Out Shift Report - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Pay In & Out Shift Report - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}
                  >
                    Pay In/Out
                  </Text>
                </TouchableOpacity>
                :
                <></>
              }

              {!screensToBlock.includes('Refund_Report') ?
                <TouchableOpacity
                  onPress={() => {
                    CommonStore.update((s) => {
                      s.currPage = "Refund Report - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Refund Report - KooDoo BackOffice",
                      ];
                      linkTo && linkTo(`${prefix}/sales-by-refund-report`);
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.REPORT) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.REPORT;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Refund Report - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Refund Report - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: "150%" } : {},
                    ]}
                  >
                    Refund
                  </Text>
                </TouchableOpacity>
                :
                <></>
              }

              <TouchableOpacity
                onPress={() => {
                  CommonStore.update((s) => {
                    s.currPage = "Payout Report - KooDoo BackOffice";
                    s.currPageStack = [
                      ...currPageStack,
                      "Payout Report - KooDoo BackOffice",
                    ];
                    linkTo && linkTo(`${prefix}/payout-report`);
                  });

                  if (expandTab !== EXPAND_TAB_TYPE.REPORT) {
                    CommonStore.update((s) => {
                      s.expandTab = EXPAND_TAB_TYPE.REPORT;
                    });
                  }
                }}
                style={[
                  styles.subBar,
                  {
                    ...(currPage === "Payout Report - KooDoo BackOffice" &&
                      highlightStyle),
                  },
                ]}
              >
                <Text
                  style={[
                    styles.expandedItems,
                    {
                      color:
                        currPage == "Payout Report - KooDoo BackOffice"
                          ? Colors.primaryColor
                          : Colors.descriptionColor,
                    },
                    switchMerchant ? { fontSize: 9, height: "150%" } : {},
                  ]}
                >
                  Payout
                </Text>
              </TouchableOpacity>

              {/* <TouchableOpacity
              onPress={() => {
                CommonStore.update((s) => {
                  s.currPage = "ReportSalesRepeat";
                  s.currPageStack = [...currPageStack, "ReportSalesRepeat"];
                  linkTo && linkTo(`${prefix}/report-sales-repeat`);
                });

                if (expandTab !== EXPAND_TAB_TYPE.REPORT) {
                  CommonStore.update((s) => {
                    s.expandTab = EXPAND_TAB_TYPE.REPORT;
                  });
                }
              }}
              style={[
                styles.subBar,
                {
                  ...(currPage === "ReportSalesRepeat" && highlightStyle),
                },
              ]}
            >
              <Text
                style={[
                  styles.expandedItems,
                  {
                    color:
                      currPage == "ReportSalesRepeat"
                        ? Colors.primaryColor
                        : Colors.descriptionColor,
                  },
                  switchMerchant ? { fontSize: 9, height: "150%" } : {},
                ]}
              >
                Repeat Sales
              </Text>
            </TouchableOpacity> */}
              {/* <TouchableOpacity
                            onPress={() => { props.navigation.navigate('AllTransaction', {selectedTab: 8}), setSelectedTab(8), expandAction(8) }}
                            style={[styles.subBar, ]}>
                            <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>All Transactions</Text>
                        </TouchableOpacity> */}
              {/* <TouchableOpacity
                            onPress={() => { props.navigation.navigate(""), setSelectedTab(8), expandAction(8) }}
                            style={styles.subBar}>
                            <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>Promotions report</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            onPress={() => { props.navigation.navigate(""), setSelectedTab(8), expandAction(8) }}
                            style={styles.subBar}>
                            <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>Stock Value</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            onPress={() => { props.navigation.navigate(""), setSelectedTab(8), expandAction(8) }}
                            style={styles.subBar}>
                            <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>Customer payment owing {'\n'}report</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            onPress={() => { props.navigation.navigate(""), setSelectedTab(8), expandAction(8) }}
                            style={styles.subBar}>
                            <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>Commission /overriding</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            onPress={() => { props.navigation.navigate(""), setSelectedTab(8), expandAction(8) }}
                            style={styles.subBar}>
                            <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>Loyalty Points</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            onPress={() => { props.navigation.navigate(""), setSelectedTab(8), expandAction(8) }}
                            style={styles.subBar}>
                            <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>Package/credit purchase & {'\n'}redemption</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            onPress={() => { props.navigation.navigate(""), setSelectedTab(8), expandAction(8) }}
                            style={styles.subBar}>
                            <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>History</Text>
                        </TouchableOpacity>
                        <TouchableOpacity
                            onPress={() => { setState({ expandSales: true }) }}
                            style={styles.subBar}>
                            <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>Sales</Text>
                        </TouchableOpacity> */}
              {/* {expandSales == true ? (
                            <View>
                                <TouchableOpacity
                                    onPress={() => { props.navigation.navigate('ReportSalesOvertime'), setSelectedTab(8), expandAction(8) }}
                                    style={[styles.subBar, { left: Dimensions.get('screen').width * 0.045 }]}>
                                    <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>Overtime</Text>
                                </TouchableOpacity>
                                <TouchableOpacity
                                    onPress={() => { props.navigation.navigate('ReportSalesProduct'), setSelectedTab(8), expandAction(8) }}
                                    style={[styles.subBar, { left: Dimensions.get('screen').width * 0.045 }]}>
                                    <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>Product</Text>
                                </TouchableOpacity>
                                <TouchableOpacity
                                    onPress={() => { props.navigation.navigate('Inventory'), setSelectedTab(8), expandAction(8) }}
                                    style={[styles.subBar, { left: Dimensions.get('screen').width * 0.045 }]}>
                                    <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>Inventory</Text>
                                </TouchableOpacity>
                                <TouchableOpacity
                                    onPress={() => { props.navigation.navigate('ReportSalesCategory'), setSelectedTab(8), expandAction(8) }}
                                    style={[styles.subBar, { left: Dimensions.get('screen').width * 0.045 }]}>
                                    <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>Category / Tags</Text>
                                </TouchableOpacity>
                                <TouchableOpacity
                                    onPress={() => { props.navigation.navigate('ReportSalesVariant'), setSelectedTab(8), expandAction(8) }}
                                    style={[styles.subBar, { left: Dimensions.get('screen').width * 0.045 }]}>
                                    <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>Variants</Text>
                                </TouchableOpacity>
                                <TouchableOpacity
                                    onPress={() => { props.navigation.navigate('ReportSalesSKU'), setSelectedTab(8), expandAction(8) }}
                                    style={[styles.subBar, { left: Dimensions.get('screen').width * 0.045 }]}>
                                    <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>SKU</Text>
                                </TouchableOpacity>
                                <TouchableOpacity
                                    onPress={() => { props.navigation.navigate('ReportSalesTransaction'), setSelectedTab(8), expandAction(8) }}
                                    style={[styles.subBar, { left: Dimensions.get('screen').width * 0.045 }]}>
                                    <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>Transaction</Text>
                                </TouchableOpacity>
                                <TouchableOpacity
                                    onPress={() => { props.navigation.navigate('ReportSalesPaymentMethod'), setSelectedTab(8), expandAction(8) }}
                                    style={[styles.subBar, { left: Dimensions.get('screen').width * 0.045 }]}>
                                    <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>Payment Method</Text>
                                </TouchableOpacity>
                                <TouchableOpacity
                                    onPress={() => { props.navigation.navigate('ReportSaleByShift'), setSelectedTab(8), expandAction(8) }}
                                    style={[styles.subBar, { left: Dimensions.get('screen').width * 0.045 }]}>
                                    <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>Shift</Text>
                                </TouchableOpacity>
                                <TouchableOpacity
                                    onPress={() => { props.navigation.navigate('AllTransaction'), setSelectedTab(8), expandAction(8) }}
                                    style={[styles.subBar, { left: Dimensions.get('screen').width * 0.045 }]}>
                                    <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>All Transactions</Text>
                                </TouchableOpacity>
                            </View>

                        ) : null} */}

              {/* hide first 2025-05-23 */}
              {/* {
                !screensToBlock.includes('All_Transcation')
                  ?
                  <TouchableOpacity
                    onPress={() => {
                      // props.navigation.navigate('AllTransaction');
                      // setSelectedTab(7), expandAction(7),
                      CommonStore.update((s) => {
                        s.currPage = "All Transaction - KooDoo BackOffice";
                        s.currPageStack = [
                          ...currPageStack,
                          "All Transaction - KooDoo BackOffice",
                        ];
                      });

                      linkTo && linkTo(`${prefix}/all-transaction`);

                      if (expandTab !== EXPAND_TAB_TYPE.REPORT) {
                        CommonStore.update((s) => {
                          s.expandTab = EXPAND_TAB_TYPE.REPORT;
                        });
                      }
                    }}
                    style={[
                      styles.subBar,
                      {
                        ...(currPage === "All Transaction - KooDoo BackOffice" &&
                          highlightStyle),
                      },
                    ]}
                  >
                    <Text
                      style={[
                        styles.expandedItems,
                        {
                          color:
                            currPage == "All Transaction - KooDoo BackOffice"
                              ? Colors.primaryColor
                              : Colors.descriptionColor,
                        },
                        switchMerchant ? { fontSize: 9, height: "150%" } : {},
                      ]}
                    >{`All\nTransactions`}</Text>
                  </TouchableOpacity>
                  :
                  <></>
              } */}
            </View>
          ) : null}
        </View>
      </View>

      <View style={{
        ...sideBarItemsContainerStyleScale,
        ...(isMobile() && { width: windowWidth * 0.23 }),
      }}>
        <TouchableHighlight
          underlayColor={Colors.lightPrimary}
          onPress={() => {
            //setState({ expandRedemption: !expandRedemption, selectedTab: 4 });
            // setExpandRedemption(!expandRedemption);
            // setSelectedTab(4);
            // expandAction(4)

            if (expandTab !== EXPAND_TAB_TYPE.DOCKET) {
              CommonStore.update((s) => {
                s.expandTab = EXPAND_TAB_TYPE.DOCKET;
              });
            } else {
              CommonStore.update((s) => {
                s.expandTab = EXPAND_TAB_TYPE.NONE;
              });
            }
          }}
          style={sideBarItemsStyleScale}
        >
          {expandTab == EXPAND_TAB_TYPE.DOCKET ? (
            <View>
              <View
                style={[
                  styles.sidebarView,
                  {
                    backgroundColor: Colors.lightPrimary,
                    height: isTablet
                      ? windowHeight * 0.08
                      : windowHeight * 0.16,
                    width: isTablet
                      ? windowWidth * Styles.sideBarWidth
                      : "100%",
                    ...isMobile() && {
                      width: windowWidth * 0.23,
                      marginLeft: 'auto',
                      marginRight: 'auto',
                    },
                  },
                ]}
              >
                <View style={sidebarIconStyleScale}>
                  <RedemptionG
                    width={
                      switchMerchant
                        ? windowWidth * 0.02
                        : windowWidth * iconSize
                    }
                    height={
                      switchMerchant
                        ? windowWidth * 0.02
                        : windowWidth * iconSize
                    }
                    {...(isMobile() && {
                      width: 18,
                      height: 18
                    })}
                  />
                </View>
                <View style={styles.sidebarText}>
                  <Text
                    style={[
                      sidebarTextStyleScale,
                      { color: Colors.primaryColor },
                    ]}
                  >
                    Docket
                  </Text>
                </View>
              </View>
            </View>
          ) : (
            <View>
              <View
                style={[
                  styles.sidebarView,
                  {
                    backgroundColor: Colors.whiteColor,
                    height: isTablet
                      ? windowHeight * 0.08
                      : windowHeight * 0.16,
                    width: isTablet
                      ? windowWidth * Styles.sideBarWidth
                      : "100%",
                    ...(isMobile() && {
                      width: windowWidth * 0.23,
                    }),
                  },
                ]}
              >
                <View style={[sidebarIconStyleScale, {
                  ...(isMobile() && {
                    width: 18,
                    height: 18
                  }),
                },
                ]}>
                  <Redemption
                    width={
                      switchMerchant
                        ? windowWidth * 0.02
                        : windowWidth * iconSize
                    }
                    height={
                      switchMerchant
                        ? windowWidth * 0.02
                        : windowWidth * iconSize
                    }
                    {...(isMobile() && {
                      width: 18,
                      height: 18
                    })}
                  />
                </View>
                <View style={styles.sidebarText}>
                  <Text style={sidebarTextStyleScale}>Docket</Text>
                </View>
              </View>
            </View>
          )}
        </TouchableHighlight>
        {expandTab === EXPAND_TAB_TYPE.DOCKET ? (
          <View
            style={{
              paddingVertical: 16,
              paddingTop: 5,
              backgroundColor:
                expandTab == EXPAND_TAB_TYPE.DOCKET
                  ? Colors.lightPrimary
                  : null,
              alignItems: "center",
            }}
          >
            <TouchableOpacity
              onPress={() => {
                // props.navigation.navigate('Redemption');
                // setSelectedTab(4), expandAction(4),
                CommonStore.update((s) => {
                  s.currPage = "Active Docket - KooDoo BackOffice";
                  s.currPageStack = [
                    ...currPageStack,
                    "Active Docket - KooDoo BackOffice",
                  ];
                });

                linkTo && linkTo(`${prefix}/active-docket`);

                if (expandTab !== EXPAND_TAB_TYPE.DOCKET) {
                  CommonStore.update((s) => {
                    s.expandTab = EXPAND_TAB_TYPE.DOCKET;
                  });
                }
              }}
              style={[
                styles.subBar,
                {
                  ...(currPage === "Active Docket - KooDoo BackOffice" &&
                    highlightStyle),
                },
              ]}
            >
              <Text
                style={[
                  styles.expandedItems,
                  {
                    color:
                      currPage == "Active Docket - KooDoo BackOffice"
                        ? Colors.primaryColor
                        : Colors.descriptionColor,
                  },
                  switchMerchant ? { fontSize: 9, height: "150%" } : {},
                ]}
              >{`Active\nDocket`}</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                // props.navigation.navigate('RedemptionExpired');
                // setSelectedTab(4), expandAction(4),
                CommonStore.update((s) => {
                  s.currPage = "Expired Docket - KooDoo BackOffice";
                  s.currPageStack = [
                    ...currPageStack,
                    "Expired Docket - KooDoo BackOffice",
                  ];
                });

                linkTo && linkTo(`${prefix}/expired-docket`);

                if (expandTab !== EXPAND_TAB_TYPE.DOCKET) {
                  CommonStore.update((s) => {
                    s.expandTab = EXPAND_TAB_TYPE.DOCKET;
                  });
                }
              }}
              style={[
                styles.subBar,
                {
                  ...(currPage === "Expired Docket - KooDoo BackOffice" &&
                    highlightStyle),
                },
              ]}
            >
              <Text
                style={[
                  styles.expandedItems,
                  {
                    color:
                      currPage == "Expired Docket - KooDoo BackOffice"
                        ? Colors.primaryColor
                        : Colors.descriptionColor,
                  },
                  switchMerchant ? { fontSize: 9, height: "150%" } : {},
                ]}
              >{`Expired\nDocket`}</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                // setSelectedTab(4),
                // props.navigation.navigate('RedemptionRedeemed'),
                // setSelectedTab(4), expandAction(4),
                CommonStore.update((s) => {
                  s.currPage = "Redeemed Docket - KooDoo BackOffice";
                  s.currPageStack = [
                    ...currPageStack,
                    "Redeemed Docket - KooDoo BackOffice",
                  ];
                });

                linkTo && linkTo(`${prefix}/redeemed-docket`);

                if (expandTab !== EXPAND_TAB_TYPE.DOCKET) {
                  CommonStore.update((s) => {
                    s.expandTab = EXPAND_TAB_TYPE.DOCKET;
                  });
                }
              }}
              style={[
                styles.subBar,
                {
                  ...(currPage === "Redeemed Docket - KooDoo BackOffice" &&
                    highlightStyle),
                },
              ]}
            >
              <Text
                style={[
                  styles.expandedItems,
                  {
                    color:
                      currPage == "Redeemed Docket - KooDoo BackOffice"
                        ? Colors.primaryColor
                        : Colors.descriptionColor,
                  },
                  switchMerchant ? { fontSize: 9, height: "150%" } : {},
                ]}
              >{`Redeemed\nDocket`}</Text>
            </TouchableOpacity>
            <TouchableOpacity
              onPress={() => {
                // props.navigation.navigate('SettingRedemption');
                // setSelectedTab(4), expandAction(4),
                CommonStore.update((s) => {
                  s.currPage = "Docket List - KooDoo BackOffice";
                  s.currPageStack = [
                    ...currPageStack,
                    "Docket List - KooDoo BackOffice",
                  ];
                });

                linkTo && linkTo(`${prefix}/docket-list`);

                if (expandTab !== EXPAND_TAB_TYPE.DOCKET) {
                  CommonStore.update((s) => {
                    s.expandTab = EXPAND_TAB_TYPE.DOCKET;
                  });
                }
              }}
              style={[
                styles.subBar,
                {
                  ...(currPage === "Docket List - KooDoo BackOffice" &&
                    highlightStyle),
                },
              ]}
            >
              <Text
                style={[
                  styles.expandedItems,
                  {
                    color:
                      currPage == "Docket List - KooDoo BackOffice"
                        ? Colors.primaryColor
                        : Colors.descriptionColor,
                  },
                  switchMerchant ? { fontSize: 9, height: "150%" } : {},
                ]}
              >{`Manage\nDocket`}</Text>
            </TouchableOpacity>
          </View>
        ) : null}
      </View>

      {(
        (currOutlet && currOutlet.privileges &&
          currOutlet.privileges.includes(PRIVILEGES_NAME.VOUCHER))
        && privileges.includes(PRIVILEGES_NAME.VOUCHER))
        ?
        <View style={{
          ...sideBarItemsContainerStyleScale,
          ...(isMobile() && { width: windowWidth * 0.23 }),
        }}>
          <TouchableHighlight
            underlayColor={Colors.lightPrimary}
            onPress={() => {
              // setState({ expandPromotions: !expandPromotions, selectedTab: 5 }),
              // setExpandVoucher(!expandVoucher);
              // setSelectedTab(5);
              // expandAction(5)

              if (expandTab !== EXPAND_TAB_TYPE.VOUCHER) {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.VOUCHER;
                });
              } else {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.NONE;
                });
              }
            }}
            style={sideBarItemsStyleScale}
          >
            {expandTab == EXPAND_TAB_TYPE.VOUCHER ? (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: Colors.lightPrimary,
                      height: isTablet
                        ? windowHeight * 0.08
                        : windowHeight * 0.16,
                      width: isTablet
                        ? windowWidth * Styles.sideBarWidth
                        : "100%",
                      ...isMobile() && {
                        width: windowWidth * 0.23,
                        marginLeft: 'auto',
                        marginRight: 'auto',
                      },
                    },
                  ]}
                >
                  <View style={sidebarIconStyleScale}>
                    <TicketConfirmationOutline
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      {...(isMobile() && {
                        width: 18,
                        height: 18
                      })}
                      color={"#4E9F7D"}
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text
                      style={[
                        sidebarTextStyleScale,
                        { color: Colors.primaryColor },
                      ]}
                    >
                      Voucher
                    </Text>
                  </View>
                </View>
              </View>
            ) : (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: Colors.whiteColor,
                      height: isTablet
                        ? windowHeight * 0.08
                        : windowHeight * 0.16,
                      width: isTablet
                        ? windowWidth * Styles.sideBarWidth
                        : "100%",
                      ...(isMobile() && {
                        width: windowWidth * 0.23,
                      }),
                    },
                  ]}
                >
                  <View style={[sidebarIconStyleScale, {
                    ...(isMobile() && {
                      width: 18,
                      height: 18
                    }),
                  },
                  ]}>
                    <TicketConfirmationOutline
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      {...(isMobile() && {
                        width: 18,
                        height: 18
                      })}
                      color={"#ACACAC"}
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text style={sidebarTextStyleScale}>Voucher</Text>
                  </View>
                </View>
              </View>
            )}
          </TouchableHighlight>
          {expandTab == EXPAND_TAB_TYPE.VOUCHER ? (
            <View
              style={{
                paddingVertical: 16,
                paddingTop: 5,
                backgroundColor:
                  expandTab == EXPAND_TAB_TYPE.VOUCHER
                    ? Colors.lightPrimary
                    : null,
                alignItems: "center",
              }}
            >
              {/* <TouchableOpacity
              onPress={() => {
                // setSelectedTab(5), expandAction(5),
                props.navigation.navigate('VoucherScreen');
                CommonStore.update((s) => {
                  s.currPage = "VoucherScreen";
                  s.currPageStack = [...currPageStack, "VoucherScreen"];
                });

                if (expandTab !== EXPAND_TAB_TYPE.VOUCHER) {
                  CommonStore.update((s) => {
                    s.expandTab = EXPAND_TAB_TYPE.VOUCHER;
                  });
                }
              }}
              style={[
                styles.subBar,
                {
                  ...(currPage === "VoucherScreen" && highlightStyle),
                },
              ]}
            >
              <Text
                style={[
                  styles.expandedItems,
                  {
                    color:
                      currPage == "VoucherScreen"
                        ? Colors.primaryColor
                        : Colors.descriptionColor,
                  },
                  switchMerchant ? { fontSize: 9, height: "150%" } : {},
                ]}
              >
                E-vouchers
              </Text>
            </TouchableOpacity> */}
              {!screensToBlock.includes('Voucher_List') ?
                <TouchableOpacity
                  onPress={() => {
                    props.navigation.navigate("Voucher List - KooDoo BackOffice"); //, { selectedTab: 5 }),
                    // setSelectedTab(11), expandAction(11),
                    CommonStore.update((s) => {
                      s.currPage = "Voucher List - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Voucher List - KooDoo BackOffice",
                      ];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.VOUCHER) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.VOUCHER;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Voucher List - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Voucher List - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: "150%" } : {},
                    ]}
                  >
                    {"Voucher\nList"}
                  </Text>
                </TouchableOpacity>
                :
                <></>
              }

              {!screensToBlock.includes('Add_Voucher') ?
                <TouchableOpacity
                  onPress={() => {
                    props.navigation.navigate("Voucher - KooDoo BackOffice"); //{ selectedTab: 5 }),
                    // setSelectedTab(5), expandAction(5),
                    CommonStore.update((s) => {
                      s.currPage = "Voucher - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Voucher - KooDoo BackOffice",
                      ];

                      s.selectedTaggableVoucherEdit = null;
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Voucher - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Voucher - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                    ]}
                  >
                    Add Voucher
                  </Text>
                </TouchableOpacity>
                :
                <></>
              }

              {/* <TouchableOpacity
              onPress={() => {
                // setSelectedTab(5), expandAction(5),
                props.navigation.navigate('VoucherReport');
                CommonStore.update((s) => {
                  s.currPage = "VoucherReport";
                  s.currPageStack = [...currPageStack, "VoucherReport"];
                });

                if (expandTab !== EXPAND_TAB_TYPE.VOUCHER) {
                  CommonStore.update((s) => {
                    s.expandTab = EXPAND_TAB_TYPE.VOUCHER;
                  });
                }
              }}
              style={[
                styles.subBar,
                {
                  ...(currPage === "VoucherReport" && highlightStyle),
                },
              ]}
            >
              <Text
                style={[
                  styles.expandedItems,
                  {
                    color:
                      currPage == "VoucherReport"
                        ? Colors.primaryColor
                        : Colors.descriptionColor,
                  },
                  switchMerchant ? { fontSize: 9, height: "150%" } : {},
                ]}
              >
                {"Voucher\nReport"}
              </Text>
            </TouchableOpacity> */}
              {!screensToBlock.includes('Voucher_Report') ?
                <TouchableOpacity
                  onPress={() => {
                    props.navigation.navigate("Voucher Report - KooDoo BackOffice"); //, { selectedTab: 5 }), setSelectedTab(5), expandAction(5),
                    CommonStore.update((s) => {
                      s.currPage = "Voucher Report - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Voucher Report - KooDoo BackOffice",
                      ];

                      s.selectedTaggableVoucherEdit = null;
                    });
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Voucher Report - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Voucher Report - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                    ]}
                  >
                    {"Voucher\nReport"}
                  </Text>
                </TouchableOpacity>
                :
                <></>
              }
            </View>
          ) : null}
        </View>
        :
        <></>
      }

      {((currOutlet && currOutlet.privileges &&
        currOutlet.privileges.includes(PRIVILEGES_NAME.CRM))
        && privileges.includes(PRIVILEGES_NAME.CRM))
        ?
        <View style={{
          ...sideBarItemsContainerStyleScale,
          ...(isMobile() && { width: windowWidth * 0.23 }),
        }}>
          <TouchableHighlight
            underlayColor={Colors.lightPrimary}
            onPress={() => {
              // setState({ expandPromotions: !expandPromotions, selectedTab: 6 }),
              // setExpandCRM(!expandCRM);
              // setSelectedTab(6);
              // expandAction(6)

              if (expandTab !== EXPAND_TAB_TYPE.CRM) {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.CRM;
                });
              } else {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.NONE;
                });
              }
            }}
            style={sideBarItemsStyleScale}
          >
            {expandTab == EXPAND_TAB_TYPE.CRM ? (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: Colors.lightPrimary,
                      height: isTablet
                        ? windowHeight * 0.08
                        : windowHeight * 0.16,
                      width: isTablet
                        ? windowWidth * Styles.sideBarWidth
                        : "100%",
                      ...isMobile() && {
                        width: windowWidth * 0.23,
                        marginLeft: 'auto',
                        marginRight: 'auto',
                      },
                    },
                  ]}
                >
                  <View style={sidebarIconStyleScale}>
                    <CRMG
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      {...(isMobile() && {
                        width: 18,
                        height: 18
                      })}
                      color={Colors.primaryColor}
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text
                      style={[
                        sidebarTextStyleScale,
                        { color: Colors.primaryColor },
                      ]}
                    >
                      CRM
                    </Text>
                  </View>
                </View>
              </View>
            ) : (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: Colors.whiteColor,
                      height: isTablet
                        ? windowHeight * 0.08
                        : windowHeight * 0.16,
                      width: isTablet
                        ? windowWidth * Styles.sideBarWidth
                        : "100%",
                      ...(isMobile() && {
                        width: windowWidth * 0.23,
                      }),
                    },
                  ]}
                >
                  <View style={[sidebarIconStyleScale, {
                    ...(isMobile() && {
                      width: 18,
                      height: 18
                    }),
                  },
                  ]}>
                    <CRM
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      {...(isMobile() && {
                        width: 18,
                        height: 18
                      })}
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text style={sidebarTextStyleScale}>CRM</Text>
                  </View>
                </View>
              </View>
            )}
          </TouchableHighlight>
          {expandTab === EXPAND_TAB_TYPE.CRM ? (
            <View
              style={{
                paddingVertical: 16,
                paddingTop: 5,
                backgroundColor:
                  expandTab === EXPAND_TAB_TYPE.CRM ? Colors.lightPrimary : null,
                alignItems: "center",
              }}
            >
              {!screensToBlock.includes('Customer') ?
                <TouchableOpacity
                  onPress={() => {
                    CommonStore.update((s) => {
                      s.currPage = "Customer List - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Customer List - KooDoo BackOffice",
                      ];
                      linkTo && linkTo(`${prefix}/customers-list`);
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.CRM) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.CRM;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Customer List - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Customer List - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: "150%" } : {},
                    ]}
                  >{`Customers`}</Text>
                </TouchableOpacity>
                :
                <></>
              }

              {!screensToBlock.includes('Segment') ?
                <TouchableOpacity
                  onPress={() => {
                    CommonStore.update((s) => {
                      s.currPage = "Segment List - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Segment List - KooDoo BackOffice",
                      ];
                      linkTo && linkTo(`${prefix}/segment-list`);
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.CRM) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.CRM;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Segment List - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Segment List - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: "150%" } : {},
                    ]}
                  >{`Segment`}</Text>
                </TouchableOpacity>
                :
                <></>
              }

              {/* <TouchableOpacity
                            onPress={() => {
                                props.navigation.navigate('NewCustomer', { selectedTab: 6 }), setSelectedTab(6), expandAction(6), CommonStore.update(s => {
                                    s.currPage = 'NewCustomer';
                                    s.currPageStack = [
                                        ...currPageStack,
                                        'NewCustomer',
                                    ];
                                });
                            }}
                            style={[styles.subBar, {
                                ...(currPage === 'NewCustomer') && highlightStyle,
                            }]}>
                            <Text style={[styles.expandedItems, { color: currPage == "NewCustomer" ? Colors.primaryColor : Colors.descriptionColor }]}>New Customers</Text>
                        </TouchableOpacity> */}
            </View>
          ) : null}
        </View>
        :
        <></>
      }

      {/*<View style={sideBarItemsContainerStyleScale}>
        <TouchableHighlight
          underlayColor={Colors.lightPrimary}
          onPress={() => {
            // setState({ expandTransactions: !expandTransactions, selectedTab: 7 }),
            // setExpandTransactions(!expandTransactions);
            // setSelectedTab(7);
            // expandAction(7)

            if (expandTab !== EXPAND_TAB_TYPE.TRANSACTIONS) {
              CommonStore.update((s) => {
                s.expandTab = EXPAND_TAB_TYPE.TRANSACTIONS;
              });
            } else {
              CommonStore.update((s) => {
                s.expandTab = EXPAND_TAB_TYPE.NONE;
              });
            }
          }}
          style={sideBarItemsStyleScale}
        >
          {expandTab == EXPAND_TAB_TYPE.TRANSACTIONS ? (
            <View>
              <View
                style={[
                  styles.sidebarView,
                  {
                    backgroundColor: Colors.lightPrimary,
                    height: isTablet
                      ? windowHeight * 0.08
                      : windowHeight * 0.16,
                    width: isTablet
                      ? windowWidth * Styles.sideBarWidth
                      : "100%",
                  },
                ]}
              >
                <View style={sidebarIconStyleScale}>
                  <TransactionsG
                    width={
                      switchMerchant
                        ? windowWidth * 0.02
                        : windowWidth * iconSize
                    }
                    height={
                      switchMerchant
                        ? windowWidth * 0.02
                        : windowWidth * iconSize
                    }
                  />
                </View>
                <View style={styles.sidebarText}>
                  <Text
                    style={[
                      sidebarTextStyleScale,
                      { color: Colors.primaryColor },
                    ]}
                  >
                    Transactions
                  </Text>
                </View>
              </View>
            </View>
          ) : (
            <View>
              <View
                style={[
                  styles.sidebarView,
                  {
                    backgroundColor: Colors.whiteColor,
                    height: isTablet
                      ? windowHeight * 0.08
                      : windowHeight * 0.16,
                    width: isTablet
                      ? windowWidth * Styles.sideBarWidth
                      : "100%",
                  },
                ]}
              >
                <View style={sidebarIconStyleScale}>
                  <Transactions
                    width={
                      switchMerchant
                        ? windowWidth * 0.02
                        : windowWidth * iconSize
                    }
                    height={
                      switchMerchant
                        ? windowWidth * 0.02
                        : windowWidth * iconSize
                    }
                  />
                </View>
                <View style={styles.sidebarText}>
                  <Text style={sidebarTextStyleScale}>Transactions</Text>
                </View>
              </View>
            </View>
          )}
        </TouchableHighlight>
        {expandTab === EXPAND_TAB_TYPE.TRANSACTIONS ? (
          <View
            style={{
              paddingVertical: 16,
              paddingTop: 5,
              backgroundColor:
                expandTab == EXPAND_TAB_TYPE.TRANSACTIONS
                  ? Colors.lightPrimary
                  : null,
              alignItems: "center",
            }}
          >
            
          </View>
        ) : null}
          </View>*/}

      {((currOutlet && currOutlet.privileges &&
        currOutlet.privileges.includes(PRIVILEGES_NAME.EMPLOYEES))
        && privileges.includes(PRIVILEGES_NAME.EMPLOYEES))
        ?
        <View style={{
          ...sideBarItemsContainerStyleScale,
          ...(isMobile() && { width: windowWidth * 0.23 }),
        }}>
          <TouchableHighlight
            underlayColor={Colors.lightPrimary}
            onPress={() => {
              // setState({ expandEmployees: !expandEmployees, selectedTab: 9 }),
              // setExpandEmployees(!expandEmployees);
              // setSelectedTab(9);
              // expandAction(9)

              if (expandTab !== EXPAND_TAB_TYPE.EMPLOYEES) {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.EMPLOYEES;
                });
              } else {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.NONE;
                });
              }
            }}
            style={sideBarItemsStyleScale}
          >
            {expandTab == EXPAND_TAB_TYPE.EMPLOYEES ? (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: Colors.lightPrimary,
                      height: isTablet
                        ? windowHeight * 0.08
                        : windowHeight * 0.16,
                      width: isTablet
                        ? windowWidth * Styles.sideBarWidth
                        : "100%",
                      ...isMobile() && {
                        width: windowWidth * 0.23,
                        marginLeft: 'auto',
                        marginRight: 'auto',
                      },
                    },
                  ]}
                >
                  <View style={sidebarIconStyleScale}>
                    <EmployeesG
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      {...(isMobile() && {
                        width: 18,
                        height: 18
                      })}
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text
                      style={[
                        sidebarTextStyleScale,
                        { color: Colors.primaryColor },
                      ]}
                    >
                      Employees
                    </Text>
                  </View>
                </View>
              </View>
            ) : (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: Colors.whiteColor,
                      height: isTablet
                        ? windowHeight * 0.08
                        : windowHeight * 0.16,
                      width: isTablet
                        ? windowWidth * Styles.sideBarWidth
                        : "100%",
                      ...(isMobile() && {
                        width: windowWidth * 0.23,
                      }),
                    },
                  ]}
                >
                  <View style={[sidebarIconStyleScale, {
                    ...(isMobile() && {
                      width: 18,
                      height: 18
                    }),
                  },
                  ]}>
                    <Employees
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      {...(isMobile() && {
                        width: 18,
                        height: 18
                      })}
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text style={sidebarTextStyleScale}>Employees</Text>
                  </View>
                </View>
              </View>
            )}
          </TouchableHighlight>
          {expandTab === EXPAND_TAB_TYPE.EMPLOYEES ? (
            <View
              style={{
                paddingVertical: 16,
                paddingTop: 5,
                backgroundColor:
                  expandTab === EXPAND_TAB_TYPE.EMPLOYEES
                    ? Colors.lightPrimary
                    : null,
                alignItems: "center",
              }}
            >
              {!screensToBlock.includes('Manage_Employee') ?
                <TouchableOpacity
                  onPress={() => {
                    // props.navigation.navigate('Employee');
                    // setSelectedTab(9), expandAction(9),
                    CommonStore.update((s) => {
                      s.currPage = "Employee List - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Employee List - KooDoo BackOffice",
                      ];
                      linkTo && linkTo(`${prefix}/employee-list`);
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.EMPLOYEES) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.EMPLOYEES;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Employee List - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Employee List - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: "150%" } : {},
                    ]}
                  >{`Manage\nEmployee`}</Text>
                </TouchableOpacity>
                :
                <></>
              }

              {!screensToBlock.includes('Active_Log') ?
                <TouchableOpacity
                  onPress={() => {
                    props.navigation.navigate("Activity-Log - KooDoo BackOffice");
                    // setSelectedTab(9), expandAction(9),
                    CommonStore.update((s) => {
                      s.currPage = "Activity-Log - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Activity-Log - KooDoo BackOffice",
                      ];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.EMPLOYEES) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.EMPLOYEES;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Activity-Log - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Activity-Log - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: "150%" } : {},
                    ]}
                  >{`Activity\nLog`}</Text>
                </TouchableOpacity>
                :
                <></>
              }

              {!screensToBlock.includes('Employee_Timesheet') ?
                <TouchableOpacity
                  onPress={() => {
                    props.navigation.navigate(
                      "Employee Timesheet - KooDoo BackOffice"
                    );
                    // setSelectedTab(9), expandAction(9),
                    CommonStore.update((s) => {
                      s.currPage = "Employee Timesheet - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Employee Timesheet - KooDoo BackOffice",
                      ];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.EMPLOYEES) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.EMPLOYEES;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Employee Timesheet - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Employee Timesheet - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9, height: "150%" } : {},
                    ]}
                  >{`Employee\nTimesheet`}</Text>
                </TouchableOpacity>
                :
                <></>
              }

              {/* <TouchableOpacity
                            onPress={() => { props.navigation.navigate("Employee"); setSelectedTab(9), expandAction(9) }}
                            style={styles.subBar}>
                            <Text style={[styles.expandedItems, { color: currPage == "" ? Colors.primaryColor : Colors.descriptionColor }]}>Employee Performance</Text>
                        </TouchableOpacity> */}
            </View>
          ) : null}
        </View>
        :
        <></>
      }

      {((currOutlet && currOutlet.privileges &&
        currOutlet.privileges.includes(PRIVILEGES_NAME.SETTINGS)) &&
        privileges.includes(PRIVILEGES_NAME.SETTINGS))
        ?
        <View style={{
          ...sideBarItemsContainerStyleScale,
          ...(isMobile() && { width: windowWidth * 0.23 }),
        }}>
          <TouchableHighlight
            underlayColor={Colors.lightPrimary}
            onPress={() => {
              // setState({ expandSettings: !expandSettings, selectedTab: 10 }),
              // setExpandSettings(!expandSettings);
              // setSelectedTab(10);
              // expandAction(10)

              if (expandTab !== EXPAND_TAB_TYPE.SETTINGS) {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.SETTINGS;
                });
              } else {
                CommonStore.update((s) => {
                  s.expandTab = EXPAND_TAB_TYPE.NONE;
                });
              }
            }}
            style={sideBarItemsStyleScale}
          >
            {expandTab === EXPAND_TAB_TYPE.SETTINGS ? (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: Colors.lightPrimary,
                      height: isTablet
                        ? windowHeight * 0.08
                        : windowHeight * 0.16,
                      width: isTablet
                        ? windowWidth * Styles.sideBarWidth
                        : "100%",
                      ...isMobile() && {
                        width: windowWidth * 0.23,
                        marginLeft: 'auto',
                        marginRight: 'auto',
                      },
                    },
                  ]}
                >
                  <View style={sidebarIconStyleScale}>
                    <SettingsG
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      {...(isMobile() && {
                        width: 18,
                        height: 18
                      })}
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text
                      style={[
                        sidebarTextStyleScale,
                        { color: Colors.primaryColor },
                      ]}
                    >
                      Settings
                    </Text>
                  </View>
                </View>
              </View>
            ) : (
              <View>
                <View
                  style={[
                    styles.sidebarView,
                    {
                      backgroundColor: Colors.whiteColor,
                      height: isTablet
                        ? windowHeight * 0.08
                        : windowHeight * 0.16,
                      width: isTablet
                        ? windowWidth * Styles.sideBarWidth
                        : "100%",
                      ...(isMobile() && {
                        width: windowWidth * 0.23,
                      }),
                    },
                  ]}
                >
                  <View style={[sidebarIconStyleScale, {
                    ...(isMobile() && {
                      width: 18,
                      height: 18
                    }),
                  },
                  ]}>
                    <Settings
                      width={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      height={
                        switchMerchant
                          ? windowWidth * 0.02
                          : windowWidth * iconSize
                      }
                      {...(isMobile() && {
                        width: 18,
                        height: 18
                      })}
                    />
                  </View>
                  <View style={styles.sidebarText}>
                    <Text style={sidebarTextStyleScale}>Settings</Text>
                  </View>
                </View>
              </View>
            )}
          </TouchableHighlight>
          {expandTab === EXPAND_TAB_TYPE.SETTINGS ? (
            <View
              style={{
                paddingVertical: 16,
                paddingTop: 5,
                backgroundColor:
                  expandTab === EXPAND_TAB_TYPE.SETTINGS
                    ? Colors.lightPrimary
                    : null,
                alignItems: "center",
              }}
            >
              {!screensToBlock.includes('General_Setting') ?
                <TouchableOpacity
                  onPress={() => {
                    // props.navigation.navigate('Setting');
                    // setSelectedTab(10), expandAction(10),
                    CommonStore.update((s) => {
                      s.currPage = "General Settings - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "General Settings - KooDoo BackOffice",
                      ];
                      linkTo && linkTo(`${prefix}/general-settings`);
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.SETTINGS) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.SETTINGS;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "General Settings - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "General Settings - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}
                  >
                    General
                  </Text>
                </TouchableOpacity>
                :
                <></>
              }

              {/* <TouchableOpacity
                onPress={() => {
                  CommonStore.update((s) => {
                    s.currPage = "Merchant Payment - KooDoo BackOffice";
                    s.currPageStack = [
                      ...currPageStack,
                      "Merchant Payment - KooDoo BackOffice",
                    ];
                    linkTo && linkTo(`${prefix}/mp-settings`);
                  });

                  if (expandTab !== EXPAND_TAB_TYPE.SETTINGS) {
                    CommonStore.update((s) => {
                      s.expandTab = EXPAND_TAB_TYPE.SETTINGS;
                    });
                  }
                }}
                style={[
                  styles.subBar,
                  {
                    ...(currPage === "Merchant Payment - KooDoo BackOffice" &&
                      highlightStyle),
                  },
                ]}
              >
                <Text
                  style={[
                    styles.expandedItems,
                    {
                      color:
                        currPage == "Merchant Payment - KooDoo BackOffice"
                          ? Colors.primaryColor
                          : Colors.descriptionColor,
                    },
                    switchMerchant ? { fontSize: 9 } : {},
                  ]}
                >
                  {"Merchant\nPayment"}
                </Text>
              </TouchableOpacity> */}

              <TouchableOpacity
                onPress={() => {
                  CommonStore.update((s) => {
                    s.currPage = "SMS Credit - KooDoo BackOffice";
                    s.currPageStack = [
                      ...currPageStack,
                      "SMS Credit - KooDoo BackOffice",
                    ];
                    linkTo && linkTo(`${prefix}/sms-credit-settings`);
                  });

                  if (expandTab !== EXPAND_TAB_TYPE.SETTINGS) {
                    CommonStore.update((s) => {
                      s.expandTab = EXPAND_TAB_TYPE.SETTINGS;
                    });
                  }
                }}
                style={[
                  styles.subBar,
                  {
                    ...(currPage === "SMS Credit - KooDoo BackOffice" &&
                      highlightStyle),
                  },
                ]}
              >
                <Text
                  style={[
                    styles.expandedItems,
                    {
                      color:
                        currPage == "SMS Credit - KooDoo BackOffice"
                          ? Colors.primaryColor
                          : Colors.descriptionColor,
                    },
                    switchMerchant ? { fontSize: 9 } : {},
                  ]}
                >
                  {"SMS\nCredit"}
                </Text>
              </TouchableOpacity>

              <TouchableOpacity
                onPress={() => {
                  CommonStore.update((s) => {
                    s.currPage = "Whatsapp Credit - KooDoo BackOffice";
                    s.currPageStack = [
                      ...currPageStack,
                      "Whatsapp Credit - KooDoo BackOffice",
                    ];
                    linkTo && linkTo(`${prefix}/whatsapp-credit-settings`);
                  });

                  if (expandTab !== EXPAND_TAB_TYPE.SETTINGS) {
                    CommonStore.update((s) => {
                      s.expandTab = EXPAND_TAB_TYPE.SETTINGS;
                    });
                  }
                }}
                style={[
                  styles.subBar,
                  {
                    ...(currPage === "Whatsapp Credit - KooDoo BackOffice" &&
                      highlightStyle),
                  },
                ]}
              >
                <Text
                  style={[
                    styles.expandedItems,
                    {
                      color:
                        currPage == "Whatsapp Credit - KooDoo BackOffice"
                          ? Colors.primaryColor
                          : Colors.descriptionColor,
                    },
                    switchMerchant ? { fontSize: 9 } : {},
                  ]}
                >
                  {"Whatsapp\nCredit"}
                </Text>
              </TouchableOpacity>


              {!screensToBlock.includes('Shift_Setting') ?
                <TouchableOpacity
                  onPress={() => {
                    // props.navigation.navigate('SettingShift');
                    // setSelectedTab(10), expandAction(10),
                    CommonStore.update((s) => {
                      s.currPage = "Shift Settings - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Shift Settings - KooDoo BackOffice",
                      ];
                      linkTo && linkTo(`${prefix}/shift-settings`);
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.SETTINGS) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.SETTINGS;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Shift Settings - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Shift Settings - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}
                  >
                    Shift
                  </Text>
                </TouchableOpacity>
                :
                <></>
              }

              {!screensToBlock.includes('Receipt_Setting') ?
                <TouchableOpacity
                  onPress={() => {
                    // props.navigation.navigate('SettingReceipt');
                    // setSelectedTab(10), expandAction(10),
                    CommonStore.update((s) => {
                      s.currPage = "Receipt Settings - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Receipt Settings - KooDoo BackOffice",
                      ];
                      linkTo && linkTo(`${prefix}/receipt-settings`);
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.SETTINGS) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.SETTINGS;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Receipt Settings - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Receipt Settings - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}
                  >
                    Receipt
                  </Text>
                </TouchableOpacity>
                :
                <></>
              }

              {!screensToBlock.includes('Order_Setting') ?
                <TouchableOpacity
                  onPress={() => {
                    props.navigation.navigate("Order Settings - KooDoo BackOffice");
                    //setSelectedTab(10), expandAction(10),
                    CommonStore.update((s) => {
                      s.currPage = "Order Settings - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Order Settings - KooDoo BackOffice",
                      ];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.SETTINGS) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.SETTINGS;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Order Settings - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Order Settings - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}
                  >
                    Order
                  </Text>
                </TouchableOpacity>
                :
                <></>
              }

              {!screensToBlock.includes('Printer_Setting') ?
                <TouchableOpacity
                  onPress={() => {
                    props.navigation.navigate(
                      "Printer Settings - KooDoo BackOffice"
                    );
                    CommonStore.update((s) => {
                      s.currPage = "Printer Settings - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Printer Settings - KooDoo BackOffice",
                      ];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.SETTINGS) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.SETTINGS;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Printer Settings - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Printer Settings - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}
                  >
                    Printer
                  </Text>
                </TouchableOpacity>
                :
                <></>
              }

              {!screensToBlock.includes('Payment_Setting') ?
                <TouchableOpacity
                  onPress={() => {
                    // props.navigation.navigate('SettingPayment');
                    linkTo && linkTo(`${prefix}/payment-settings`);
                    CommonStore.update((s) => {
                      s.currPage = "Payment Setting - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Payment Setting - KooDoo BackOffice",
                      ];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.SETTINGS) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.SETTINGS;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Payment Setting - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Payment Setting - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}
                  >
                    Payment
                  </Text>
                </TouchableOpacity>
                :
                <></>
              }
              {!screensToBlock.includes('EInvoice') ?
                <TouchableOpacity
                  onPress={() => {
                    // props.navigation.navigate('SettingPayment');
                    linkTo && linkTo(`${prefix}/e-invoice`);
                    CommonStore.update((s) => {
                      s.currPage = "E Invoice - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "E Invoice - KooDoo BackOffice",
                      ];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.SETTINGS) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.SETTINGS;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "E Invoice - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "E Invoice - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}
                  >
                    e-Invoice
                  </Text>
                </TouchableOpacity>
                :
                <></>
              }
              {!screensToBlock.includes('SettingsWhatsapp') ?
                <TouchableOpacity
                  onPress={() => {
                    // props.navigation.navigate('SettingPayment');
                    linkTo && linkTo(`${prefix}/whatsapp-settings`);
                    CommonStore.update((s) => {
                      s.currPage = "WhatsApp Settings - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "WhatsApp Settings - KooDoo BackOffice",
                      ];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.SETTINGS) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.SETTINGS;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "WhatsApp Settings - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "WhatsApp Settings - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}
                  >
                    WhatsApp
                  </Text>
                </TouchableOpacity>
                :
                <></>
              }

              {!screensToBlock.includes('SettingsGrab') ?
                <TouchableOpacity
                  onPress={() => {
                    // props.navigation.navigate('SettingPayment');
                    linkTo && linkTo(`${prefix}/grab-settings`);
                    CommonStore.update((s) => {
                      s.currPage = "Grab Settings - KooDoo BackOffice";
                      s.currPageStack = [
                        ...currPageStack,
                        "Grab Settings - KooDoo BackOffice",
                      ];
                    });

                    if (expandTab !== EXPAND_TAB_TYPE.SETTINGS) {
                      CommonStore.update((s) => {
                        s.expandTab = EXPAND_TAB_TYPE.SETTINGS;
                      });
                    }
                  }}
                  style={[
                    styles.subBar,
                    {
                      ...(currPage === "Grab Settings - KooDoo BackOffice" &&
                        highlightStyle),
                    },
                  ]}
                >
                  <Text
                    style={[
                      styles.expandedItems,
                      {
                        color:
                          currPage == "Grab Settings - KooDoo BackOffice"
                            ? Colors.primaryColor
                            : Colors.descriptionColor,
                      },
                      switchMerchant ? { fontSize: 9 } : {},
                    ]}
                  >
                    Grab
                  </Text>
                </TouchableOpacity>
                :
                <></>
              }
              {/* <TouchableOpacity
              onPress={() => {
                // props.navigation.navigate('SettingReservation');

                CommonStore.update((s) => {
                  s.currPage = "SettingsReservation";
                  s.currPageStack = [...currPageStack, "SettingsReservation"];
                  linkTo && linkTo(`${prefix}/setting-sreservation`);
                });

                if (expandTab !== EXPAND_TAB_TYPE.SETTINGS) {
                  CommonStore.update((s) => {
                    s.expandTab = EXPAND_TAB_TYPE.SETTINGS;
                  });
                }
              }}
              style={[
                styles.subBar,
                {
                  ...(currPage === "SettingsReservation" && highlightStyle),
                },
              ]}
            >
              <Text
                style={[
                  styles.expandedItems,
                  {
                    color:
                      currPage == "SettingsReservation"
                        ? Colors.primaryColor
                        : Colors.descriptionColor,
                  },
                  switchMerchant ? { fontSize: 9 } : {},
                ]}
              >
                Reservation
              </Text>
            </TouchableOpacity> */}

              {/* Will hide first - 23/9/2022 */}

              {/* {
                ((currOutlet && currOutlet.privileges &&
                  currOutlet.privileges.includes(PRIVILEGES_NAME.LOYALTY))
                  && privileges.includes(PRIVILEGES_NAME.LOYALTY) && !screensToBlock.includes('Loyalty_Setting'))
                  ?
                  <TouchableOpacity
                    onPress={() => {
                      linkTo && linkTo(`${prefix}/setting-loyalty`);
                      CommonStore.update((s) => {
                        s.currPage = "Setting Loyalty - KooDoo BackOffice";
                        s.currPageStack = [...currPageStack, "Setting Loyalty - KooDoo BackOffice"];
                      });

                      if (expandTab !== EXPAND_TAB_TYPE.SETTINGS) {
                        CommonStore.update((s) => {
                          s.expandTab = EXPAND_TAB_TYPE.SETTINGS;
                        });
                      }
                    }}
                    style={[
                      styles.subBar,
                      {
                        ...(currPage === "Setting Loyalty - KooDoo BackOffice" && highlightStyle),
                      },
                    ]}
                  >
                    <Text
                      style={[
                        styles.expandedItems,
                        {
                          color:
                            currPage == "Loyalty Setting - KooDoo BackOffice"
                              ? Colors.primaryColor
                              : Colors.descriptionColor,
                        },
                        switchMerchant ? { fontSize: 9 } : {},
                      ]}
                    >
                      Loyalty
                    </Text>
                  </TouchableOpacity>
                  :
                  <></>
              } */}
            </View>
          ) : null}
        </View>
        :
        <></>
      }

      <View style={{
        ...sideBarItemsContainerStyleScale,
        ...(isMobile() && { width: windowWidth * 0.23 }),
      }}>
        <TouchableHighlight
          underlayColor={Colors.lightPrimary}
          style={[sideBarItemsStyleScale, {}]}
          onPress={async () => {
            await AsyncStorage.clear();
            // User.setlogin(false);
            // User.getRefreshMainScreen();
            logOutButton();
          }}
        >
          <View
            style={[
              styles.sidebarView,
              {
                backgroundColor: Colors.whiteColor,
                height: isTablet ? windowHeight * 0.08 : windowHeight * 0.16,
                width: isTablet ? windowWidth * Styles.sideBarWidth : "100%",
                ...(isMobile() && {
                  width: windowWidth * 0.23,
                }),
              },
            ]}
          >
            <View style={[sidebarIconStyleScale, {
              ...(isMobile() && {
                width: 18,
                height: 18
              }),
            },
            ]}>
              <Logout
                width={
                  switchMerchant ? windowWidth * 0.02 : windowWidth * iconSize
                }
                height={
                  switchMerchant ? windowWidth * 0.02 : windowWidth * iconSize
                }
                {...(isMobile() && {
                  width: 18,
                  height: 18
                })}
                color={Colors.descriptionColor}
              />
            </View>
            <View style={styles.sidebarText}>
              <Text style={sidebarTextStyleScale}>Logout</Text>
            </View>
          </View>
        </TouchableHighlight>
      </View>
    </ScrollView>
  );
};

var styles = StyleSheet.create({
  icon: {
    width: 80,
    height: 80,
    justifyContent: "center",
    alignContent: "center",
    alignItems: "center",
    alignSelf: "center",
    marginTop: 30,
  },
  scrollView: {
    backgroundColor: Colors.whiteColor,
    // flex: 1,
    shadowColor: "#000",
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.22,
    shadowRadius: 3.22,
    elevation: 3,
    // width: Dimensions.get('screen').width * 0.163,
    // width: Dimensions.get('screen').width * 0.15,
    width: Dimensions.get("screen").width * Styles.sideBarWidth,
  },
  menuIcon: {
    width: 20,
    height: 20,
    alignSelf: "center",
    marginBottom: 10,
  },
  sidebarItems: {
    width: Dimensions.get("screen").width * Styles.sideBarWidth,
    // borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: "#6e6e6e",
  },
  //sidebarView include dashboard
  sidebarView: {
    alignItems: "center",
    height: isTablet
      ? Dimensions.get("screen").height * 0.08
      : Dimensions.get("screen").height * 0.16,
    // flexDirection: 'row',
    // paddingLeft: 16,
    // width: Dimensions.get('screen').width * 0.155,
    width: isTablet
      ? Dimensions.get("screen").width * Styles.sideBarWidth
      : "100%",

    justifyContent: "center",
    ...isMobile() && {
      //marginLeft: 25,
    },
    paddingTop: 7,
  },
  sidebarIcon: {
    flex: 1,
  },
  sidebarText: {
    // flex: 2,
    marginTop: 6,
  },
  sidebarArrow: {
    flex: 0.5,
    alignItems: "flex-start",
    marginTop: 2000,
    display: "none",
  },
  sidebarTextStyle: {
    fontFamily: "NunitoSans-SemiBold",
    fontSize: 15,
    color: Colors.descriptionColor,
  },
  expandedItems: {
    fontFamily: "NunitoSans-SemiBold",
    fontSize: 14,
    color: Colors.descriptionColor,
    marginLeft: Dimensions.get("screen").width * 0.01,

    // width: '60%',
    width: Dimensions.get("screen").width * 0.075,
    // width: '20%',
    //height: Dimensions.get('screen').height * 0.05,
    marginLeft: -Dimensions.get("screen").width * 0.001,
    textAlign: "center",
    // backgroundColor: 'red',
    // justifyContent: 'center',
    // textAlign: 'center',
    alignSelf: "center",
    // paddingVertical: 10,
  },
  subBar: {
    // left: Dimensions.get('screen').width * 0.01,
    // height: 35,
    justifyContent: "center",
    // flexWrap: 'wrap',
    // height: isTablet ? Dimensions.get('screen').height * 0.05 : Dimensions.get('screen').height * 0.1,
    ...(!isTablet && {
      height: Dimensions.get("screen").height * 0.1,
    }),
    alignItems: "center",
    paddingVertical: 10,
  },
});

export default SideBar;
