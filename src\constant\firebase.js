var Collections = {};

Collections.Accumulator = 'Accumulator';

Collections.User = 'User';
Collections.UserCart = 'UserCart';
Collections.UserAction = 'UserAction';
Collections.UserActionSlice = 'UserActionSlice';

Collections.Outlet = 'Outlet';
Collections.Merchant = 'Merchant';
Collections.UserOutlet = 'UserOutlet';
Collections.QRDynamic = 'QRDynamic';
Collections.UserLegacy = 'UserLegacy';
Collections.Notification = 'Notification';
Collections.Activity = 'Activity';
Collections.GiftCard = 'GiftCard';
Collections.UserSegment = 'UserSegment';
Collections.GiftCardSegment = 'GiftCardSegment';
Collections.AppliedGiftCard = 'AppliedGiftCard';
Collections.UserGiftCard = 'UserGiftCard';
Collections.UserReferral = 'UserReferral';
Collections.Category = 'Category';
Collections.Item = 'Item';
Collections.ItemAddOn = 'ItemAddOn';
Collections.ItemAddOnChoice = 'ItemAddOnChoice';
Collections.SimpleComps = 'SimpleComps';
Collections.Tax = 'Tax';
Collections.Table = 'Table';
Collections.Order = 'Order';
Collections.OutletRing = 'OutletRing';
Collections.ItemPromo = 'ItemPromo';
Collections.OrderItem = 'OrderItem';
Collections.BirthdayRewardClaimed = 'BirthdayRewardClaimed';
Collections.BirthdayReward = 'BirthdayReward';
Collections.UserReward = 'UserReward';
Collections.MerchantLoyalty = 'MerchantLoyalty';
Collections.Reward = 'Reward';
Collections.RewardDiscount = 'RewardDiscount';
Collections.RewardDiscountCategory = 'RewardDiscountCategory';
Collections.RewardDiscountBill = 'RewardDiscountBill';
Collections.RewardDiscountItem = 'RewardDiscountItem';
Collections.RewardCash = 'RewardCash';
Collections.RewardItem = 'RewardItem';
Collections.OutletReview = 'OutletReview';
Collections.OutletOpening = 'OutletOpening';

Collections.OutletItem = 'OutletItem';
Collections.OutletItemCategory = 'OutletItemCategory';
Collections.OutletItemAddOn = 'OutletItemAddOn';
Collections.OutletItemAddOnChoice = 'OutletItemAddOnChoice';

Collections.OutletItemStore = 'OutletItemStore';

Collections.OutletSupplyItem = 'OutletSupplyItem';
Collections.SupplyItem = 'SupplyItem';
Collections.Supplier = 'Supplier';
Collections.PurchaseOrder = 'PurchaseOrder';
Collections.StockTransfer = 'StockTransfer';
Collections.StockTake = 'StockTake';

Collections.SupplierProduct = 'SupplierProduct';
Collections.PurchaseOrderProduct = 'PurchaseOrderProduct';
Collections.StockTransferProduct = 'StockTransferProduct';
Collections.StockTakeProduct = 'StockTakeProduct';
Collections.StockReturnProduct = 'StockReturnProduct';

Collections.OutletTax = 'OutletTax';

Collections.UserAddress = 'UserAddress';
Collections.UserOrder = 'UserOrder';
Collections.UserOrderBeerDocket = 'UserOrderBeerDocket';
Collections.UserOrderLoyalty = 'UserOrderLoyalty';

Collections.UserOrderRefund = 'UserOrderRefund';

Collections.UserOrderDestBackup = 'UserOrderDestBackup';

Collections.UserOrderMetadata = 'UserOrderMetadata';

Collections.OutletTable = 'OutletTable';
Collections.OutletSection = 'OutletSection';
Collections.OutletTableCombination = 'OutletTableCombination';

Collections.Tag = 'Tag';
Collections.OutletTag = 'OutletTag';

Collections.UserReservation = 'UserReservation';
Collections.UserQueue = 'UserQueue';
Collections.UserRing = 'UserRing';

Collections.OutletQueueNumber = 'OutletQueueNumber';
Collections.OutletOrderNumber = 'OutletOrderNumber';

Collections.QueueConfig = 'QueueConfig';

///////////////////////////////////////////////////////

Collections.MerchantWallet = 'MerchantWallet';
Collections.MerchantTransaction = 'MerchantTransaction';

Collections.MerchantWalletCreditWhatsapp = 'MerchantWalletCreditWhatsapp';
Collections.MerchantWalletCreditWhatsappTransaction = 'MerchantWalletCreditWhatsappTransaction';

Collections.CourierWallet = 'CourierWallet';
Collections.CourierTransaction = 'CourierTransaction';

///////////////////////////////////////////////////////

Collections.Segment = 'Segment';
Collections.MerchantVoucher = 'MerchantVoucher';
Collections.MerchantVoucherReport = 'MerchantVoucherReport';
Collections.UserVoucherRedemption = 'UserVoucherRedemption';
Collections.UserVoucher = 'UserVoucher';

Collections.OutletShift = 'OutletShift';

Collections.UserFavoriteOutlet = 'UserFavoriteOutlet';

Collections.UserLoyalty = 'UserLoyalty';

Collections.BeerDocketCategory = 'BeerDocketCategory';
Collections.BeerDocket = 'BeerDocket';
Collections.UserBeerDocket = 'UserBeerDocket';

Collections.Promotion = 'Promotion';
Collections.PromotionRedemption = 'PromotionRedemption';

Collections.CRMUser = 'CRMUser';
Collections.CRMUserTag = 'CRMUserTag';
Collections.CRMMerchant = 'CRMMerchant';
Collections.CRMSegment = 'CRMSegment';

Collections.CustomerReview = 'CustomerReview';

Collections.PreorderPackage = 'PreorderPackage';

Collections.PointsRedeemPackage = 'PointsRedeemPackage';
Collections.PointsRedeemPackageRedemption = 'PointsRedeemPackageRedemption';

Collections.UserPointsTransaction = 'UserPointsTransaction';

Collections.OutletPrinter = 'OutletPrinter';

Collections.LoyaltyStamp = 'LoyaltyStamp';
Collections.UserLoyaltyStamp = 'UserLoyaltyStamp';

Collections.OutletPaymentMethod = 'OutletPaymentMethod';

Collections.LoyaltyCampaign = 'LoyaltyCampaign';
Collections.LoyaltyCampaignRedemption = 'LoyaltyCampaignRedemption';
Collections.LoyaltyCampaignCreditTransaction = 'LoyaltyCampaignCreditTransaction'; // can find by userId, crmUserId, phone, or email
Collections.UserLoyaltyCampaign = 'UserLoyaltyCampaign';
Collections.LoyaltyTier = 'LoyaltyTier';
Collections.LoyaltyStampType = 'LoyaltyStampType';

Collections.ReservationAvailability = 'ReservationAvailability';
Collections.UserReservationWaitList = 'UserReservationWaitList';
Collections.ReservationConfig = 'ReservationConfig';

///////////////////////////////////////////////////////

Collections.TaggableVoucher = 'TaggableVoucher';
Collections.UserTaggableVoucher = 'UserTaggableVoucher';

///////////////////////////////////////////////////////

Collections.EmployeeClock = 'EmployeeClock';

///////////////////////////////////////////////////////

// 2022-08-09 - Added support code login (staff)

Collections.SupportCode = 'SupportCode';

///////////////////////////////////////////////////////

// 2022-08-09 - Added topup credit type

Collections.TopupCreditType = 'TopupCreditType';

///////////////////////////////////////////////////////

Collections.MerchantPayment = 'MerchantPayment';

Collections.MerchantOrderSMSCreditTemp = 'MerchantOrderSMSCreditTemp';
Collections.MerchantOrderSMSCredit = 'MerchantOrderSMSCredit';

Collections.MerchantWalletCreditSMS = 'MerchantWalletCreditSMS';
Collections.MerchantWalletCreditSMSTransaction = 'MerchantWalletCreditSMSTransaction';

Collections.RazerPayoutTransaction = 'RazerPayoutTransaction';
Collections.RazerPayoutTransactionExtend = 'RazerPayoutTransactionExtend';

///////////////////////////////////////////////////////

// 2023-01-31 - Added upselling campaign collection

Collections.UpsellingCampaign = 'UpsellingCampaign';

///////////////////////////////////////////////////////

Collections.RecommendedLoyalty = 'RecommendedLoyalty';

///////////////////////////////////////////////////////

// 2024-07-09 - To keep track of the outlet supply item's movement

Collections.OutletSupplyItemTransaction = 'OutletSupplyItemTransaction';

///////////////////////////////////////////////////////

Collections.WOItem = 'WOItem';
Collections.WOList = 'WOList';

///////////////////////////////////////////////////////

Collections.OutletCatalog = 'OutletCatalog';

///////////////////////////////////////////////////////

export {
    Collections,
};
