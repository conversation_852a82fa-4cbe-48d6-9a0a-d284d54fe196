import React, { useState, useEffect, useCallback } from 'react';
import {
  Text,
  StyleSheet,
  Image,
  View,
  Alert,
  TouchableOpacity,
  Dimensions,
  useWindowDimensions,
  Linking,
} from 'react-native';
import Colors from '../constant/Colors';
import Ionicon from 'react-native-vector-icons/Ionicons';
import SideBar from './SideBar';
import { TextInput, ScrollView } from 'react-native-gesture-handler';
import API from '../constant/API';
import ApiClient from '../util/ApiClient';
import Styles from '../constant/Styles';
// import Switch from 'react-native-switch-pro';
import {
  getTransformForScreenInsideNavigation,
  isTablet
} from '../util/common';
import { UserStore } from '../store/userStore';
import { MerchantStore } from '../store/merchantStore';
// import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import { useKeyboard } from '../hooks';
import { CommonStore } from '../store/commonStore';
import { EXPAND_TAB_TYPE, WEEK } from '../constant/common';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
import { parseValidPriceText } from '../util/common';
import APILocal from '../util/apiLocalReplacers';
import DropDownPicker from "react-native-dropdown-picker";
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
// import Clipboard from "@react-native-clipboard/clipboard";
import { grabSelfServeJourney } from '../util/grabFood';
import personicon from "../assets/image/default-profile.png";
import headerLogo from "../assets/image/logo.png";

const SettingGrabScreen = props => {
  const {
    navigation,
  } = props;

  ///////////////////////////////////////////////////////////

  const [isMounted, setIsMounted] = useState(true);

  useFocusEffect(
    useCallback(() => {
      setIsMounted(true);
      return () => {
        setIsMounted(false);
      };
    }, [])
  );

  ///////////////////////////////////////////////////////////

  const [keyboardHeight] = useKeyboard();

  const { width: windowWidth, height: windowHeight } = useWindowDimensions();

  const [loading, setLoading] = useState(false);
  const [switchMerchant, setSwitchMerchant] = useState(false);

  //////////////////////////////////////////////////////////////////////////

  const [temp, setTemp] = useState('');

  const [grabActivationUrl, setGrabActivationUrl] = useState('');

  const [targetOutletDropdownList, setTargetOutletDropdownList] = useState([]);
  const [selectedTargetOutletId, setSelectedTargetOutletId] = useState('');

  const currOutletId = MerchantStore.useState((s) => s.currOutletId);
  const currOutlet = MerchantStore.useState((s) => s.currOutlet);

  useEffect(() => { 
    console.log('currOutlet', currOutlet)
  }, [currOutlet]);

  const allOutlets = MerchantStore.useState(s => s.allOutlets);
  const merchantId = UserStore.useState(s => s.merchantId);

  const userName = UserStore.useState(s => s.name);
  const outletSelectDropdownView = CommonStore.useState(s => s.outletSelectDropdownView);

  const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
  const expandTab = CommonStore.useState((s) => s.expandTab);
  const currPageStack = CommonStore.useState((s) => s.currPageStack);

  //////////////////////////////////////////////////////////////////////

  const orderAcceptanceOption = [
    {
      label: 'Manual',
      value: 'MANUAL',
    },
    {
      label: 'Auto',
      value: 'AUTO'
    },];
  const [orderAcceptance, setOrderAcceptance] = useState('MANUAL');
  const [grabMerchantId, setGrabMerchantId] = useState('');
  const [grabCommissionGD, setGrabCommissionGD] = useState(0);
  const [grabCommissionOD, setGrabCommissionOD] = useState(0);
  const [grabCommissionT, setGrabCommissionT] = useState(0);
  const [grabCommissionDI, setGrabCommissionDI] = useState(0);

  useEffect(() => {
    if (currOutlet && currOutlet.uniqueId) {
      setGrabMerchantId(currOutlet.odGrabMID ? currOutlet.odGrabMID : '')
      setGrabCommissionGD(currOutlet.odGrabCommRate ? currOutlet.odGrabCommRate.toFixed(0) : 0)
      setGrabCommissionOD(currOutlet.odGrabDByRestaurantCommRate ? currOutlet.odGrabDByRestaurantCommRate.toFixed(0) : 0)
      setGrabCommissionT(currOutlet.odGrabTakeAwayCommRate ? currOutlet.odGrabTakeAwayCommRate.toFixed(0) : 0)
      setGrabCommissionDI(currOutlet.odGrabDineInCommRate ? currOutlet.odGrabDineInCommRate.toFixed(0) : 0)
      setOrderAcceptance(currOutlet.odGrabAccept ? currOutlet.odGrabAccept : 'MANUAL')
    }
  }, [currOutlet]);

  useEffect(() => {
    setTargetOutletDropdownList(allOutlets.map(outlet => ({ label: outlet.name, value: outlet.uniqueId })));

    if (selectedTargetOutletId === '' && allOutlets.length > 0) {
      setSelectedTargetOutletId(allOutlets[0].uniqueId);
    }
  }, [allOutlets]);

  //////////////////////////////////////////////////////////////////////////

  const setState = () => { };

  const [openDD, setOpenDD] = useState(false);


  navigation.setOptions({
          headerLeft: () => (
              <View
                  style={[
                      styles.headerLeftStyle,
                      {
                          width: windowWidth * 0.17,
                      },
                  ]}
              >
                  <img src={headerLogo} width={124} height={26} />
                  {/* <Image
                style={{
                  width: 124,
                  height: 26,
                }}
                resizeMode="contain"
                source={require('../assets/image/logo.png')}
              /> */}
              </View>
          ),
          headerTitle: () => (
              <View
                  style={[
                      {
                          justifyContent: "center",
                          alignItems: "center",
                          // marginRight: Platform.OS === 'ios' ? "27%" : 0,
                          // bottom: switchMerchant ? '2%' : 0,
                          //width:  "55%",
                      },
                      Dimensions.get("screen").width <= 768
                          ? { right: Dimensions.get("screen").width * 0.12 }
                          : {},
                  ]}
              >
                  <Text
                      style={{
                          fontSize: 24,
                          // lineHeight: 25,
                          textAlign: "center",
                          fontFamily: "NunitoSans-Bold",
                          color: Colors.whiteColor,
                          opacity: 1,
                      }}
                  >
                      Grab Settings
                  </Text>
              </View>
          ),
          headerRight: () => (
              <View
                  style={{
                      flexDirection: "row",
                      alignItems: "center",
                      justifyContent: "space-between",
                  }}
              >
                  {/* {console.log('edward test')} */}
                  {/* {console.log(outletSelectDropdownView)} */}
                  {outletSelectDropdownView && outletSelectDropdownView()}
                  <View
                      style={{
                          backgroundColor: "white",
                          width: 0.5,
                          height: Dimensions.get("screen").height * 0.025,
                          opacity: 0.8,
                          marginHorizontal: 15,
                          bottom: -1,
                          // borderWidth: 1
                      }}
                  ></View>
                  <TouchableOpacity
                      onPress={() => {
                          if (global.currUserRole === 'admin') {
                              navigation.navigate("General Settings - KooDoo BackOffice")
                          }
                      }}
                      style={{ flexDirection: "row", alignItems: "center" }}
                  >
                      <Text
                          style={{
                              fontFamily: "NunitoSans-SemiBold",
                              fontSize: 16,
                              color: Colors.secondaryColor,
                              marginRight: 15,
                          }}
                      >
                          {userName}
                      </Text>
                      <View
                          style={{
                              //backgroundColor: 'red',
                              marginRight: 30,
                              width: windowHeight * 0.05,
                              height: windowHeight * 0.05,
                              borderRadius: windowHeight * 0.05 * 0.5,
                              alignItems: "center",
                              justifyContent: "center",
                              backgroundColor: "white",
                          }}
                      >
                          <img
                              src={personicon}
                              width={windowHeight * 0.035}
                              height={windowHeight * 0.035}
                          />
                          {/* <Image
                    style={{
                      width: windowHeight * 0.05,
                    height: windowHeight * 0.05,
                      alignSelf: 'center',
                    }}
                    source={require('../assets/image/profile-pic.jpg')}
                  /> */}
                      </View>
                  </TouchableOpacity>
              </View>
          ),
        });

  const updateGrabSettingToOutlet = () => {
    var body = {
      outletId: currOutletId,
      odGrabAccept: orderAcceptance,
      odGrabMID: grabMerchantId,
      odGrabCommRate: grabCommissionGD,
      odGrabDByRestaurantCommRate: grabCommissionOD,
      odGrabTakeAwayCommRate: grabCommissionT,
      odGrabDineInCommRate: grabCommissionDI,
    };

    // console.log(body);

    // ApiClient.POST(API.updateOutletOrderDetails, body, false)
    APILocal.updateGrabSettingToOutlet({ body: body })
      .then((result) => {
        setLoading(true)

        if (result && result.status === 'success') {
          alert('Success: Grab settings has been updated.');
        }
        else{
          alert('Error: Grab settings has not been updated.');
        }
        
        setLoading(false);
      });
  }

  return (
    // <View style={styles.container}>
    //   <View style={styles.sidebar}>    
    (<UserIdleWrapper disabled={!isMounted}>
      <View style={[styles.container, !switchMerchant ? {
        transform: [
          { scaleX: 1 },
          { scaleY: 1 },
        ],
      } : {}, {
        ...getTransformForScreenInsideNavigation(),
      }]}>
        <View style={{ width: switchMerchant ? windowWidth * Styles.sideBarWidth : windowWidth * 0.08 }}>
          <SideBar navigation={props.navigation} selectedTab={6} expandProduct />
        </View>

        <ScrollView
          showsVerticalScrollIndicator={false}
          // scrollEnabled={switchMerchant}
          style={{ backgroundColor: Colors.highlightColor }}
          contentContainerStyle={{
            paddingBottom: windowHeight * 0.1,
            backgroundColor: Colors.highlightColor,
          }}>
          <ScrollView horizontal={true}>

            <View style={[styles.content, {
              width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
            }]}>
              <View
                style={{
                  backgroundColor: Colors.whiteColor,
                  // height: windowHeight - 120,
                  height: '100%',
                  width: windowWidth * 0.87,
                  alignSelf: 'center',

                  shadowColor: '#000',
                  shadowOffset: {
                    width: 0,
                    height: 2,
                  },
                  shadowOpacity: 0.22,
                  shadowRadius: 3.22,
                  // elevation: 1,
                  elevation: 3,
                  borderRadius: 5,

                  // borderRadius: 8,
                }}>
                <View>
                  <View style={{}}>
                    {/* grab merchant id */}
                    <View style={{ flexDirection: 'row', padding: 30, zIndex: -3 }}>
                      <View style={{ flex: 3 }}>
                        {/* //////////////////////////////////////////////// */}

                        <View
                          style={{ flexDirection: 'row', flex: 2, marginTop: 30, zIndex: -3 }}>
                          {/* left */}
                          <View style={{
                            justifyContent: 'center',
                            // flex: 1,
                            width: '33.33%',
                          }}>
                            <View>
                              <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14 }}>
                                {`Grab Merchant ID `}
                              </Text>
                            </View>
                          </View>

                          <View style={{
                            // flex: 1,
                            flexDirection: 'row',
                            // backgroundColor: 'red',
                            width: '40.33%',
                          }}>
                            <View
                              style={{
                                width: switchMerchant ? windowWidth * 0.26 : windowWidth * 0.26,
                                height: 50,
                                justifyContent: 'center',
                              }}>
                              <TextInput
                                underlineColorAndroid={Colors.fieldtBgColor}
                                style={[styles.textInputTakeawayCharges, { fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }, { width: switchMerchant ? windowWidth * 0.24 : windowWidth * 0.26 }]}
                                placeholderStyle={{ color: 'black', fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                itemStyle={{ justifyContent: 'flex-start', marginLeft: 10, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                placeholder="Grab Merchant ID"
                                containerStyle={{ height: 40 }}
                                placeholderTextColor={{}}
                                value={grabMerchantId}
                                //iOS
                                // clearTextOnFocus={true}
                                selectTextOnFocus
                                //////////////////////////////////////////////
                                //Android
                                // onFocus={() => {
                                //   setTemp(grabMerchantId)
                                //   setGrabMerchantId('');
                                // }}
                                ///////////////////////////////////////////////
                                //When textinput is not selected
                                // onEndEditing={() => {
                                //   if (grabMerchantId == '') {
                                //     setGrabMerchantId(temp);
                                //   }
                                // }}
                                //////////////////////////////////////////////
                                onChangeText={text => {
                                  setGrabMerchantId(text);
                                }}
                              />
                            </View>
                          </View>

                          <View style={{
                            // flex: 1,
                            justifyContent: 'center',
                            // backgroundColor: 'green',
                            width: '26.33%',
                          }}>

                          </View>
                        </View>

                        {/* //////////////////////////////////////////////// */}
                        {/* koodoo merchant id */}
                        <View
                          style={{ flexDirection: 'row', flex: 2, marginTop: 30, zIndex: -3 }}>
                          {/* left */}
                          <View style={{
                            justifyContent: 'center',
                            // flex: 1,
                            width: '33.33%',
                          }}>
                            <View>
                              <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14 }}>
                                {`KooDoo Merchant ID `}
                              </Text>
                            </View>
                          </View>

                          <View style={{
                            // flex: 1,
                            flexDirection: 'row',
                            // backgroundColor: 'red',
                            width: '40.33%',
                            alignItems: 'center',
                          }}>
                            <View
                              style={{
                                width: switchMerchant ? windowWidth * 0.26 : windowWidth * 0.26,
                                height: 50,
                                justifyContent: 'center',
                              }}>
                              <Text style={{}}>
                                {merchantId ? merchantId : 'N/A'}
                              </Text>
                            </View>
                            <View style={{ marginLeft: 10, }}>
                              <TouchableOpacity onPress={() => {
                                if (navigator.clipboard) {
                                  try {
                                    navigator.clipboard.writeText(merchantId);
                                    alert('Copied! KooDoo Merchant ID copied to clipboard.');
                                  } catch (error) {
                                    alert('Error: Failed to copy to clipboard.');
                                  }
                                }
                              }}>
                                <MaterialIcons name='content-copy' size={20} />
                              </TouchableOpacity>
                            </View>


                          </View>

                          <View style={{
                            // flex: 1,
                            justifyContent: 'center',
                            // backgroundColor: 'green',
                            width: '26.33%',
                          }}>
                          </View>
                        </View>

                        {/* //////////////////////////////////////////////// */}
                        {/* self activation flow */}
                        <View
                          style={{ flexDirection: 'row', flex: 2, marginTop: 30, zIndex: -3 }}>
                          {/* left */}
                          <View style={{
                            justifyContent: 'center',
                            // flex: 1,
                            width: '33.33%',
                          }}>
                            <View>
                              <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14 }}>
                                {`Self Activation `}
                              </Text>
                            </View>
                          </View>

                          <View style={{
                            // flex: 1,
                            flexDirection: 'row',
                            // backgroundColor: 'red',
                            width: '40.33%',
                            alignItems: 'center',
                          }}>
                            {/* <View
                              style={{
                                width: switchMerchant ? windowWidth * 0.26 : windowWidth * 0.26,
                                height: 50,
                                justifyContent: 'center',
                              }}>
                              <Text style={{}}>
                                {merchantId ? merchantId : 'N/A'}
                              </Text>
                            </View> */}
                            <View style={{
                              // width: switchMerchant ? windowWidth * 0.26 : windowWidth * 0.26,
                              // right: 20,
                            }}>
                              <TouchableOpacity
                                style={{
                                  justifyContent: 'center',
                                  flexDirection: 'row',
                                  borderWidth: 1,
                                  borderColor: Colors.primaryColor,
                                  backgroundColor: '#4E9F7D',
                                  borderRadius: 5,
                                  // width: 120,
                                  paddingHorizontal: 10,
                                  height: 35,
                                  alignItems: 'center',
                                  shadowOffset: {
                                    width: 0,
                                    height: 2,
                                  },
                                  shadowOpacity: 0.22,
                                  shadowRadius: 3.22,
                                  elevation: 1,
                                  zIndex: -1,
                                }}
                                onPress={async () => {
                                  // Clipboard.setString(merchantId);
                                  // Alert.alert('Copied!', 'KooDoo Merchant ID copied to clipboard.');

                                  if (grabActivationUrl) {
                                    Linking.openURL(grabActivationUrl);
                                  }
                                  else {
                                    const result = await grabSelfServeJourney(currOutlet.uniqueId);

                                    if (result && result.data && result.data.activationUrl) {
                                      setGrabActivationUrl(result.data.activationUrl);
                                    }
                                    else {
                                      alert('Unable to initiate the self activation flow, please contact account manager on this.');
                                    }
                                  }
                                }}>
                                <Text style={{
                                  color: Colors.whiteColor,
                                  //marginLeft: 5,
                                  fontSize: switchMerchant ? 10 : 16,
                                  fontFamily: 'NunitoSans-Bold',
                                }}>
                                  {grabActivationUrl ? 'OPEN IN BROWSER' : 'ACTIVATE'}
                                </Text>
                              </TouchableOpacity>
                            </View>


                          </View>

                          <View style={{
                            // flex: 1,
                            justifyContent: 'center',
                            // backgroundColor: 'green',
                            width: '26.33%',
                          }}>
                          </View>
                        </View>

                        {/* commission grab delivery */}
                        <View
                          style={{ flexDirection: 'row', flex: 2, marginTop: 30, zIndex: -3 }}>
                          {/* left */}
                          <View style={{
                            justifyContent: 'center',
                            // flex: 1,
                            width: '33.33%',
                          }}>
                            <View>
                              <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14 }}>
                                {`Commission Rate (Grab Delivery) `}
                              </Text>
                            </View>
                          </View>

                          <View style={{
                            // flex: 1,
                            flexDirection: 'row',
                            // backgroundColor: 'red',
                            width: '40.33%',
                          }}>
                            <View
                              style={{
                                width: switchMerchant ? windowWidth * 0.26 : windowWidth * 0.26,
                                height: 50,
                                justifyContent: 'center',
                              }}>
                              <TextInput
                                underlineColorAndroid={Colors.fieldtBgColor}
                                style={[styles.textInputTakeawayCharges, { fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }, { width: switchMerchant ? windowWidth * 0.24 : windowWidth * 0.26 }]}
                                placeholderStyle={{ color: 'black', fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                itemStyle={{ justifyContent: 'flex-start', marginLeft: 10, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                keyboardType={'decimal-pad'}
                                placeholder="0"
                                containerStyle={{ height: 40 }}
                                placeholderTextColor={{}}
                                value={grabCommissionGD}
                                //iOS
                                // clearTextOnFocus={true}
                                selectTextOnFocus
                                //////////////////////////////////////////////
                                //Android
                                // onFocus={() => {
                                //   setTemp(grabCommissionGD)
                                //   setGrabCommissionGD('');
                                // }}
                                ///////////////////////////////////////////////
                                //When textinput is not selected
                                // onEndEditing={() => {
                                //   if (grabCommissionGD == '') {
                                //     setGrabCommissionGD(temp);
                                //   }
                                // }}
                                //////////////////////////////////////////////
                                onChangeText={text => {
                                  setGrabCommissionGD(text);
                                }}
                              />
                            </View>
                          </View>

                          <View style={{
                            // flex: 1,
                            justifyContent: 'center',
                            // backgroundColor: 'green',
                            width: '26.33%',
                          }}>

                          </View>
                        </View>

                        {/* commission outlet delivery*/}
                        <View
                          style={{ flexDirection: 'row', flex: 2, marginTop: 30, zIndex: -3 }}>
                          {/* left */}
                          <View style={{
                            justifyContent: 'center',
                            // flex: 1,
                            width: '33.33%',
                          }}>
                            <View>
                              <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14 }}>
                                {`Commission Rate (Outlet Delivery) `}
                              </Text>
                            </View>
                          </View>

                          <View style={{
                            // flex: 1,
                            flexDirection: 'row',
                            // backgroundColor: 'red',
                            width: '40.33%',
                          }}>
                            <View
                              style={{
                                width: switchMerchant ? windowWidth * 0.26 : windowWidth * 0.26,
                                height: 50,
                                justifyContent: 'center',
                              }}>
                              <TextInput
                                underlineColorAndroid={Colors.fieldtBgColor}
                                style={[styles.textInputTakeawayCharges, { fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }, { width: switchMerchant ? windowWidth * 0.24 : windowWidth * 0.26 }]}
                                placeholderStyle={{ color: 'black', fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                itemStyle={{ justifyContent: 'flex-start', marginLeft: 10, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                keyboardType={'decimal-pad'}
                                placeholder="0"
                                containerStyle={{ height: 40 }}
                                placeholderTextColor={{}}
                                value={grabCommissionOD}
                                //iOS
                                // clearTextOnFocus={true}
                                selectTextOnFocus
                                //////////////////////////////////////////////
                                //Android
                                // onFocus={() => {
                                //   setTemp(grabCommissionOD)
                                //   setGrabCommissionOD('');
                                // }}
                                ///////////////////////////////////////////////
                                //When textinput is not selected
                                // onEndEditing={() => {
                                //   if (grabCommissionOD == '') {
                                //     setGrabCommissionOD(temp);
                                //   }
                                // }}
                                //////////////////////////////////////////////
                                onChangeText={text => {
                                  setGrabCommissionOD(text);
                                }}
                              />
                            </View>
                          </View>

                          <View style={{
                            // flex: 1,
                            justifyContent: 'center',
                            // backgroundColor: 'green',
                            width: '26.33%',
                          }}>

                          </View>
                        </View>

                        {/* commission Takeaway*/}
                        <View
                          style={{ flexDirection: 'row', flex: 2, marginTop: 30, zIndex: -3 }}>
                          {/* left */}
                          <View style={{
                            justifyContent: 'center',
                            // flex: 1,
                            width: '33.33%',
                          }}>
                            <View>
                              <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14 }}>
                                {`Commission Rate (Takeaway) `}
                              </Text>
                            </View>
                          </View>

                          <View style={{
                            // flex: 1,
                            flexDirection: 'row',
                            // backgroundColor: 'red',
                            width: '40.33%',
                          }}>
                            <View
                              style={{
                                width: switchMerchant ? windowWidth * 0.26 : windowWidth * 0.26,
                                height: 50,
                                justifyContent: 'center',
                              }}>
                              <TextInput
                                underlineColorAndroid={Colors.fieldtBgColor}
                                style={[styles.textInputTakeawayCharges, { fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }, { width: switchMerchant ? windowWidth * 0.24 : windowWidth * 0.26 }]}
                                placeholderStyle={{ color: 'black', fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                itemStyle={{ justifyContent: 'flex-start', marginLeft: 10, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                keyboardType={'decimal-pad'}
                                placeholder="0"
                                containerStyle={{ height: 40 }}
                                placeholderTextColor={{}}
                                value={grabCommissionT}
                                //iOS
                                // clearTextOnFocus={true}
                                selectTextOnFocus
                                //////////////////////////////////////////////
                                //Android
                                // onFocus={() => {
                                //   setTemp(grabCommissionT)
                                //   setGrabCommissionT('');
                                // }}
                                ///////////////////////////////////////////////
                                //When textinput is not selected
                                // onEndEditing={() => {
                                //   if (grabCommissionT == '') {
                                //     setGrabCommissionT(temp);
                                //   }
                                // }}
                                //////////////////////////////////////////////
                                onChangeText={text => {
                                  setGrabCommissionT(text);
                                }}
                              />
                            </View>
                          </View>

                          <View style={{
                            // flex: 1,
                            justifyContent: 'center',
                            // backgroundColor: 'green',
                            width: '26.33%',
                          }}>

                          </View>
                        </View>

                        {/* commission Dine-in*/}
                        <View
                          style={{ flexDirection: 'row', flex: 2, marginTop: 30, zIndex: -3 }}>
                          {/* left */}
                          <View style={{
                            justifyContent: 'center',
                            // flex: 1,
                            width: '33.33%',
                          }}>
                            <View>
                              <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14 }}>
                                {`Commission Rate (Dine-in) `}
                              </Text>
                            </View>
                          </View>

                          <View style={{
                            // flex: 1,
                            flexDirection: 'row',
                            // backgroundColor: 'red',
                            width: '40.33%',
                          }}>
                            <View
                              style={{
                                width: switchMerchant ? windowWidth * 0.26 : windowWidth * 0.26,
                                height: 50,
                                justifyContent: 'center',
                              }}>
                              <TextInput
                                underlineColorAndroid={Colors.fieldtBgColor}
                                style={[styles.textInputTakeawayCharges, { fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }, { width: switchMerchant ? windowWidth * 0.24 : windowWidth * 0.26 }]}
                                placeholderStyle={{ color: 'black', fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                itemStyle={{ justifyContent: 'flex-start', marginLeft: 10, fontFamily: 'NunitoSans-Regular', fontSize: switchMerchant ? 10 : 14 }}
                                keyboardType={'decimal-pad'}
                                placeholder="0"
                                containerStyle={{ height: 40 }}
                                placeholderTextColor={{}}
                                value={grabCommissionDI}
                                //iOS
                                // clearTextOnFocus={true}
                                selectTextOnFocus
                                //////////////////////////////////////////////
                                //Android
                                // onFocus={() => {
                                //   setTemp(grabCommissionDI)
                                //   setGrabCommissionDI('');
                                // }}
                                ///////////////////////////////////////////////
                                //When textinput is not selected
                                // onEndEditing={() => {
                                //   if (grabCommissionDI == '') {
                                //     setGrabCommissionDI(temp);
                                //   }
                                // }}
                                //////////////////////////////////////////////
                                onChangeText={text => {
                                  setGrabCommissionDI(text);
                                }}
                              />
                            </View>
                          </View>

                          <View style={{
                            // flex: 1,
                            justifyContent: 'center',
                            // backgroundColor: 'green',
                            width: '26.33%',
                          }}>

                          </View>
                        </View>

                        {/* order acceptance */}
                        <View
                          style={{ flexDirection: 'row', flex: 2, marginTop: 30, zIndex: 1 }}>
                          {/* left */}
                          <View style={{
                            justifyContent: 'center',
                            // flex: 1,
                            width: '33.33%',
                          }}>
                            <View>
                              <Text style={{ fontFamily: 'NunitoSans-Bold', fontSize: switchMerchant ? 10 : 14 }}>
                                {`Order Acceptance `}
                              </Text>
                            </View>
                          </View>

                          <View style={{
                            // flex: 1,
                            flexDirection: 'row',
                            // backgroundColor: 'red',
                            width: '40.33%',
                          }}>
                            <View
                              style={{
                                width: switchMerchant ? windowWidth * 0.26 : windowWidth * 0.26,
                                height: 50,
                                justifyContent: 'center',
                              }}>
                                <DropDownPicker
                                  style={{
                                    backgroundColor: Colors.fieldtBgColor,
                                    width: windowWidth * 0.26,
                                    height: 40,
                                    borderRadius: 10,
                                    borderWidth: 1,
                                    borderColor: "#E5E5E5",
                                    flexDirection: "row",
                                  }}
                                  dropDownContainerStyle={{
                                    width: windowWidth * 0.26,
                                    backgroundColor: Colors.fieldtBgColor,
                                    borderColor: "#E5E5E5",
                                  }}
                                  labelStyle={{
                                    marginLeft: 5,
                                    flexDirection: "row",
                                  }}
                                  textStyle={{
                                    fontSize: 14,
                                    fontFamily: 'NunitoSans-Regular',
                                    marginLeft: 5,
                                    paddingVertical: 10,
                                    flexDirection: "row",
                                  }}
                                  selectedItemContainerStyle={{
                                    flexDirection: "row",
                                  }}
                                  placeholderStyle={{
                                    color: Colors.fieldtTxtColor,
                                    // marginTop: 15,
                                  }}
                                  showArrowIcon={true}
                                  ArrowDownIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-down-outline"
                                    />
                                  )}
                                  ArrowUpIconComponent={({ style }) => (
                                    <Ionicon
                                      size={25}
                                      color={Colors.fieldtTxtColor}
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      name="chevron-up-outline"
                                    />
                                  )}
                
                                  showTickIcon={true}
                                  TickIconComponent={({ press }) => (
                                    <Ionicon
                                      style={{ paddingHorizontal: 5, marginTop: 5 }}
                                      color={
                                        press ? Colors.fieldtBgColor : Colors.primaryColor
                                      }
                                      name={'md-checkbox'}
                                      size={25}
                                    />
                                  )}
                                  dropDownDirection="BOTTOM"
                                  placeholder="Select"
                                  items={orderAcceptanceOption}
                                  value={orderAcceptance}
                                  onSelectItem={(item) => {
                                    setOrderAcceptance(item.value);
                                  }}
                                  open={openDD}
                                  setOpen={setOpenDD}
                                />
                            </View>
                          </View>

                          <View style={{
                            // flex: 1,
                            justifyContent: 'center',
                            // backgroundColor: 'green',
                            width: '26.33%',
                          }}>

                          </View>
                        </View>

                      </View>

                    </View>
                  </View>
                  <View style={{ alignItems: 'center', zIndex: -4, }}>
                    <TouchableOpacity
                      style={{
                        justifyContent: 'center',
                        flexDirection: 'row',
                        borderWidth: 1,
                        borderColor: Colors.primaryColor,
                        backgroundColor: '#4E9F7D',
                        borderRadius: 5,
                        width: 120,
                        paddingHorizontal: 10,
                        height: 35,
                        alignItems: 'center',
                        shadowOffset: {
                          width: 0,
                          height: 2,
                        },
                        shadowOpacity: 0.22,
                        shadowRadius: 3.22,
                        elevation: 1,
                        zIndex: -1,
                      }}
                      disabled={loading}
                      onPress={() => {
                        updateGrabSettingToOutlet();
                      }}>
                      <Text style={{
                        color: Colors.whiteColor,
                        //marginLeft: 5,
                        fontSize: switchMerchant ? 10 : 16,
                        fontFamily: 'NunitoSans-Bold',
                      }}>
                        {loading ? 'LOADING...' : 'SAVE'}
                      </Text>
                    </TouchableOpacity>
                  </View>
                  <View style={{ marginTop: 20 }}></View>
                </View>
              </View>

            </View>
          </ScrollView>
        </ScrollView>
      </View>
    </UserIdleWrapper>)
  );

}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#ffffff',
    flexDirection: 'row',
    fontFamily: 'NunitoSans-Regular',
  },
  iosStyle: {
    paddingHorizontal: 30
  },
  headerLogo: {
    width: 112,
    height: 25,
    marginLeft: 10,
  },
  headerLogo1: {
    width: '100%',
    height: '100%',
  },
  list: {
    paddingVertical: 20,
    paddingHorizontal: 30,
    flexDirection: 'row',
    alignItems: 'center',
  },
  listItem: {
    fontFamily: 'NunitoSans-Regular',
    marginLeft: 20,
    color: Colors.descriptionColor,
    fontSize: 16,
  },
  sidebar: {
    width: Dimensions.get('window').width * Styles.sideBarWidth,
    // shadowColor: '#000',
    // shadowOffset: {
    //   width: 0,
    //   height: 8,
    // },
    // shadowOpacity: 0.44,
    // shadowRadius: 10.32,

    // elevation: 16,
  },
  content: {
    padding: 20,
    width: Dimensions.get('window').width * (1 - Styles.sideBarWidth),
    // backgroundColor: 'lightgrey',
    backgroundColor: Colors.highlightColor,
  },
  textInputTakeawayCharges: {
    backgroundColor: Colors.fieldtBgColor,
    width: 200,
    height: 40,
    borderRadius: 5,
    padding: 5,
    marginVertical: 5,
    borderWidth: 1,
    borderColor: '#E5E5E5',
    paddingLeft: 10,
    //marginLeft: 190,
  },

  textInput: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
    marginRight: 0,
    flex: 1,
    flexDirection: 'row',
  },
  textInputLocation: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 100,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 10,
    marginRight: 10,
  },
  textInput8: {
    fontFamily: 'NunitoSans-Regular',
    width: 60,
    height: 50,
    flex: 1,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    paddingHorizontal: 5,
    borderColor: '#E5E5E5',
    borderWidth: 1,
  },
  textInput9: {
    fontFamily: 'NunitoSans-Regular',
    width: 110,
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 8,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  textInput10: {
    fontFamily: 'NunitoSans-Regular',
    width: 200,
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    flexDirection: 'row',
    justifyContent: 'center',
  },
  textInput1: {
    fontFamily: 'NunitoSans-Regular',
    width: 250,
    height: 40,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginBottom: 10,
  },
  textInput2: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 5,
    marginRight: 30,
  },
  textInput3: {
    fontFamily: 'NunitoSans-Regular',
    width: '85%',
    height: 50,
    backgroundColor: Colors.fieldtBgColor,
    marginTop: 10,
    marginBottom: 10,
    borderRadius: 10,
    alignSelf: 'center',
    paddingHorizontal: 10
  },
  textInput4: {
    width: '85%',
    height: 70,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    marginTop: 10,
    marginBottom: 10,
    borderRadius: 10,
  },
  textInput5: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 50,
    paddingHorizontal: 20,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 50,
  },
  textInput6: {
    fontFamily: 'NunitoSans-Regular',
    width: '80 %',
    padding: 16,
    backgroundColor: Colors.fieldtBgColor,
    marginRight: 30,
    borderRadius: 10,
    alignSelf: 'center'
  },
  textInput7: {
    fontFamily: 'NunitoSans-Regular',
    width: 300,
    height: 80,
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 7,
    marginRight: 30,
  },
  button: {
    backgroundColor: Colors.primaryColor,
    width: 150,
    padding: 8,
    borderRadius: 10,
    alignItems: 'center',
    alignSelf: 'center',
    marginBottom: 20,
  },
  button1: {
    width: '15%',
    padding: 8,
    borderRadius: 10,
    alignItems: 'center',
    alignSelf: 'center',
    marginBottom: 20,
  },
  button2: {
    backgroundColor: Colors.primaryColor,
    width: '60%',
    padding: 8,
    borderRadius: 10,
    alignItems: 'center',
    marginLeft: '2%',
  },
  button3: {
    backgroundColor: Colors.primaryColor,
    width: '30%',
    height: 50,
    borderRadius: 10,
    alignItems: 'center',
    alignSelf: 'center',
    marginBottom: 30,
  },
  textSize: {
    fontSize: 19,
    fontFamily: 'NunitoSans-SemiBold'
  },
  viewContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    flex: 0,
    width: '100%',
    marginBottom: 15,
  },
  openHourContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    flex: 1,
    marginBottom: 15,
    width: '100%',
  },
  addButtonView: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 200,
    height: 40,
    alignItems: 'center',
  },
  addButtonView1: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 150,
    height: 40,
    alignItems: 'center',
  },
  addNewView: {
    flexDirection: 'row',
    justifyContent: 'flex-start',
    marginBottom: 65,
    marginTop: 7,
    width: '83%',
    alignSelf: 'flex-end',
  },
  addNewView1: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 10,
    alignItems: 'center',
  },
  merchantDisplayView: {
    flexDirection: 'row',
    flex: 1,
    marginLeft: '17%'
  },
  shiftView: {
    flexDirection: 'row',
    backgroundColor: Colors.fieldtBgColor,
    borderRadius: 50,
    width: 200,
    height: 60,
    alignItems: 'center',
    marginTop: 30,
    alignSelf: 'center',
  },
  shiftText: {
    marginLeft: '15%',
    color: Colors.primaryColor,
    fontFamily: 'NunitoSans-SemiBold',
    fontSize: 25,
  },
  closeView: {
    flexDirection: 'row',
    borderRadius: 5,
    borderColor: Colors.primaryColor,
    borderWidth: 1,
    width: 200,
    height: 40,
    alignItems: 'center',
    marginTop: 30,
    alignSelf: 'center',
  },
  taxView: {
    flexDirection: 'row',
    borderWidth: 2,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 150,
    height: 40,
    alignItems: 'center',
    marginTop: 20,
    alignSelf: 'flex-end',
  },
  sectionView: {
    flexDirection: 'row',
    borderRadius: 5,
    padding: 16,
    alignItems: 'center',
  },
  receiptView: {
    flexDirection: 'row',
    borderWidth: 1,
    borderColor: Colors.primaryColor,
    borderRadius: 5,
    width: 200,
    height: 40,
    alignItems: 'center',
    alignSelf: 'flex-end',
  },
  pinBtn: {
    backgroundColor: Colors.fieldtBgColor,
    width: 70,
    height: 70,
    marginBottom: 16,
    alignContent: 'center',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
  },
  pinNo: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  confirmBox: {
    width: '30%',
    height: '30%',
    borderRadius: 30,
    backgroundColor: Colors.whiteColor,
  },
  headerLeftStyle: {
    width: Dimensions.get('screen').width * 0.17,
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default SettingGrabScreen;