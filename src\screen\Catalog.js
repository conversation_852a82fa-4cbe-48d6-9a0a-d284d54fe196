import React, { Component, useReducer, useState, useEffect, useCallback } from 'react';
import {
  Text,
  StyleSheet,
  Image,
  View,

  TouchableOpacity,
  Dimensions,
  Switch,
  Modal as ModalComponent,
  Platform,
  KeyboardAvoidingView,
  ActivityIndicator,
  useWindowDimensions,
  CheckBox
} from 'react-native';
import Colors from '../constant/Colors';
import SideBar from './SideBar';
import Icon from 'react-native-vector-icons/Feather';
import AntDesign from 'react-native-vector-icons/AntDesign';
import Entypo from 'react-native-vector-icons/Entypo';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';
import EvilIcons from 'react-native-vector-icons/EvilIcons';
import { TextInput, FlatList, ScrollView } from 'react-native-gesture-handler';
import API from '../constant/API';
import ApiClient from '../util/ApiClient';
import * as User from '../util/User';
import AsyncStorage from '@react-native-async-storage/async-storage';
// import CheckBox from '@react-native-community/checkbox';
import moment from 'moment';
import DateTimePickerModal from 'react-native-modal-datetime-picker';
import Styles from '../constant/Styles';
import {
    // isTablet,
    getTransformForModalInsideNavigation,
    getTransformForScreenInsideNavigation,
    logEventAnalytics,
} from '../util/common';
import { OutletStore } from '../store/outletStore';
import { MerchantStore } from '../store/merchantStore';
import { UserStore } from '../store/userStore';
// import FontAwesome from 'react-native-vector-icons/FontAwesome';
// import Upload from '../assets/svg/Upload';
// import Download from '../assets/svg/Download';
import {
    listenToUserChangesMerchant,
    listenToMerchantIdChangesMerchant,
    listenToCurrOutletIdChangesWaiter,
    listenToAllOutletsChangesMerchant,
    listenToCommonChangesMerchant,
    listenToSelectedOutletItemChanges,
    convertArrayToCSV,
    listenToSelectedOutletTableIdChanges,
    requestNotificationsPermission,
} from '../util/common';
import Feather from 'react-native-vector-icons/Feather';
import DropDownPicker from 'react-native-dropdown-picker';
import Ionicon from 'react-native-vector-icons/Ionicons';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import { CommonStore } from '../store/commonStore';
import AsyncImage from '../components/asyncImage';
import { useKeyboard } from '../hooks';
import AIcon from 'react-native-vector-icons/AntDesign';
import {
    CATALOG_SORT_FIELD_TYPE,
    CATALOG_SORT_FIELD_TYPE_VALUE,
    REPORT_SORT_FIELD_TYPE_COMPARE,
    REPORT_SORT_COMPARE_OPERATOR,
    EXPAND_TAB_TYPE,
} from '../constant/common';
import APILocal from '../util/apiLocalReplacers';
import { useFocusEffect } from '@react-navigation/native';
import UserIdleWrapper from '../components/userIdleWrapper';
import { ANALYTICS, ANALYTICS_PARSED } from '../constant/analytics';
import { Collections } from "../constant/firebase";
// import { firebase } from "@react-native-firebase/firestore";
import firebase from 'firebase/app';
import personicon from "../assets/image/default-profile.png";
import headerLogo from "../assets/image/logo.png";

////////////////////////////////////
const ModalView =  ModalComponent;

///////////////////////////////////

///////////////////////////////////

const CATALOG_SECTION = {
    ADD_CATALOG: 'CATALOG_SECTION.ADD_CATALOG',
    CATALOG_LIST: 'CATALOG_SECTION.CATALOG_LIST',
    EDIT_CATALOG: 'CATALOG_SECTION.EDIT_CATALOG',
};

const transformOutletItems = (outletItems) => {
    return outletItems.map(item => {
        const key = Object.keys(item)[0]; // Get the first key (SKU)
        return {
            sku: key,
            ...item[key] // Spread the properties of the item
        };
    });
};

const Catalog = (props) => {
    const { navigation } = props;

    ///////////////////////////////////////////////////////////

    const [isMounted, setIsMounted] = useState(true);

    useFocusEffect(
        useCallback(() => {
            setIsMounted(true);
            return () => {
                setIsMounted(false);
            };
        }, [])
    );

    ///////////////////////////////////////////////////////////

    const { width: windowWidth, height: windowHeight } = useWindowDimensions();

    //////////////////////////////////// UseState Here
    const [keyboardHeight] = useKeyboard();

    const userId = UserStore.useState((s) => s.firebaseUid);
    const userName = UserStore.useState((s) => s.name);
    const merchantName = MerchantStore.useState((s) => s.name);

    const [list1, setList1] = useState(true);
    const [customerList, setCustomerList] = useState([]);

    const [perPage, setPerPage] = useState(10);
    const [currentPage, setCurrentPage] = useState(1);
    const [pageCount, setPageCount] = useState(0);
    const [page, setPage] = useState(0);

    const [search, setSearch] = useState('');
    const [searchItem, setSearchItem] = useState('');

    const [visible, setVisible] = useState(false);

    const [controller, setController] = useState({});
    const [switchMerchant, setSwitchMerchant] = useState(false);
    const [catalogSection, setCatalogSection] = useState(
        CATALOG_SECTION.CATALOG_LIST,
    );
    const [userTagName, setUserTagName] = useState('');
    const [userTagList, setUserTagList] = useState([]);
    const [userTagDropdownList, setUserTagDropdownList] = useState([]);
    const [selectedUserTagList, setSelectedUserTagList] = useState([]);
    const [searchingUserTagText, setSearchingUserTagText] = useState('');

    const [catalogName, setCatalogName] = useState('');

    const [temp, setTemp] = useState('');
    //////////////////////////////////////////////////////////////

    const currOutletId = MerchantStore.useState((s) => s.currOutletId);
    const allOutlets = MerchantStore.useState((s) => s.allOutlets);
    const merchantId = UserStore.useState((s) => s.merchantId);
    const crmUserTags = OutletStore.useState((s) => s.crmUserTags);
    const crmUserTagsDict = OutletStore.useState((s) => s.crmUserTagsDict);
    const selectedCatalogEdit = CommonStore.useState(
        (s) => s.selectedCatalogEdit,
    );

    const outletCatalog = OutletStore.useState((s) => s.outletCatalog);

    const [currSummarySort, setCurrSummarySort] = useState('');

    // const selectedCustomerDineInOrders = OutletStore.useState(s => s.selectedCustomerDineInOrders);
    const isLoading = CommonStore.useState((s) => s.isLoading);

    const outletSelectDropdownView = CommonStore.useState(
        (s) => s.outletSelectDropdownView,
    );
    const [pushPagingToTop, setPushPagingToTop] = useState(false);

    const isAlphaUser = UserStore.useState((s) => s.isAlphaUser);
    const expandTab = CommonStore.useState((s) => s.expandTab);
    const currPageStack = CommonStore.useState((s) => s.currPageStack);
    const outletItems = OutletStore.useState((s) => s.outletItems);
    const [itemList, setItemList] = useState([]);
    const currOutlet = MerchantStore.useState((s) => s.currOutlet);
    const [isEdit, setIsEdit] = useState(false);

    
    useEffect(() => {
        if (!currOutletId || !merchantId) return;

        const unsubscribe = firebase.firestore()
            .collection(Collections.OutletCatalog)
            .where('outletId', '==', currOutletId)
            .where('merchantId', '==', merchantId)
            .onSnapshot((snapshot) => {
                var outletCatalog = [];

                if (snapshot && !snapshot.empty) {
                    for (var i = 0; i < snapshot.size; i++) {
                        const record = snapshot.docs[i].data();
                        outletCatalog.push(record);
                    }
                }

                outletCatalog.sort((a, b) => (a.catalogName ? a.catalogName : '').localeCompare(b.catalogName ? b.catalogName : ''));

                OutletStore.update((s) => {
                    s.outletCatalog = outletCatalog;
                });
            }, (error) => {
                console.error('Error listening to catalog changes:', error);
            });

        return () => {
            if (unsubscribe) {
                unsubscribe();
            }
        };
    }, [currOutletId, merchantId, isMounted]);

    useEffect(() => {
        // console.log('================================');
        // console.log('selectedCatalogEdit');
        // console.log(selectedCatalogEdit);

        if (selectedCatalogEdit) {
            // insert info

            setCatalogName(selectedCatalogEdit.catalogName);
            // setItemList(selectedCatalogEdit.outletItems);
        } else {
            // designed to always mounted, thus need clear manually...

            setCatalogName('');
            setItemList([]);
        }
    }, [selectedCatalogEdit, catalogSection]);

    //////////////////////////////////////////////////////////////

    //////////////////////////////////// UseEffect here

    useEffect(() => {
        if(outletCatalog?.length){
            setCurrentPage(1);
            setPageCount(Math.ceil(outletCatalog.length / perPage));
        }
    }, [outletCatalog]);

    ///////// SORT //////////
    const sortCatalog = (dataList, catalogSortFieldType) => {
        var dataListTemp = [...dataList];

        const CatalogSortFieldTypeValue =
            CATALOG_SORT_FIELD_TYPE[catalogSortFieldType];
        const CatalogSortFieldTypeCompare =
            REPORT_SORT_FIELD_TYPE_COMPARE[catalogSortFieldType];

        if (CatalogSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.ASC) {
            if (catalogSortFieldType === CATALOG_SORT_FIELD_TYPE.CATALOG_NAME_ASC) {
                dataListTemp.sort((a, b) =>
                    (a[CatalogSortFieldTypeValue]
                        ? a[CatalogSortFieldTypeValue]
                        : ''
                    ).localeCompare(
                        b[CatalogSortFieldTypeValue] ? b[CatalogSortFieldTypeValue] : '',
                    ),
                );
            } else {
                dataListTemp.sort(
                    (a, b) =>
                        (a[CatalogSortFieldTypeValue] ? a[CatalogSortFieldTypeValue] : '') -
                        (b[CatalogSortFieldTypeValue] ? b[CatalogSortFieldTypeValue] : ''),
                );
            }
        } else if (
            CatalogSortFieldTypeCompare === REPORT_SORT_COMPARE_OPERATOR.DESC
        ) {
            if (catalogSortFieldType === CATALOG_SORT_FIELD_TYPE.CATALOG_NAME_DESC) {
                dataListTemp.sort((a, b) =>
                    (b[CatalogSortFieldTypeValue]
                        ? b[CatalogSortFieldTypeValue]
                        : ''
                    ).localeCompare(
                        a[CatalogSortFieldTypeValue] ? a[CatalogSortFieldTypeValue] : '',
                    ),
                );
            } else {
                dataListTemp.sort(
                    (a, b) =>
                        (b[CatalogSortFieldTypeValue] ? b[CatalogSortFieldTypeValue] : '') -
                        (a[CatalogSortFieldTypeValue] ? a[CatalogSortFieldTypeValue] : ''),
                );
            }
        }
        return dataListTemp;
    };

    //////////////////////////////////// Page standardize pattern here

    navigation.setOptions({
        headerLeft: () => (
            <View
                style={[
                    styles.headerLeftStyle,
                    {
                        width: windowWidth * 0.17,
                    },
                ]}
            >
                <img src={headerLogo} width={124} height={26} />
                {/* <Image
              style={{
                width: 124,
                height: 26,
              }}
              resizeMode="contain"
              source={require('../assets/image/logo.png')}
            /> */}
            </View>
        ),
        headerTitle: () => (
            <View
                style={[
                    {
                        justifyContent: "center",
                        alignItems: "center",
                        // marginRight: Platform.OS === 'ios' ? "27%" : 0,
                        // bottom: switchMerchant ? '2%' : 0,
                        //width:  "55%",
                    },
                    Dimensions.get("screen").width <= 768
                        ? { right: Dimensions.get("screen").width * 0.12 }
                        : {},
                ]}
            >
                <Text
                    style={{
                        fontSize: 24,
                        // lineHeight: 25,
                        textAlign: "center",
                        fontFamily: "NunitoSans-Bold",
                        color: Colors.whiteColor,
                        opacity: 1,
                    }}
                >
                    Catalog
                </Text>
            </View>
        ),
        headerRight: () => (
            <View
                style={{
                    flexDirection: "row",
                    alignItems: "center",
                    justifyContent: "space-between",
                }}
            >
                {/* {console.log('edward test')} */}
                {/* {console.log(outletSelectDropdownView)} */}
                {outletSelectDropdownView && outletSelectDropdownView()}
                <View
                    style={{
                        backgroundColor: "white",
                        width: 0.5,
                        height: Dimensions.get("screen").height * 0.025,
                        opacity: 0.8,
                        marginHorizontal: 15,
                        bottom: -1,
                        // borderWidth: 1
                    }}
                ></View>
                <TouchableOpacity
                    onPress={() => {
                        if (global.currUserRole === 'admin') {
                            navigation.navigate("General Settings - KooDoo BackOffice")
                        }
                    }}
                    style={{ flexDirection: "row", alignItems: "center" }}
                >
                    <Text
                        style={{
                            fontFamily: "NunitoSans-SemiBold",
                            fontSize: 16,
                            color: Colors.secondaryColor,
                            marginRight: 15,
                        }}
                    >
                        {userName}
                    </Text>
                    <View
                        style={{
                            //backgroundColor: 'red',
                            marginRight: 30,
                            width: windowHeight * 0.05,
                            height: windowHeight * 0.05,
                            borderRadius: windowHeight * 0.05 * 0.5,
                            alignItems: "center",
                            justifyContent: "center",
                            backgroundColor: "white",
                        }}
                    >
                        <img
                            src={personicon}
                            width={windowHeight * 0.035}
                            height={windowHeight * 0.035}
                        />
                        {/* <Image
                  style={{
                    width: windowHeight * 0.05,
                  height: windowHeight * 0.05,
                    alignSelf: 'center',
                  }}
                  source={require('../assets/image/profile-pic.jpg')}
                /> */}
                    </View>
                </TouchableOpacity>
            </View>
        ),
      });

    ///////////////////////////////////////////////////////Function here

    const nextPage = () => {
        setCurrentPage(currentPage + 1 > pageCount ? currentPage : currentPage + 1);
    };

    const prevPage = () => {
        setCurrentPage(currentPage - 1 < 1 ? currentPage : currentPage - 1);
    };


    const renderItem = ({ item, index }) => {
        return (
            <TouchableOpacity
                onPress={() => {
                    CommonStore.update((s) => {
                        s.selectedCatalogEdit = item;
                    });

                    setSearch('');
                    setCatalogSection(CATALOG_SECTION.EDIT_CATALOG);
                }}>
                <View
                    style={{
                        //backgroundColor: (index + 1) % 2 == 0 ? Colors.whiteColor : Colors.highlightColor,
                        backgroundColor: '#FFFFFF',
                        paddingVertical: 15,
                        paddingHorizontal: 5,
                        //paddingLeft: 1,
                        borderColor: '#BDBDBD',
                        borderTopWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
                        borderBottomWidth: (index + 1) % 2 == 0 ? 0 : 0.5,
                        // width: '100%',
                    }}>
                    <View style={{ flexDirection: 'row', alignItems: 'center', }}>
                        <View style={{ width: '50%', paddingLeft: 5, }}>
                            <Text
                                style={
                                    switchMerchant
                                        ? {
                                            fontWeight: '600',
                                            color: Colors.primaryColor,
                                            fontSize: 10,
                                            fontFamily: 'NunitoSans-Regular',
                                        }
                                        : {
                                            fontWeight: '600',
                                            color: Colors.primaryColor,
                                            fontSize: 14,
                                            fontFamily: 'NunitoSans-Regular',
                                        }
                                }>
                                {item.catalogName}
                            </Text>
                        </View>
                        <View style={{ width: '20%', paddingLeft: 5, }}>
                            <Text
                                style={{
                                    fontWeight: '600',
                                    color: Colors.primaryColor,
                                    fontSize: 14,
                                    fontFamily: 'NunitoSans-Regular',
                                }}>
                                {item.outletItems.length}
                            </Text>
                        </View>
                        <View style={{ width: '15%', paddingLeft: 5, }}>
                            <Text
                                style={{
                                    fontWeight: '600',
                                    color: currOutlet.odGrabCatalogId && currOutlet.odGrabCatalogId === item.uniqueId ? Colors.primaryColor : 'red',
                                    fontSize: 14,
                                    fontFamily: 'NunitoSans-Regular',
                                }}>
                                {currOutlet.odGrabCatalogId && currOutlet.odGrabCatalogId === item.uniqueId ? 'Active' : 'Inactive'}
                            </Text>
                        </View>
                        <View style={{ width: '15%', marginLeft: 10, flexDirection: 'row' }}>
                            <TouchableOpacity
                                style={{
                                    justifyContent: 'center',
                                    flexDirection: 'row',
                                    borderWidth: 1,
                                    borderColor: Colors.primaryColor,
                                    backgroundColor: '#4E9F7D',
                                    borderRadius: 5,
                                    //width: 160,
                                    paddingHorizontal: 10,
                                    marginRight: 10,
                                    height: switchMerchant ? 35 : 40,
                                    alignItems: 'center',
                                    shadowOffset: {
                                        width: 0,
                                        height: 2,
                                    },
                                    shadowOpacity: 0.22,
                                    shadowRadius: 3.22,
                                    elevation: 1,
                                    zIndex: -1,
                                    marginRight: 15,
                                }}
                                onPress={() => {
                                    CommonStore.update((s) => {
                                        s.selectedCatalogEdit = item;
                                    });
                                    setVisible(true);
                                }}>
                                <Text
                                    style={{
                                        color: Colors.whiteColor,
                                        // marginLeft: 5,
                                        fontSize: switchMerchant ? 10 : 16,
                                        fontFamily: 'NunitoSans-Bold',
                                    }}>
                                    PUBLISH
                                </Text>
                            </TouchableOpacity>
                        </View>
                    </View>
                </View>
            </TouchableOpacity>
        );
    };

    // In your component where you set the data for FlatList
    const [transformedItems, setTransformedItems] = useState([]);

    useEffect(() => {
        // Update transformedItems whenever selectedCatalogEdit changes
        if (selectedCatalogEdit) {
            const items = transformOutletItems(selectedCatalogEdit.outletItems);
            setTransformedItems(items);
        } else {
            setTransformedItems([]); // Clear items if no catalog is selected
        }
    }, [selectedCatalogEdit, catalogSection]);


    const renderLinkedItems = ({ item }) => {
        // <TouchableOpacity
        //     onPress={() => {
        //         isChecked(item)
        //     }}>
        const name = item.n || 'N/A';
        const skuMerchant = item.s || 'N/A';
        const imgae = item.i || '';
        const price = item.p || 0;
        return (
            <View
                style={{
                    backgroundColor: '#FFFFFF',
                    flexDirection: 'row',
                    paddingVertical: 10,
                    paddingHorizontal: 5,
                    borderBottomWidth: StyleSheet.hairlineWidth,
                    borderBottomColor: '#C4C4C4',
                    //minHeight: 90,
                    alignItems: 'center',
                }}>
                <View
                    style={{
                        width: '10%',
                        paddingLeft: 10,
                        justifyContent: 'center',
                        alignItems: 'center',
                    }}>
                    {imgae ? (
                        <AsyncImage
                            source={{ uri: imgae }}
                            item={item}
                            hideLoading
                            style={{
                                width: switchMerchant ? 35 : 45,
                                height: switchMerchant ? 35 : 45,
                                borderWidth: 1,
                                borderColor: '#E5E5E5',
                                borderRadius: 5,
                            }}
                        />
                    ) : (
                        <Ionicon
                            name="fast-food-outline"
                            size={switchMerchant ? 25 : 35}
                        />
                    )}
                </View>
                <Text
                    style={{
                        width: '11%',
                        fontFamily: 'NunitoSans-Regular',
                        fontSize: switchMerchant ? 10 : 14,
                        textAlign: 'left',
                    }}
                    numberOfLines={1}>
                    {skuMerchant || 'N/A'}
                </Text>
                <View
                    style={{
                        width: '55%',
                        // alignItems: 'center',
                        justifyContent: 'flex-start',
                        paddingRight: 20,
                    }}>
                    <Text
                        style={{
                            fontFamily: 'NunitoSans-Regular',
                            fontSize: switchMerchant ? 10 : 14,
                            textAlign: 'left',
                        }}
                        numberOfLines={2}>
                        {name}
                    </Text>
                </View>

                <Text
                    style={{
                        width: '12%',
                        fontFamily: 'NunitoSans-Regular',
                        fontSize: switchMerchant ? 10 : 14,
                        textAlign: 'left',
                    }}>
                    RM {(price).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                </Text>
            </View>
            // </TouchableOpacity>
        )
    };

    const isChecked = (item) => {
        var selectedItems = outletItems.filter(
            (o) => o.uniqueId === item.uniqueId,
        );
        //console.log('D ITEM', selectedDeleteItems);

        const convertToObj = outletItems.find((o) => {
            return selectedItems.map(item => item.uniqueId).includes(o.uniqueId);
        });

        const itemExists = itemList.find((o) => {
            return selectedItems.map(item => item.uniqueId).includes(o.uniqueId);
        });

        if (itemExists) {
            setItemList(itemList.filter((item) => item.uniqueId !== itemExists.uniqueId));
        }
        else {
            setItemList([...itemList, convertToObj]);
        }
    };

    const renderOutletItems = ({ item }) => (
        <TouchableOpacity
            onPress={() => {
                isChecked(item)
            }}>
            <View
                style={{
                    backgroundColor: '#FFFFFF',
                    flexDirection: 'row',
                    paddingVertical: 10,
                    paddingHorizontal: 5,
                    borderBottomWidth: StyleSheet.hairlineWidth,
                    borderBottomColor: '#C4C4C4',
                    //minHeight: 90,
                    alignItems: 'center',
                }}>
                <View
                    style={{
                        width: '10%',
                        paddingLeft: 10,
                        justifyContent: 'center',
                        alignItems: 'center',
                    }}>
                    {item.image ? (
                        <AsyncImage
                            source={{ uri: item.image }}
                            item={item}
                            hideLoading
                            style={{
                                width: switchMerchant ? 35 : 45,
                                height: switchMerchant ? 35 : 45,
                                borderWidth: 1,
                                borderColor: '#E5E5E5',
                                borderRadius: 5,
                            }}
                        />
                    ) : (
                        <Ionicon
                            name="fast-food-outline"
                            size={switchMerchant ? 25 : 35}
                        />
                    )}
                </View>
                <Text
                    style={{
                        width: '11%',
                        fontFamily: 'NunitoSans-Regular',
                        fontSize: switchMerchant ? 10 : 14,
                        textAlign: 'left',
                    }}
                    numberOfLines={1}>
                    {item.skuMerchant || 'N/A'}
                </Text>
                <View
                    style={{
                        width: '55%',
                        // alignItems: 'center',
                        justifyContent: 'flex-start',
                        paddingRight: 20,
                    }}>
                    <Text
                        style={{
                            fontFamily: 'NunitoSans-Regular',
                            fontSize: switchMerchant ? 10 : 14,
                            textAlign: 'left',
                        }}
                        numberOfLines={2}>
                        {item.name}
                    </Text>
                </View>

                <Text
                    style={{
                        width: '12%',
                        fontFamily: 'NunitoSans-Regular',
                        fontSize: switchMerchant ? 10 : 14,
                        textAlign: 'left',
                    }}>
                    RM {(item.price || 0).toFixed(2).replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1,')}
                </Text>

                <View
                    style={{
                        width: '12%',
                        flexDirection: 'column',
                        alignItems: 'flex-start',
                        //justifyContent: 'flex-start',
                    }}>
                    <CheckBox
                        // disabled
                        value={itemList.find((findItem) => findItem.uniqueId === item.uniqueId) ? true : false}
                        onValueChange={() => isChecked(item)}
                        style={{
                            // bottom: 1,
                            // height: 25,
                        }}
                    />
                </View>
            </View>
        </TouchableOpacity>
    );

    //   Create New Catalog with Outlet Items
    const createCatalog = async () => {
        if (!catalogName) {
            alert('Catalog name cannot be empty');
        } else if (itemList.length <= 0) {
            alert('Please select at least one item');
        } else {
            const outletItemsBody = {};
            itemList.forEach(item => {
                outletItemsBody[item.sku] = {
                    cId: item.categoryId,
                    n: item.name,
                    p: item.price,
                    i: item.image,
                    s: item.skuMerchant || 'N/A',
                };
            });

            const body = {
                catalogName: catalogName,
                outletItems: outletItemsBody,
                outletId: currOutletId, // Assuming currOutletId is defined in your component
                merchantId: merchantId,
                odTypes: [
                    'GRABFOOD',
                    'FOODPANDA',
                    'SHOPEEFOOD',
                ],
            };

            CommonStore.update((s) => {
                s.isLoading = true;
            });

            // Call the API to create the catalog
            APILocal.createOutletCatalog({ body: body, uid: userId })
                .then((result) => {
                    if (result && result.status === 'success') {
                        alert('Catalog has been created');
                        setCatalogSection(CATALOG_SECTION.CATALOG_LIST);
                    } else {
                        console.error('Create Error:', result);
                        alert('Something went wrong. Please try again later.');
                    }

                    CommonStore.update((s) => {
                        s.isLoading = false;
                    });
                });
        }
    };

    const updateCatalog = async () => {
        if (!catalogName) {
            alert('Catalog name cannot be empty');
        } else if (itemList.length <= 0) {
            alert('Please select at least one item');
        } else {
            const outletItemsBody = {};
            itemList.forEach(item => {
                outletItemsBody[item.sku] = {
                    cId: item.categoryId,
                    n: item.name,
                    p: item.price,
                    i: item.image,
                    s: item.skuMerchant || 'N/A',
                };
            });

            const body = {
                catalogId: selectedCatalogEdit.uniqueId,
                catalogName: catalogName,
                outletItems: outletItemsBody,
                outletId: currOutletId, // Assuming currOutletId is defined in your component
                merchantId: merchantId,
                odTypes: [
                    'GRABFOOD',
                    'FOODPANDA',
                    'SHOPEEFOOD',
                ],
            };

            CommonStore.update((s) => {
                s.isLoading = true;
            });

            // Call the API to create the catalog
            APILocal.updateOutletCatalog({ body: body, uid: userId })
                .then((result) => {
                    if (result && result.status === 'success') {
                        alert('Catalog has been updated');
                        setCatalogSection(CATALOG_SECTION.CATALOG_LIST);
                    } else {
                        console.error('Create Error:', result);
                        alert('Something went wrong. Please try again later.');
                    }

                    CommonStore.update((s) => {
                        s.isLoading = false;
                    });
                });
        }
    };

    // 2025-02-18 - publish items to other delivery partner (grabfood, etc)

    const publishItemsToOD = () => {
        if (currOutlet.odGrabMID) {
            var body = {
                odGrabMID: currOutlet.odGrabMID,
            };

            // console.log(body);

            CommonStore.update((s) => {
                s.isLoading = true;
            });

            ApiClient.POST(API.grabUpdateMenuNotification, body)
                .then((result) => {
                    if (result && result.status === 'success') {
                        alert('Menu has been published.');
                    } else {
                        alert('Failed to publish the menu.');
                    }

                    CommonStore.update((s) => {
                        s.isLoading = false;
                    });
                });
        }
        else {
            alert('Please contact your account manager to link your Grab Merchant ID first.');
        }
    };

    const publishCatalog = async () => {
        if (selectedCatalogEdit) {
            try {
                // Update the outlet document with the catalog ID
                await firebase.firestore().collection(Collections.Outlet).doc(currOutletId).update({
                    odGrabCatalogId: selectedCatalogEdit.uniqueId,
                });

                publishItemsToOD();

                alert('Catalog published successfully.');
            } catch (error) {
                console.error('Error publishing catalog:', error);
                alert('Failed to publish the catalog.');
            }
        } else {
            alert('No catalog selected for publishing.');
        }
    };

    ///////////////////////////////////////////////////////

    return (
        <View
            style={[
                styles.container,
                !switchMerchant
                    ? {
                        transform: [{ scaleX: 1 }, { scaleY: 1 }],
                    }
                    : {},
                {
                    ...getTransformForScreenInsideNavigation(),
                }
            ]}>
            <View style={{ width: switchMerchant ? windowWidth * Styles.sideBarWidth : windowWidth * 0.08 }}>
                <SideBar navigation={props.navigation} selectedTab={6} expandProduct />
            </View>

            <ScrollView showsVerticalScrollIndicator={false} style={{}}>
                <ScrollView
                    horizontal={true}
                    scrollEnabled={switchMerchant}
                    showsHorizontalScrollIndicator={false}>
                    <ModalView
                        supportedOrientations={['landscape', 'portrait']}
                        style={{ flex: 1 }}
                        visible={visible}
                        transparent={true}
                        animationType="none">
                        <View
                            behavior="padding"
                            style={{
                                backgroundColor: 'rgba(0,0,0,0.5)',
                                flex: 1,
                                justifyContent: 'center',
                                alignItems: 'center',
                                // minHeight: windowHeight,
                            }}>
                            <View
                                style={{
                                    backgroundColor: 'white',
                                    borderRadius: 12,
                                    padding: 10,
                                    width: windowWidth * 0.33,
                                    height: windowHeight * 0.38,
                                    ...getTransformForModalInsideNavigation(),
                                }}>
                                <View
                                    style={{
                                        position: 'absolute',
                                        right: windowWidth * 0.02,
                                        top: windowWidth * 0.02,
                                        zIndex: 9999,
                                    }}>
                                    <TouchableOpacity
                                        onPress={() => {
                                            setVisible(false);
                                        }}>
                                        <AntDesign
                                            name={'closecircle'}
                                            size={25}
                                            color={'#858C94'}
                                        />
                                    </TouchableOpacity>
                                </View>
                                <View
                                    style={{
                                        justifyContent: 'center',
                                        alignItems: 'center',
                                        marginTop: 20
                                    }}>
                                    <Text style={{ fontSize: 26, fontFamily: 'Nunitosans-Bold' }}>
                                        Publish Setup
                                    </Text>
                                </View>
                                <View style={{ flexDirection: "row", marginTop: 30, alignItems: 'center', justifyContent: 'space-between', paddingHorizontal: 20, }}>
                                    <Text
                                        style={{
                                            marginLeft: 5,
                                            fontSize: 20,
                                            fontFamily: 'NunitoSans-Bold',
                                        }}>
                                        GrabFood
                                    </Text>
                                    <TouchableOpacity
                                        disabled={isLoading}
                                        style={styles.publishButtonStyle}
                                        onPress={() => {
                                            // publishItemsToOD();
                                            publishCatalog();
                                        }}>
                                        <Text
                                            style={{
                                                color: Colors.whiteColor,
                                                // marginLeft: 5,
                                                fontSize: switchMerchant ? 10 : 16,
                                                fontFamily: 'NunitoSans-Bold',
                                            }}>
                                            PUBLISH
                                        </Text>
                                    </TouchableOpacity>
                                </View>
                                {/* <View style={{ flexDirection: "row", marginTop: 20, alignItems: 'center', justifyContent: 'space-between', paddingHorizontal: 20, }}>
                                    <Text
                                        style={{
                                            marginLeft: 5,
                                            fontSize: 20,
                                            fontFamily: 'NunitoSans-Bold',
                                        }}>
                                        FoodPanda
                                    </Text>
                                    <TouchableOpacity
                                        style={styles.publishButtonStyle}
                                        onPress={() => {
                                            // publishItemsToOD();
                                        }}>
                                        <Text
                                            style={{
                                                color: Colors.whiteColor,
                                                // marginLeft: 5,
                                                fontSize: switchMerchant ? 10 : 16,
                                                fontFamily: 'NunitoSans-Bold',
                                            }}>
                                            Publish
                                        </Text>
                                    </TouchableOpacity>
                                </View> */}
                                {/* <View style={{ flexDirection: "row", marginTop: 20, alignItems: 'center', justifyContent: 'space-between', paddingHorizontal: 20, }}>
                                    <Text
                                        style={{
                                            marginLeft: 5,
                                            fontSize: 20,
                                            fontFamily: 'NunitoSans-Bold',
                                        }}>
                                        ShopeeFood
                                    </Text>
                                    <TouchableOpacity
                                        style={styles.publishButtonStyle}
                                        onPress={() => {
                                            // publishItemsToOD();
                                        }}>
                                        <Text
                                            style={{
                                                color: Colors.whiteColor,
                                                // marginLeft: 5,
                                                fontSize: switchMerchant ? 10 : 16,
                                                fontFamily: 'NunitoSans-Bold',
                                            }}>
                                            Publish
                                        </Text>
                                    </TouchableOpacity>
                                </View> */}
                            </View>
                        </View>
                    </ModalView>

                    <KeyboardAvoidingView style={{}}>
                        {catalogSection === CATALOG_SECTION.CATALOG_LIST ? (
                            <View>
                                <View
                                    style={{
                                        flexDirection: 'row',
                                        justifyContent: 'space-between',
                                        marginTop: 20,
                                        marginBottom: 20,
                                        width: switchMerchant
                                            ? windowWidth * 0.8
                                            : windowWidth * 0.87,
                                        //marginTop: 30,
                                        alignSelf: 'center',
                                        alignItems: 'center',
                                    }}>
                                    <View style={{ justifyContent: 'center' }}>
                                        <Text
                                            style={{
                                                fontSize: switchMerchant ? 20 : 26,
                                                fontFamily: 'NunitoSans-Bold',
                                            }}>
                                            {outletCatalog?.length || 0} Catalog
                                        </Text>
                                    </View>

                                    <View
                                        style={{ flexDirection: 'row', justifyContent: 'flex-end' }}>

                                        <TouchableOpacity
                                            style={{
                                                justifyContent: 'center',
                                                flexDirection: 'row',
                                                borderWidth: 1,
                                                borderColor: Colors.primaryColor,
                                                backgroundColor: '#4E9F7D',
                                                borderRadius: 5,
                                                //width: 160,
                                                paddingHorizontal: 10,
                                                marginRight: 10,
                                                height: switchMerchant ? 35 : 40,
                                                alignItems: 'center',
                                                shadowOffset: {
                                                    width: 0,
                                                    height: 2,
                                                },
                                                shadowOpacity: 0.22,
                                                shadowRadius: 3.22,
                                                elevation: 1,
                                                zIndex: -1,
                                                marginRight: 15,
                                            }}
                                            onPress={() => {
                                                CommonStore.update((s) => {
                                                    s.selectedCatalogEdit = null;
                                                });
                                                setSearch('');
                                                setItemList([]);
                                                setCatalogSection(CATALOG_SECTION.ADD_CATALOG);
                                            }}>
                                            <AntDesign
                                                name="pluscircle"
                                                size={switchMerchant ? 10 : 20}
                                                style={{ color: Colors.whiteColor }}
                                            />
                                            <Text
                                                style={{
                                                    color: Colors.whiteColor,
                                                    marginLeft: 5,
                                                    fontSize: switchMerchant ? 10 : 16,
                                                    fontFamily: 'NunitoSans-Bold',
                                                }}>
                                                {/* ADD CATALOG */}
                                                CATALOG
                                            </Text>
                                        </TouchableOpacity>

                                        <View
                                            style={{
                                                width: switchMerchant ? 200 : 250,
                                                height: switchMerchant ? 35 : 40,
                                                backgroundColor: 'white',
                                                borderRadius: 5,
                                                flexDirection: 'row',
                                                alignItems: 'center',
                                                shadowColor: '#000',
                                                shadowOffset: {
                                                    width: 0,
                                                    height: 2,
                                                },
                                                shadowOpacity: 0.22,
                                                shadowRadius: 3.22,
                                                elevation: 3,
                                                borderWidth: 1,
                                                borderColor: '#E5E5E5',
                                                //marginTop: 10,
                                            }}>
                                            <Icon
                                                name="search"
                                                size={switchMerchant ? 10 : 18}
                                                color={Colors.primaryColor}
                                                style={{ marginLeft: 15 }}
                                            />
                                            <View style={{ flex: 4 }}>
                                                <TextInput
                                                    underlineColorAndroid={Colors.whiteColor}
                                                    style={{
                                                        width: switchMerchant ? 160 : 220,
                                                        fontSize: switchMerchant ? 10 : 16,
                                                        fontFamily: 'NunitoSans-Regular',
                                                        paddingLeft: 5,
                                                        height: 45,
                                                    }}
                                                    placeholderTextColor={Platform.select({
                                                        ios: '#a9a9a9',
                                                    })}
                                                    clearButtonMode="while-editing"
                                                    placeholder=" Search"
                                                    onChangeText={(text) => {
                                                        setSearch(text);
                                                    }}
                                                    defaultValue={search}
                                                />
                                            </View>
                                        </View>
                                    </View>
                                </View>
                                {/* </View> */}
                                <View
                                    style={{
                                        backgroundColor: Colors.whiteColor,
                                        width: switchMerchant
                                            ? windowWidth * 0.8
                                            : windowWidth * 0.87,
                                        height: switchMerchant
                                            ? windowHeight * 0.6
                                            : windowHeight * 0.7,
                                        //marginTop: 30,
                                        //marginHorizontal: 30,
                                        //alignSelf: 'center',
                                        borderRadius: 5,
                                        shadowOpacity: 0,
                                        shadowColor: '#000',
                                        shadowOffset: {
                                            width: 0,
                                            height: 2,
                                        },
                                        shadowOpacity: 0.22,
                                        shadowRadius: 3.22,
                                        elevation: 3,
                                        marginHorizontal: switchMerchant ? 30 : 35,
                                        //marginBottom: switchMerchant ? 30 : 0,
                                    }}>

                                    {/****************List View Here****************/}

                                    <View style={{ width: '100%', marginTop: 0 }}>
                                        <View
                                            style={{
                                                backgroundColor: Colors.whiteColor,
                                                padding: 5,
                                                paddingTop: 0,
                                                height: '99%',
                                                paddingHorizontal: 20,
                                                borderRadius: 5,
                                            }}>
                                            <View
                                                style={{
                                                    backgroundColor: Colors.whiteColor,
                                                    flexDirection: 'row',
                                                    alignSelf: 'center',
                                                    paddingVertical: 20,
                                                    paddingHorizontal: 20,
                                                    borderBottomWidth: StyleSheet.hairlineWidth,
                                                    borderRadius: 5,
                                                    shadowOpacity: 0.22,
                                                    shadowRadius: 3.22,
                                                    elevation: 3,
                                                    width: windowWidth * 0.87,
                                                }}>
                                                <View
                                                    style={{
                                                        flexDirection: 'row',
                                                        width: '50%',
                                                        borderRightWidth: 0,
                                                        borderRightColor: 'lightgrey',
                                                        alignItems: 'center',
                                                        justifyContent: 'flex-start',
                                                        marginLeft: 0,
                                                    }}>
                                                    <View style={{ flexDirection: 'column' }}>
                                                        <TouchableOpacity
                                                            onPress={() => {
                                                                if (
                                                                    currSummarySort ===
                                                                    CATALOG_SORT_FIELD_TYPE.CATALOG_NAME_ASC
                                                                ) {
                                                                    setCurrSummarySort(
                                                                        CATALOG_SORT_FIELD_TYPE.CATALOG_NAME_DESC,
                                                                    );
                                                                } else {
                                                                    setCurrSummarySort(
                                                                        CATALOG_SORT_FIELD_TYPE.CATALOG_NAME_ASC,
                                                                    );
                                                                }
                                                            }}>
                                                            <Text
                                                                numberOfLines={1}
                                                                style={{
                                                                    paddingLeft: 5,
                                                                    fontSize: switchMerchant ? 10 : 14,
                                                                    fontFamily: 'NunitoSans-Bold',
                                                                    color: 'black',
                                                                    fontWeight: '600',
                                                                }}>
                                                                Catalog Name
                                                            </Text>
                                                            {/* <Text style={{ fontSize: 10, color: Colors.descriptionColor }}></Text> */}
                                                        </TouchableOpacity>
                                                    </View>
                                                    <View style={{ marginLeft: '3%' }}>
                                                        <Entypo
                                                            name="triangle-up"
                                                            size={switchMerchant ? 9 : 14}
                                                            color={
                                                                currSummarySort ===
                                                                    CATALOG_SORT_FIELD_TYPE.CATALOG_NAME_ASC
                                                                    ? Colors.secondaryColor
                                                                    : Colors.descriptionColor
                                                            }></Entypo>

                                                        <Entypo
                                                            name="triangle-down"
                                                            size={switchMerchant ? 9 : 14}
                                                            color={
                                                                currSummarySort ===
                                                                    CATALOG_SORT_FIELD_TYPE.CATALOG_NAME_DESC
                                                                    ? Colors.secondaryColor
                                                                    : Colors.descriptionColor
                                                            }></Entypo>
                                                    </View>
                                                </View>
                                                <View
                                                    style={{
                                                        flexDirection: 'row',
                                                        width: '20%',
                                                        borderRightWidth: 0,
                                                        borderRightColor: 'lightgrey',
                                                        alignItems: 'center',
                                                        justifyContent: 'flex-start',
                                                        marginLeft: 0,
                                                    }}>
                                                    <View style={{ flexDirection: 'column' }}>
                                                        <Text
                                                            numberOfLines={1}
                                                            style={{
                                                                paddingLeft: 5,
                                                                fontSize: switchMerchant ? 10 : 14,
                                                                fontFamily: 'NunitoSans-Bold',
                                                                color: 'black',
                                                                fontWeight: '600',
                                                            }}>
                                                            Linked Products
                                                        </Text>
                                                    </View>
                                                </View>
                                                <View
                                                    style={{
                                                        flexDirection: 'row',
                                                        width: '15%',
                                                        borderRightWidth: 0,
                                                        borderRightColor: 'lightgrey',
                                                        alignItems: 'center',
                                                        justifyContent: 'flex-start',
                                                        marginLeft: 0,
                                                    }}>
                                                    <View style={{ flexDirection: 'column' }}>
                                                        <Text
                                                            numberOfLines={1}
                                                            style={{
                                                                paddingLeft: 5,
                                                                fontSize: switchMerchant ? 10 : 14,
                                                                fontFamily: 'NunitoSans-Bold',
                                                                color: 'black',
                                                                fontWeight: '600',
                                                            }}>
                                                            Status
                                                        </Text>
                                                    </View>
                                                </View>
                                                <View
                                                    style={{
                                                        flexDirection: 'row',
                                                        width: '15%',
                                                        borderRightWidth: 0,
                                                        borderRightColor: 'lightgrey',
                                                        alignItems: 'center',
                                                        justifyContent: 'flex-start',
                                                        marginLeft: 10,
                                                    }}>
                                                    <View style={{ flexDirection: 'column' }}>

                                                    </View>
                                                </View>
                                            </View>
                                            {list1 ? (
                                                // <View style={{ borderTopWidth: 1 }}>
                                                (<FlatList
                                                    ////THIS IS FOR SEARCH////
                                                    showsVerticalScrollIndicator={false}
                                                    data={sortCatalog(
                                                        outletCatalog,
                                                        currSummarySort,
                                                    ).filter((item, i) => {
                                                        const searchLowerCase = search.toLowerCase();

                                                        if (
                                                            item.catalogName.toLowerCase().includes(searchLowerCase)
                                                        ) {
                                                            return true;
                                                        }
                                                    })}
                                                    renderItem={renderItem}
                                                    keyExtractor={(item, index) => String(index)}
                                                    style={{ marginTop: 0 }}
                                                // initialNumToRender={4}
                                                />)
                                            ) : // </View>

                                                null}
                                        </View>
                                    </View>
                                </View>
                                <View
                                    style={{
                                        position: 'relative',
                                        //left: 160,
                                        flexDirection: 'row',
                                        marginTop: 10,
                                        width: switchMerchant
                                            ? windowWidth * 0.8
                                            : windowWidth * 0.87,
                                        alignItems: 'center',
                                        alignSelf: 'center',
                                        justifyContent: 'flex-end',
                                        top:
                                            Platform.OS == 'ios'
                                                ? pushPagingToTop && keyboardHeight > 0
                                                    ? -keyboardHeight * 1
                                                    : 0
                                                : 0,
                                        // backgroundColor: pushPagingToTop && keyboardHeight > 0 ? Colors.highlightColor : null,
                                        // borderWidth: pushPagingToTop && keyboardHeight > 0 ? 1 : 0,
                                        // borderColor: pushPagingToTop && keyboardHeight > 0 ? '#E5E5E5' : null,
                                        borderRadius: pushPagingToTop && keyboardHeight > 0 ? 8 : 0,
                                        paddingHorizontal:
                                            pushPagingToTop && keyboardHeight > 0 ? 10 : 0,
                                        // shadowOffset: {
                                        //   width: 0,
                                        //   height: 1,
                                        // },
                                        // shadowOpacity: 0.22,
                                        // shadowRadius: 3.22,
                                        // elevation: 1,
                                        marginBottom: switchMerchant
                                            ? windowHeight * 0.1
                                            : 10,
                                    }}>
                                    <Text
                                        style={{
                                            fontSize: switchMerchant ? 10 : 14,
                                            fontFamily: 'NunitoSans-Bold',
                                            marginRight: '1%',
                                        }}>
                                        Page
                                    </Text>
                                    <View
                                        style={{
                                            width: switchMerchant ? 65 : 70,
                                            height: switchMerchant ? 20 : 35,
                                            backgroundColor: Colors.whiteColor,
                                            borderRadius: 10,
                                            justifyContent: 'center',
                                            paddingHorizontal: 22,
                                            borderWidth: 1,
                                            borderColor: '#E5E5E5',
                                        }}>
                                        {console.log('currentPage')}
                                        {console.log(currentPage)}

                                        <TextInput
                                            onChangeText={(text) => {
                                                var currentPageTemp =
                                                    text.length > 0 ? parseInt(text) : 1;

                                                setCurrentPage(
                                                    currentPageTemp > pageCount
                                                        ? pageCount
                                                        : currentPageTemp < 1
                                                            ? 1
                                                            : currentPageTemp,
                                                );
                                            }}
                                            placeholder={currentPage.toString()}
                                            placeholderStyle={{
                                                fontSize: switchMerchant ? 10 : 14,
                                                fontFamily: 'NunitoSans-Regular',
                                            }}
                                            placeholderTextColor={Platform.select({ ios: '#a9a9a9' })}
                                            style={{
                                                color: 'black',
                                                fontSize: switchMerchant ? 10 : 14,
                                                fontFamily: 'NunitoSans-Regular',
                                                marginTop: Platform.OS === 'ios' ? 0 : -15,
                                                marginBottom: Platform.OS === 'ios' ? 0 : -15,
                                                textAlign: 'center',
                                                width: '100%',
                                            }}
                                            value={currentPage.toString()}
                                            defaultValue={currentPage.toString()}
                                            keyboardType={'numeric'}
                                            onFocus={() => {
                                                setPushPagingToTop(true);
                                            }}
                                        />
                                    </View>
                                    <Text
                                        style={{
                                            fontSize: switchMerchant ? 10 : 14,
                                            fontFamily: 'NunitoSans-Bold',
                                            marginLeft: '1%',
                                            marginRight: '1%',
                                        }}>
                                        of {pageCount}
                                    </Text>
                                    <TouchableOpacity
                                        style={{
                                            width: switchMerchant ? 30 : 45,
                                            height: switchMerchant ? 20 : 28,
                                            backgroundColor: Colors.primaryColor,
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                        }}
                                        onPress={() => {
                                            prevPage();
                                        }}>
                                        <MaterialIcons
                                            name="keyboard-arrow-left"
                                            size={switchMerchant ? 20 : 25}
                                            style={{ color: Colors.whiteColor }}
                                        />
                                    </TouchableOpacity>
                                    <TouchableOpacity
                                        style={{
                                            width: switchMerchant ? 30 : 45,
                                            height: switchMerchant ? 20 : 28,
                                            backgroundColor: Colors.primaryColor,
                                            alignItems: 'center',
                                            justifyContent: 'center',
                                        }}
                                        onPress={() => {
                                            nextPage();
                                        }}>
                                        <MaterialIcons
                                            name="keyboard-arrow-right"
                                            size={switchMerchant ? 20 : 25}
                                            style={{ color: Colors.whiteColor }}
                                        />
                                    </TouchableOpacity>
                                </View>
                            </View>
                        ) : null}

                        {/* edit field page */}
                        {catalogSection === CATALOG_SECTION.EDIT_CATALOG ? (
                            <View style={{ marginTop: 15 }}>
                                <View style={{ flexDirection: 'row', justifyContent: 'space-between', width: windowWidth * 0.87, alignSelf: 'center', alignItems: 'center', }}>
                                    <TouchableOpacity
                                        style={
                                            switchMerchant
                                                ? {
                                                    // marginLeft: 25,
                                                    flexDirection: 'row',
                                                    //alignContent: 'center',
                                                    //alignItems: 'center',
                                                    // marginHorizontal: 30,
                                                    //alignItems: 'center'
                                                }
                                                : {
                                                    // marginLeft: 20,
                                                    flexDirection: 'row',
                                                    // marginHorizontal: 30,
                                                    alignItems: 'center',
                                                    right: 5,
                                                }
                                        }
                                        onPress={() => {
                                            setCatalogSection(CATALOG_SECTION.CATALOG_LIST);
                                        }}>
                                        <Icon
                                            name="chevron-left"
                                            size={switchMerchant ? 20 : 30}
                                            color={Colors.primaryColor}
                                        />
                                        <Text
                                            style={{
                                                fontFamily: 'NunitoSans-Bold',
                                                fontSize: switchMerchant ? 14 : 17,
                                                color: Colors.primaryColor,
                                                bottom: switchMerchant
                                                    ? 0
                                                    : Platform.OS === 'android'
                                                        ? 1
                                                        : 0,
                                            }}>
                                            Back
                                        </Text>
                                    </TouchableOpacity>

                                    <TouchableOpacity
                                        style={{
                                            justifyContent: 'center',
                                            flexDirection: 'row',
                                            borderWidth: 1,
                                            borderColor: Colors.primaryColor,
                                            backgroundColor: '#4E9F7D',
                                            borderRadius: 5,
                                            // width: switchMerchant ? 80 : 150,
                                            paddingHorizontal: 10,
                                            height: switchMerchant ? 30 : 40,
                                            alignItems: 'center',
                                            shadowOffset: {
                                                width: 0,
                                                height: 2,
                                            },
                                            shadowOpacity: 0.22,
                                            shadowRadius: 3.22,
                                            elevation: 1,
                                            zIndex: -1,
                                        }}
                                        disabled={isLoading}
                                        onPress={() => {
                                            setSearch('');
                                            setCatalogSection(CATALOG_SECTION.ADD_CATALOG);
                                        }}>
                                        <View
                                            style={{
                                                flexDirection: 'row',
                                                justifyContent: 'center',
                                            }}>
                                            {isLoading ? (
                                                <ActivityIndicator
                                                    color={Colors.whiteColor}
                                                    size={'small'}
                                                />
                                            ) : (
                                                <Text
                                                    style={{
                                                        color: Colors.whiteColor,
                                                        //marginLeft: 5,
                                                        fontSize: switchMerchant ? 10 : 16,
                                                        fontFamily: 'NunitoSans-Bold',
                                                    }}>
                                                    EDIT CATALOG
                                                </Text>
                                            )}
                                        </View>
                                    </TouchableOpacity>
                                </View>
                                <View
                                    style={[
                                        switchMerchant
                                            ? [styles.list1, { marginTop: 10, padding: 20 }]
                                            : [
                                                styles.list,
                                                { marginTop: 15, padding: 20, marginBottom: 5, },
                                            ],
                                        {
                                            width: Dimensions.get('window').width * 0.87,
                                            height: Dimensions.get('window').height * 0.75,
                                        }
                                    ]}>
                                    <View
                                        style={{
                                            justifyContent: 'space-between',
                                            flexDirection: 'row',
                                            alignItems: 'center',
                                        }}>
                                        <Text
                                            style={{
                                                fontWeight: '500',
                                                marginRight: 10,
                                                fontFamily: 'NunitoSans-Bold',
                                                fontSize: switchMerchant ? 20 : 40,
                                            }}>
                                            {catalogName}
                                        </Text>
                                        {/* <Text
                                            style={{
                                                alignSelf: 'center',
                                                // marginTop: 30,
                                                fontSize: switchMerchant ? 20 : 40,
                                                fontWeight: 'bold',
                                            }}>
                                            {selectedCatalogEdit
                                                ? 'Edit Catalog Linked Item'
                                                : 'Add Catalog'}
                                        </Text> */}

                                        {/* <TouchableOpacity
                                            style={{
                                                position: 'relative',
                                                right: 10,
                                                justifyContent: 'center',
                                                flexDirection: 'row',
                                                borderWidth: 1,
                                                borderColor: Colors.primaryColor,
                                                backgroundColor: '#4E9F7D',
                                                borderRadius: 5,
                                                width: switchMerchant ? 80 : 120,
                                                paddingHorizontal: 10,
                                                height: switchMerchant ? 30 : 40,
                                                alignItems: 'center',
                                                shadowOffset: {
                                                    width: 0,
                                                    height: 2,
                                                },
                                                shadowOpacity: 0.22,
                                                shadowRadius: 3.22,
                                                elevation: 1,
                                                zIndex: -1,
                                            }}
                                            disabled={isLoading}
                                            onPress={() => {
                                                if (isEdit) {
                                                    setIsEdit(false);
                                                    // createCatalog();
                                                } else {
                                                    setIsEdit(true);
                                                }
                                            }}>
                                            <View
                                                style={{
                                                    flexDirection: 'row',
                                                    justifyContent: 'center',
                                                }}>
                                                {isLoading ? (
                                                    <ActivityIndicator
                                                        color={Colors.whiteColor}
                                                        size={'small'}
                                                    />
                                                ) : (
                                                    <Text
                                                        style={{
                                                            color: Colors.whiteColor,
                                                            //marginLeft: 5,
                                                            fontSize: switchMerchant ? 10 : 16,
                                                            fontFamily: 'NunitoSans-Bold',
                                                        }}>
                                                        {isEdit ? 'UPDATE' : 'EDIT'}
                                                    </Text>
                                                )}
                                            </View>
                                        </TouchableOpacity> */}
                                    </View>

                                    <View
                                        style={{
                                            alignItems: 'center',
                                            marginTop: switchMerchant ? 10 : 15,
                                        }}>
                                        <View
                                            style={{
                                                width: windowWidth * 0.85,
                                            }}>
                                            <View
                                                style={[
                                                    styles.modalView,
                                                    {
                                                        //height: windowWidth * 0.3,
                                                        // top: Platform.OS === 'ios' && keyboardHeight > 0 ? -keyboardHeight * 0.3 : 0,
                                                        backgroundColor: 'transparent',
                                                        padding: 20,
                                                    },
                                                ]}>

                                                <View style={{ flexDirection: "row", alignItems: 'center', marginBottom: 10, }}>
                                                    <Text
                                                        style={{
                                                            fontWeight: '500',
                                                            marginRight: 10,
                                                            fontFamily: 'NunitoSans-Bold',
                                                            fontSize: switchMerchant ? 10 : 14,
                                                        }}>
                                                        Item List:
                                                    </Text>
                                                    <View
                                                        style={{
                                                            width: switchMerchant ? 200 : 250,
                                                            height: switchMerchant ? 35 : 40,
                                                            backgroundColor: 'white',
                                                            borderRadius: 5,
                                                            flexDirection: 'row',
                                                            alignItems: 'center',
                                                            shadowColor: '#000',
                                                            shadowOffset: {
                                                                width: 0,
                                                                height: 2,
                                                            },
                                                            shadowOpacity: 0.22,
                                                            shadowRadius: 3.22,
                                                            elevation: 3,
                                                            borderWidth: 1,
                                                            borderColor: '#E5E5E5',
                                                            //marginTop: 10,
                                                        }}>
                                                        <Icon
                                                            name="search"
                                                            size={switchMerchant ? 10 : 18}
                                                            color={Colors.primaryColor}
                                                            style={{ marginLeft: 15 }}
                                                        />
                                                        <View style={{ flex: 4 }}>
                                                            <TextInput
                                                                underlineColorAndroid={Colors.whiteColor}
                                                                style={{
                                                                    width: switchMerchant ? 160 : 220,
                                                                    fontSize: switchMerchant ? 10 : 16,
                                                                    fontFamily: 'NunitoSans-Regular',
                                                                    paddingLeft: 5,
                                                                    height: 45,
                                                                }}
                                                                placeholderTextColor={Platform.select({
                                                                    ios: '#a9a9a9',
                                                                })}
                                                                clearButtonMode="while-editing"
                                                                placeholder=" Search"
                                                                onChangeText={(text) => {
                                                                    setSearchItem(text);
                                                                }}
                                                                defaultValue={searchItem}
                                                            />
                                                        </View>
                                                    </View>
                                                </View>

                                                <View
                                                    style={{
                                                        borderRadius: 5,
                                                        paddingVertical: 3,
                                                        paddingLeft: 0,
                                                        marginVertical: 5,
                                                        borderColor: '#E5E5E5',
                                                        borderWidth: 1,
                                                        // paddingTop: 10,
                                                        height: windowHeight * 0.45,
                                                    }}>
                                                    <View style={{ paddingHorizontal: 5, paddingVertical: 10, flexDirection: "row", marginBottom: 5, width: '100%', borderBottomWidth: 1, borderBottomColor: Colors.fieldtBgColor }}>
                                                        <Text style={{ width: '10%', paddingLeft: 10, }} />
                                                        <View style={{ width: '11%', }}>
                                                            <Text
                                                                style={{
                                                                    fontWeight: '500',
                                                                    marginRight: 10,
                                                                    fontFamily: 'NunitoSans-Bold',
                                                                    fontSize: switchMerchant ? 10 : 14,
                                                                }}>
                                                                Sku:
                                                            </Text>
                                                        </View>
                                                        <View style={{ width: '55%', }}>
                                                            <Text
                                                                style={{
                                                                    fontWeight: '500',
                                                                    marginRight: 10,
                                                                    fontFamily: 'NunitoSans-Bold',
                                                                    fontSize: switchMerchant ? 10 : 14,
                                                                }}>
                                                                Name:
                                                            </Text>
                                                        </View>
                                                        <View style={{ width: '12%', }}>
                                                            <Text
                                                                style={{
                                                                    fontWeight: '500',
                                                                    marginRight: 10,
                                                                    fontFamily: 'NunitoSans-Bold',
                                                                    fontSize: switchMerchant ? 10 : 14,
                                                                }}>
                                                                Price:
                                                            </Text>
                                                        </View>
                                                        <Text style={{ width: '12%', }} />

                                                    </View>
                                                    {transformedItems.length > 0 ?
                                                        <FlatList
                                                            data={transformedItems.filter((item, i) => {
                                                                const searchLowerCase = searchItem.toLowerCase();

                                                                if (
                                                                    item.n.toLowerCase().includes(searchLowerCase)
                                                                ) {
                                                                    return true;
                                                                }
                                                            })}
                                                            nestedScrollEnabled
                                                            renderItem={renderLinkedItems}
                                                            keyExtractor={(item, index) => item.sku}
                                                        />
                                                        :
                                                        <View
                                                            style={{ height: windowHeight * 0.4 }}>
                                                            <View
                                                                style={{
                                                                    alignItems: 'center',
                                                                    justifyContent: 'center',
                                                                    height: '100%',
                                                                }}>
                                                                <Text style={{ color: Colors.descriptionColor }}>
                                                                    - No Data Available -
                                                                </Text>
                                                            </View>
                                                        </View>
                                                    }
                                                </View>
                                            </View>
                                        </View>
                                    </View>
                                </View>
                            </View>
                        ) : null}

                        {/* **********************************create/update Page*********************************** */}
                        {catalogSection === CATALOG_SECTION.ADD_CATALOG ? (
                            <View style={{ marginTop: 15 }}>
                                <TouchableOpacity
                                    style={
                                        switchMerchant
                                            ? {
                                                marginLeft: 25,
                                                flexDirection: 'row',
                                                //alignContent: 'center',
                                                //alignItems: 'center',
                                                marginHorizontal: 30,
                                                //alignItems: 'center'
                                            }
                                            : {
                                                marginLeft: 20,
                                                flexDirection: 'row',
                                                marginHorizontal: 30,
                                                alignItems: 'center',
                                            }
                                    }
                                    onPress={() => {
                                        setCatalogSection(selectedCatalogEdit ? CATALOG_SECTION.EDIT_CATALOG : CATALOG_SECTION.CATALOG_LIST);
                                    }}>
                                    <Icon
                                        name="chevron-left"
                                        size={switchMerchant ? 20 : 30}
                                        color={Colors.primaryColor}
                                    />
                                    <Text
                                        style={{
                                            fontFamily: 'NunitoSans-Bold',
                                            fontSize: switchMerchant ? 14 : 17,
                                            color: Colors.primaryColor,
                                            bottom: switchMerchant
                                                ? 0
                                                : Platform.OS === 'android'
                                                    ? 1
                                                    : 0,
                                        }}>
                                        Back
                                    </Text>
                                </TouchableOpacity>
                                <View
                                    style={[
                                        switchMerchant
                                            ? [styles.list1, { marginTop: 10, padding: 20 }]
                                            : [
                                                styles.list,
                                                { marginTop: 15, padding: 20, marginBottom: 5, },
                                            ],
                                        {
                                            width: Dimensions.get('window').width * 0.87,
                                            height: Dimensions.get('window').height * 0.75,
                                        }
                                    ]}>
                                    <View
                                        style={{
                                            justifyContent: 'space-between',
                                            flexDirection: 'row',
                                            alignItems: 'center',
                                        }}>
                                        <View></View>
                                        <Text
                                            style={{
                                                alignSelf: 'center',
                                                // marginTop: 30,
                                                fontSize: switchMerchant ? 20 : 40,
                                                fontWeight: 'bold',
                                            }}>
                                            {selectedCatalogEdit
                                                ? 'Edit Catalog'
                                                : 'Add Catalog'}
                                        </Text>

                                        <TouchableOpacity
                                            style={{
                                                position: 'relative',
                                                right: 10,
                                                justifyContent: 'center',
                                                flexDirection: 'row',
                                                borderWidth: 1,
                                                borderColor: Colors.primaryColor,
                                                backgroundColor: '#4E9F7D',
                                                borderRadius: 5,
                                                width: switchMerchant ? 80 : 120,
                                                paddingHorizontal: 10,
                                                height: switchMerchant ? 30 : 40,
                                                alignItems: 'center',
                                                shadowOffset: {
                                                    width: 0,
                                                    height: 2,
                                                },
                                                shadowOpacity: 0.22,
                                                shadowRadius: 3.22,
                                                elevation: 1,
                                                zIndex: -1,
                                            }}
                                            disabled={isLoading}
                                            onPress={() => {
                                                if (selectedCatalogEdit) {
                                                    updateCatalog();
                                                } else {
                                                    createCatalog();
                                                }
                                            }}>
                                            <View
                                                style={{
                                                    flexDirection: 'row',
                                                    justifyContent: 'center',
                                                }}>
                                                {isLoading ? (
                                                    <ActivityIndicator
                                                        color={Colors.whiteColor}
                                                        size={'small'}
                                                    />
                                                ) : (
                                                    <Text
                                                        style={{
                                                            color: Colors.whiteColor,
                                                            //marginLeft: 5,
                                                            fontSize: switchMerchant ? 10 : 16,
                                                            fontFamily: 'NunitoSans-Bold',
                                                        }}>
                                                        {selectedCatalogEdit ? 'UPDATE' : 'CREATE'}
                                                    </Text>
                                                )}
                                            </View>
                                        </TouchableOpacity>
                                    </View>

                                    <View
                                        style={{
                                            alignItems: 'center',
                                            marginTop: switchMerchant ? 10 : 15,
                                        }}>
                                        <View
                                            style={{
                                                width: windowWidth * 0.85,
                                            }}>
                                            <View
                                                style={[
                                                    styles.modalView,
                                                    {
                                                        //height: windowWidth * 0.3,
                                                        // top: Platform.OS === 'ios' && keyboardHeight > 0 ? -keyboardHeight * 0.3 : 0,
                                                        backgroundColor: 'transparent',
                                                        padding: 20,
                                                    },
                                                ]}>

                                                <View
                                                    style={{
                                                        flexDirection: 'row',
                                                        alignItems: 'center',
                                                        marginBottom: 15,
                                                    }}>
                                                    <Text
                                                        style={{
                                                            fontWeight: '500',
                                                            marginRight: 10,
                                                            fontFamily: 'NunitoSans-Bold',
                                                            fontSize: switchMerchant ? 10 : 14,
                                                        }}>
                                                        Catalog Name:
                                                    </Text>
                                                    <TextInput
                                                        placeholder={'Catalog Name'}
                                                        style={{
                                                            backgroundColor: Colors.fieldtBgColor,
                                                            width: switchMerchant
                                                                ? windowWidth * 0.17
                                                                : windowWidth * 0.2,
                                                            height: switchMerchant ? 35 : 40,
                                                            borderRadius: 5,
                                                            padding: 5,
                                                            marginVertical: 5,
                                                            borderWidth: 1,
                                                            borderColor: '#E5E5E5',
                                                            paddingLeft: 10,
                                                            fontFamily: 'NunitoSans-Regular',
                                                            fontSize: switchMerchant ? 10 : 14,
                                                        }}
                                                        placeholderTextColor={Platform.select({
                                                            ios: '#a9a9a9',
                                                        })}
                                                        //iOS
                                                        // clearTextOnFocus={true}
                                                        selectTextOnFocus
                                                        //////////////////////////////////////////////
                                                        //Android
                                                        // onFocus={() => {
                                                        //     setTemp(catalogName)
                                                        //     setCatalogName('');
                                                        // }}
                                                        // ///////////////////////////////////////////////
                                                        // //When textinput is not selected
                                                        // onEndEditing={() => {
                                                        //     if (catalogName == '') {
                                                        //         setCatalogName(temp);
                                                        //     }
                                                        // }}
                                                        //////////////////////////////////////////////
                                                        onChangeText={(text) => {
                                                            setCatalogName(text);
                                                        }}
                                                        defaultValue={catalogName}
                                                    />
                                                </View>
                                                <View style={{ flexDirection: "row", alignItems: 'center', marginBottom: 10, }}>
                                                    <Text
                                                        style={{
                                                            fontWeight: '500',
                                                            marginRight: 10,
                                                            fontFamily: 'NunitoSans-Bold',
                                                            fontSize: switchMerchant ? 10 : 14,
                                                        }}>
                                                        Item List:
                                                    </Text>
                                                    <View
                                                        style={{
                                                            width: switchMerchant ? 200 : 250,
                                                            height: switchMerchant ? 35 : 40,
                                                            backgroundColor: 'white',
                                                            borderRadius: 5,
                                                            flexDirection: 'row',
                                                            alignItems: 'center',
                                                            shadowColor: '#000',
                                                            shadowOffset: {
                                                                width: 0,
                                                                height: 2,
                                                            },
                                                            shadowOpacity: 0.22,
                                                            shadowRadius: 3.22,
                                                            elevation: 3,
                                                            borderWidth: 1,
                                                            borderColor: '#E5E5E5',
                                                            //marginTop: 10,
                                                        }}>
                                                        <Icon
                                                            name="search"
                                                            size={switchMerchant ? 10 : 18}
                                                            color={Colors.primaryColor}
                                                            style={{ marginLeft: 15 }}
                                                        />
                                                        <View style={{ flex: 4 }}>
                                                            <TextInput
                                                                underlineColorAndroid={Colors.whiteColor}
                                                                style={{
                                                                    width: switchMerchant ? 160 : 220,
                                                                    fontSize: switchMerchant ? 10 : 16,
                                                                    fontFamily: 'NunitoSans-Regular',
                                                                    paddingLeft: 5,
                                                                    height: 45,
                                                                }}
                                                                placeholderTextColor={Platform.select({
                                                                    ios: '#a9a9a9',
                                                                })}
                                                                clearButtonMode="while-editing"
                                                                placeholder=" Search"
                                                                onChangeText={(text) => {
                                                                    setSearchItem(text);
                                                                }}
                                                                defaultValue={searchItem}
                                                            />
                                                        </View>
                                                    </View>
                                                </View>

                                                <View
                                                    style={{
                                                        borderRadius: 5,
                                                        paddingVertical: 3,
                                                        paddingLeft: 0,
                                                        marginVertical: 5,
                                                        borderColor: '#E5E5E5',
                                                        borderWidth: 1,
                                                        // paddingTop: 10,
                                                        height: windowHeight * 0.45,
                                                    }}>
                                                    <View style={{ paddingHorizontal: 5, paddingVertical: 10, flexDirection: "row", marginBottom: 5, width: '100%', borderBottomWidth: 1, borderBottomColor: Colors.fieldtBgColor }}>
                                                        <Text style={{ width: '10%', paddingLeft: 10, }} />
                                                        <View style={{ width: '11%', }}>
                                                            <Text
                                                                style={{
                                                                    fontWeight: '500',
                                                                    marginRight: 10,
                                                                    fontFamily: 'NunitoSans-Bold',
                                                                    fontSize: switchMerchant ? 10 : 14,
                                                                }}>
                                                                Sku:
                                                            </Text>
                                                        </View>
                                                        <View style={{ width: '55%', }}>
                                                            <Text
                                                                style={{
                                                                    fontWeight: '500',
                                                                    marginRight: 10,
                                                                    fontFamily: 'NunitoSans-Bold',
                                                                    fontSize: switchMerchant ? 10 : 14,
                                                                }}>
                                                                Name:
                                                            </Text>
                                                        </View>
                                                        <View style={{ width: '12%', }}>
                                                            <Text
                                                                style={{
                                                                    fontWeight: '500',
                                                                    marginRight: 10,
                                                                    fontFamily: 'NunitoSans-Bold',
                                                                    fontSize: switchMerchant ? 10 : 14,
                                                                }}>
                                                                Price:
                                                            </Text>
                                                        </View>
                                                        <Text style={{ width: '12%', }} />

                                                    </View>
                                                    <FlatList
                                                        data={outletItems.filter((item, i) => {
                                                            const searchLowerCase = searchItem.toLowerCase();

                                                            if (
                                                                item.name.toLowerCase().includes(searchLowerCase)
                                                            ) {
                                                                return true;
                                                            }
                                                        })}
                                                        nestedScrollEnabled
                                                        renderItem={renderOutletItems}
                                                        keyExtractor={(item, index) => String(index)}
                                                    />
                                                </View>
                                            </View>
                                        </View>
                                    </View>
                                </View>
                            </View>
                        ) : null}
                    </KeyboardAvoidingView>
                </ScrollView>
            </ScrollView>
        </View>
    );
};

const styles = StyleSheet.create({
    container: {
        flex: 1,
        backgroundColor: Colors.highlightColor,
        flexDirection: 'row',
    },
    list: {
        backgroundColor: Colors.whiteColor,
        width: Dimensions.get('window').width * 0.87,
        height: Dimensions.get('window').height * 0.65,
        //marginTop: 30,
        marginHorizontal: 30,
        //alignSelf: 'center',
        borderRadius: 5,
        shadowOpacity: 0,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,
    },
    list1: {
        backgroundColor: Colors.whiteColor,
        width: Dimensions.get('window').width * 0.8,
        height: Dimensions.get('window').height * 0.7,
        //marginTop: 30,
        marginHorizontal: 30,
        marginBottom: 60,
        //alignSelf: 'center',
        borderRadius: 5,
        shadowOpacity: 0,
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 3,
    },
    listItem: {
        marginLeft: 20,
        color: Colors.descriptionColor,
        fontSize: 16,
    },
    sidebar: {
        //width: windowWidth * Styles.sideBarWidth,
        // shadowColor: '#000',
        // shadowOffset: {
        //   width: 0,
        //   height: 8,
        // },
        // shadowOpacity: 0.44,
        // shadowRadius: 10.32,

        // elevation: 16,
    },
    content: {
        padding: 16,
    },
    textInput: {
        height: 50,
        paddingHorizontal: 20,
        backgroundColor: Colors.fieldtBgColor,
        borderRadius: 5,
        marginBottom: 20,
        width: 300,
    },
    headerLeftStyle: {
        width: Dimensions.get('window').width * 0.17,
        justifyContent: 'center',
        alignItems: 'center',
    },
    publishButtonStyle: {
        justifyContent: 'center',
        flexDirection: 'row',
        borderWidth: 1,
        borderColor: Colors.primaryColor,
        backgroundColor: '#4E9F7D',
        borderRadius: 5,
        //width: 160,
        paddingHorizontal: 10,
        marginRight: 10,
        height: 40,
        alignItems: 'center',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.22,
        shadowRadius: 3.22,
        elevation: 1,
        zIndex: -1,
        marginLeft: 15,
    }
});

export default Catalog;
